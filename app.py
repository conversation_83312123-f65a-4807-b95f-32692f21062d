"""
Main application file for Maximo OAuth.
This file sets up the Flask application and routes.
"""
from flask import Flask, render_template, request, redirect, url_for, session, flash, jsonify, send_from_directory
import os
import secrets
import threading
import time
import datetime
import json
import requests
import json
from dotenv import load_dotenv
from backend.auth import MaximoTokenManager
from backend.api import init_api, init_sync_routes
from backend.services import EnhancedProfileService, EnhancedWorkOrderService
from backend.services.site_access_service import SiteAccessService
from backend.services.labor_search_service import LaborSearchService
from backend.services.labor_request_service import LaborRequestService
from backend.services.labor_deletion_service import LaborDeletionService

import logging
import json

# Load environment variables from .env file
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def update_env_file(key, value, env_file_path='.env'):
    """Update a key-value pair in the .env file."""
    # Read current .env file
    env_lines = []
    key_found = False

    if os.path.exists(env_file_path):
        with open(env_file_path, 'r') as f:
            env_lines = f.readlines()

    # Update or add the key
    for i, line in enumerate(env_lines):
        if line.strip().startswith(f'{key}='):
            env_lines[i] = f'{key}={value}\n'
            key_found = True
            break

    # If key not found, add it
    if not key_found:
        env_lines.append(f'{key}={value}\n')

    # Write back to file
    with open(env_file_path, 'w') as f:
        f.writelines(env_lines)

    logger.info(f"Updated .env file: {key}={value}")

# Create Flask app with custom template and static folders
app = Flask(__name__,
            template_folder='frontend/templates',
            static_folder='frontend/static')
app.secret_key = secrets.token_hex(16)

# Get Maximo URL from environment variable with fallback
DEFAULT_MAXIMO_URL = os.getenv('MAXIMO_BASE_URL', "https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo")

# Initialize token manager globally for reuse
token_manager = MaximoTokenManager(DEFAULT_MAXIMO_URL)

# Initialize enhanced profile service
enhanced_profile_service = EnhancedProfileService(token_manager)

# Initialize enhanced work order service
enhanced_workorder_service = EnhancedWorkOrderService(token_manager, enhanced_profile_service)

# Initialize labor services
labor_search_service = LaborSearchService(token_manager)
labor_request_service = LaborRequestService(token_manager, enhanced_profile_service)

# Initialize asset management service
from backend.services.asset_management_service import AssetManagementService
asset_management_service = AssetManagementService(token_manager)

# Initialize asset creation service (will be updated later with related records service)
from backend.services.asset_creation_service import AssetCreationService
from backend.services.ai_workorder_analysis_service import AIWorkOrderAnalysisService
from backend.services.qr_related_records_service import QRRelatedRecordsService
asset_creation_service = None  # Will be initialized later
ai_analysis_service = AIWorkOrderAnalysisService(token_manager)



# Initialize API routes with the token manager
# This needs to be done after the app is created
init_api(app, token_manager)

# Initialize sync routes
init_sync_routes(app)

# Background authentication flag
background_auth_in_progress = False
background_auth_result = None

# Signature configuration storage with persistent file-based storage
import os
import json

SIGNATURE_CONFIG_FILE = 'signature_config.json'
ISSUE_MATERIAL_CONFIG_FILE = 'issue_material_config.json'

def load_signature_config():
    """Load signature configuration from file"""
    default_config = {
        'enabled': False,
        'statuses': [],
        'scope': ['parent', 'task']
    }

    try:
        if os.path.exists(SIGNATURE_CONFIG_FILE):
            with open(SIGNATURE_CONFIG_FILE, 'r') as f:
                config = json.load(f)
                logger.info(f"📝 SIGNATURE CONFIG: Loaded from file - Enabled: {config.get('enabled')}, Statuses: {config.get('statuses')}")
                return config
        else:
            logger.info(f"📝 SIGNATURE CONFIG: No config file found, using defaults")
            return default_config
    except Exception as e:
        logger.error(f"📝 SIGNATURE CONFIG: Error loading config file: {e}")
        return default_config

def save_signature_config_to_file(config):
    """Save signature configuration to file"""
    try:
        with open(SIGNATURE_CONFIG_FILE, 'w') as f:
            json.dump(config, f, indent=2)
        logger.info(f"📝 SIGNATURE CONFIG: Saved to file - Enabled: {config.get('enabled')}, Statuses: {config.get('statuses')}")
        return True
    except Exception as e:
        logger.error(f"📝 SIGNATURE CONFIG: Error saving config file: {e}")
        return False

def load_issue_material_config():
    """Load issue material configuration from file"""
    default_config = {
        'enabled': True,
        'work_order_statuses': ['WMATL', 'PISSUE'],
        'task_statuses': ['APPR', 'INPRG', 'WMATL', 'PISSUE']
    }

    try:
        if os.path.exists(ISSUE_MATERIAL_CONFIG_FILE):
            with open(ISSUE_MATERIAL_CONFIG_FILE, 'r') as f:
                config = json.load(f)
                logger.info(f"📝 ISSUE MATERIAL CONFIG: Loaded from file - Enabled: {config.get('enabled')}, WO Statuses: {config.get('work_order_statuses')}, Task Statuses: {config.get('task_statuses')}")
                return config
        else:
            logger.info(f"📝 ISSUE MATERIAL CONFIG: No config file found, using defaults")
            return default_config
    except Exception as e:
        logger.error(f"📝 ISSUE MATERIAL CONFIG: Error loading config file: {e}")
        return default_config

def save_issue_material_config_to_file(config):
    """Save issue material configuration to file"""
    try:
        with open(ISSUE_MATERIAL_CONFIG_FILE, 'w') as f:
            json.dump(config, f, indent=2)
        logger.info(f"📝 ISSUE MATERIAL CONFIG: Saved to file - Enabled: {config.get('enabled')}, WO Statuses: {config.get('work_order_statuses')}, Task Statuses: {config.get('task_statuses')}")
        return True
    except Exception as e:
        logger.error(f"📝 ISSUE MATERIAL CONFIG: Error saving config file: {e}")
        return False

# Load configurations from file on startup
signature_config = load_signature_config()
issue_material_config = load_issue_material_config()

@app.route('/')
def index():
    """Render the login page."""
    # Check if we already have a valid session
    if 'username' in session and token_manager.is_logged_in():
        return redirect(url_for('welcome'))

    # Clear any existing invalid session
    session.clear()

    # Get current Maximo URL for the form
    current_url = session.get('maximo_url', DEFAULT_MAXIMO_URL)
    return render_template('login.html', current_maximo_url=current_url)

@app.route('/login', methods=['POST'])
def login():
    """Handle login form submission."""
    global background_auth_in_progress, background_auth_result, token_manager

    username = request.form.get('username')
    password = request.form.get('password')
    maximo_url = request.form.get('maximo_url', '').strip()
    remember_me = request.form.get('remember_me') == 'on'

    if not username or not password:
        flash('Please enter both username and password', 'error')
        return redirect(url_for('index'))

    # If no URL provided, use the current one from session or default
    if not maximo_url:
        maximo_url = session.get('maximo_url', DEFAULT_MAXIMO_URL)

    # Validate URL format
    if not maximo_url.startswith(('http://', 'https://')):
        flash('Maximo URL must start with http:// or https://', 'error')
        return redirect(url_for('index'))

    # Store the URL in session
    session['maximo_url'] = maximo_url

    # If URL changed, create a new token manager and update .env file
    if maximo_url != token_manager.base_url:
        logger.info(f"Maximo URL changed from {token_manager.base_url} to {maximo_url}")

        # Update .env file with new URL
        try:
            update_env_file('MAXIMO_BASE_URL', maximo_url)
            flash(f'Maximo URL updated to: {maximo_url}', 'info')
        except Exception as e:
            logger.warning(f"Failed to update .env file: {e}")

        token_manager = MaximoTokenManager(maximo_url)

        # Re-initialize services with new token manager
        global enhanced_profile_service, enhanced_workorder_service, labor_search_service, labor_request_service
        enhanced_profile_service = EnhancedProfileService(token_manager)
        enhanced_workorder_service = EnhancedWorkOrderService(token_manager, enhanced_profile_service)
        labor_search_service = LaborSearchService(token_manager)
        labor_request_service = LaborRequestService(token_manager, enhanced_profile_service)

    # Start background authentication
    background_auth_in_progress = True
    background_auth_result = None

    def auth_worker():
        global background_auth_in_progress, background_auth_result
        try:
            # Attempt to login
            success = token_manager.login(username, password)
            background_auth_result = {'success': success, 'error': None}
        except Exception as e:
            logger.error(f"Login error: {str(e)}")
            background_auth_result = {'success': False, 'error': str(e)}
        finally:
            background_auth_in_progress = False

    # Start authentication in background
    auth_thread = threading.Thread(target=auth_worker)
    auth_thread.daemon = True
    auth_thread.start()

    # Store username in session
    session['username'] = username
    session['login_started'] = time.time()

    # Redirect to loading page
    return redirect(url_for('auth_status'))

@app.route('/auth-status')
def auth_status():
    """Check authentication status and redirect accordingly."""
    global background_auth_in_progress, background_auth_result

    if 'username' not in session:
        flash('Please login first', 'error')
        return redirect(url_for('index'))

    # If authentication is complete
    if not background_auth_in_progress and background_auth_result:
        if background_auth_result['success']:
            # Clear the result to avoid memory leaks
            result = background_auth_result
            background_auth_result = None

            # Clear any existing profile caches to ensure fresh data (memory + disk)
            try:
                if 'enhanced_profile_service' in globals():
                    enhanced_profile_service.clear_cache('all')
                if 'enhanced_workorder_service' in globals():
                    enhanced_workorder_service.clear_cache('all')

                # Clear ALL disk cache files to prevent cross-user contamination
                import os
                import glob
                cache_patterns = [
                    'cache/profile_*.pkl',
                    'cache/enhanced_profile_*.pkl',
                    'cache/workorder_*.pkl',
                    'cache/sites_*.pkl'
                ]

                for pattern in cache_patterns:
                    for cache_file in glob.glob(pattern):
                        try:
                            os.remove(cache_file)
                            logger.info(f"✅ LOGIN: Removed stale disk cache file: {cache_file}")
                        except Exception as e:
                            logger.warning(f"Could not remove cache file {cache_file}: {e}")

                logger.info("✅ LOGIN: Cleared all caches (memory + disk) for fresh profile data")
            except Exception as e:
                logger.warning(f"Error clearing caches during login: {e}")

            # Try to get fresh user profile (force refresh to avoid stale data)
            try:
                # Force fresh profile retrieval - no cache for login
                user_profile = token_manager.get_user_profile(use_mock=False, use_cache=False, force_refresh=True)
                if user_profile:
                    # Note: The profile data is already cleaned (no spi: prefixes)
                    session['default_site'] = user_profile.get('defaultSite', '')
                    session['insert_site'] = user_profile.get('insertSite', '')
                    session['first_name'] = user_profile.get('firstName', '')
                    session['last_name'] = user_profile.get('lastName', '')

                    logger.info(f"✅ LOGIN: Fresh profile data loaded for user {session['username']}")
                else:
                    logger.error("Failed to fetch fresh user profile during login")
            except Exception as e:
                logger.warning(f"Error getting user profile during login: {e}")

            return redirect(url_for('welcome'))
        else:
            # Authentication failed
            error = background_auth_result.get('error', 'Unknown error')
            background_auth_result = None
            session.clear()
            flash(f'Login failed: {error}', 'error')
            return redirect(url_for('index'))

    # If still in progress, show loading page
    return render_template('loading.html', username=session.get('username'))

@app.route('/api/auth-status')
def api_auth_status():
    """API endpoint to check authentication status."""
    global background_auth_in_progress, background_auth_result

    if 'username' not in session:
        return jsonify({'status': 'error', 'message': 'No login in progress'})

    if background_auth_in_progress:
        # Calculate how long authentication has been running
        start_time = session.get('login_started', time.time())
        elapsed = time.time() - start_time
        return jsonify({
            'status': 'in_progress',
            'elapsed': elapsed,
            'username': session.get('username')
        })

    if background_auth_result:
        if background_auth_result['success']:
            return jsonify({'status': 'success'})
        else:
            error = background_auth_result.get('error', 'Unknown error')
            return jsonify({'status': 'error', 'message': error})

    return jsonify({'status': 'unknown'})

@app.route('/api/session-status')
def api_session_status():
    """API endpoint to check current session status and provide warnings."""
    if 'username' not in session:
        return jsonify({'error': 'Not authenticated', 'authenticated': False}), 401

    try:
        # Check if session is still valid
        if not token_manager.is_logged_in():
            return jsonify({
                'authenticated': False,
                'expired': True,
                'message': 'Session has expired'
            }), 401

        # Calculate remaining session time
        expires_at = getattr(token_manager, 'expires_at', 0)
        current_time = time.time()

        if expires_at > current_time:
            minutes_remaining = int((expires_at - current_time) / 60)

            # Determine warning level
            warning = False
            warning_level = 'info'

            if minutes_remaining <= 5:
                warning = True
                warning_level = 'danger'
            elif minutes_remaining <= 10:
                warning = True
                warning_level = 'warning'
            elif minutes_remaining <= 30:
                warning = True
                warning_level = 'info'

            return jsonify({
                'authenticated': True,
                'expires_at': expires_at,
                'minutes_remaining': minutes_remaining,
                'warning': warning,
                'warning_level': warning_level,
                'username': session.get('username'),
                'login_duration': session.get('login_duration', 0)
            })
        else:
            return jsonify({
                'authenticated': False,
                'expired': True,
                'message': 'Session has expired'
            }), 401

    except Exception as e:
        logger.error(f"Error checking session status: {e}")
        return jsonify({
            'error': 'Error checking session status',
            'authenticated': False
        }), 500

@app.route('/api/test-ai-insight', methods=['POST'])
def test_ai_insight():
    """Test endpoint for AI insights functionality."""
    if 'username' not in session:
        return jsonify({'error': 'Not authenticated'}), 401

    try:
        data = request.get_json() or {}
        insight_type = data.get('type', 'test')

        # Generate a test insight based on current time
        current_hour = datetime.datetime.now().hour

        if insight_type == 'performance':
            insight = {
                'icon': 'fas fa-rocket',
                'content': f'<strong>Performance Test:</strong> System response time is optimal at {current_hour}:00. All services running smoothly.',
                'type': 'performance'
            }
        elif insight_type == 'recommendation':
            insight = {
                'icon': 'fas fa-lightbulb',
                'content': f'<strong>Smart Recommendation:</strong> Based on the time ({current_hour}:00), consider reviewing your work order priorities.',
                'type': 'recommendation'
            }
        else:
            insight = {
                'icon': 'fas fa-info-circle',
                'content': f'<strong>Test Insight:</strong> AI insights system is working correctly. Generated at {current_hour}:00.',
                'type': 'test'
            }

        return jsonify({
            'success': True,
            'insight': insight,
            'timestamp': datetime.datetime.now().isoformat()
        })

    except Exception as e:
        logger.error(f"Error generating test insight: {e}")
        return jsonify({
            'error': 'Error generating test insight',
            'success': False
        }), 500

@app.route('/debug/template-info')
def debug_template_info():
    """Debug endpoint to check template information."""
    if 'username' not in session:
        return jsonify({'error': 'Not authenticated'}), 401

    import os
    template_path = os.path.join(app.template_folder, 'welcome.html')

    try:
        # Get template modification time
        import time
        mod_time = os.path.getmtime(template_path)
        mod_time_str = time.ctime(mod_time)

        # Get template size
        file_size = os.path.getsize(template_path)

        # Check if template exists
        template_exists = os.path.exists(template_path)

        return jsonify({
            'template_path': template_path,
            'template_exists': template_exists,
            'modification_time': mod_time_str,
            'file_size_bytes': file_size,
            'app_template_folder': app.template_folder,
            'current_time': time.ctime(),
            'session_username': session.get('username')
        })

    except Exception as e:
        return jsonify({
            'error': str(e),
            'template_path': template_path
        }), 500

@app.route('/welcome')
def welcome():
    """Render the welcome page."""
    if 'username' not in session:
        flash('Please login first', 'error')
        return redirect(url_for('index'))

    # Verify that we're still logged in
    if not token_manager.is_logged_in():
        flash('Your session has expired. Please login again.', 'warning')
        session.clear()
        return redirect(url_for('index'))

    # Get login time
    login_time = session.get('login_started', time.time())
    login_duration = time.time() - login_time

    import time as time_module

    try:
        return render_template(
            'welcome.html',
            username=session['username'],
            login_duration=login_duration,
            token_expires_at=token_manager.expires_at,
            time=time_module
        )
    except Exception as e:
        logger.error(f"Error rendering welcome page: {e}")
        flash('An error occurred while loading the welcome page. Please try again.', 'error')
        return redirect(url_for('index'))

@app.route('/profile')
def profile():
    """Render the user profile page."""
    if 'username' not in session:
        flash('Please login first', 'error')
        return redirect(url_for('index'))

    # Verify that we're still logged in
    if not token_manager.is_logged_in():
        flash('Your session has expired. Please login again.', 'warning')
        session.clear()
        return redirect(url_for('index'))

    try:
        # Try to refresh the session first
        try:
            # Force a token refresh to ensure we have a valid session
            token_manager._refresh_token()
        except Exception as e:
            logger.warning(f"Error refreshing token: {e}")

        # Get user profile data with lightning-fast caching
        start_time = time.time()
        user_profile = token_manager.get_user_profile(use_mock=False, use_cache=True)
        profile_fetch_time = time.time() - start_time
        logger.info(f"Profile data retrieved in {profile_fetch_time:.3f} seconds")

        # No hardcoded fallback values - must fetch from Maximo API
        if not user_profile:
            logger.error("Failed to fetch user profile from Maximo API - no fallback values allowed")
            flash('Unable to load profile data. Please login again for fresh authentication.', 'error')
            session.clear()
            return redirect(url_for('index'))

        # Get available sites with caching enabled for better performance
        available_sites = token_manager.get_available_sites(use_mock=False, use_cache=True)

        # Ensure we have at least the current default and insert sites in the list
        if available_sites and user_profile:
            # Get current default and insert sites (now using cleaned keys without prefixes)
            default_site = user_profile.get('defaultSite', '')
            insert_site = user_profile.get('insertSite', '')

            # Check if they're in the available sites list
            default_site_exists = any(site.get('siteid') == default_site for site in available_sites)
            insert_site_exists = any(site.get('siteid') == insert_site for site in available_sites)

            # Add them if they're not in the list
            if default_site and not default_site_exists:
                available_sites.append({
                    'siteid': default_site,
                    'description': user_profile.get('defaultSiteDescription', default_site)
                })

            if insert_site and not insert_site_exists:
                available_sites.append({
                    'siteid': insert_site,
                    'description': insert_site
                })

            # Sort the sites by siteid
            available_sites.sort(key=lambda x: x.get('siteid', ''))

        # No hardcoded fallback values - must fetch from Maximo API
        # If profile fetch fails, redirect to login for fresh authentication
        if not user_profile:
            logger.error("Failed to fetch user profile from Maximo API - no fallback values allowed")
            flash('Unable to load profile data. Please login again for fresh authentication.', 'error')
            session.clear()
            return redirect(url_for('index'))

        return render_template(
            'profile.html',
            username=session['username'],
            user_profile=user_profile,
            available_sites=available_sites if available_sites else []
        )
    except Exception as e:
        logger.error(f"Error rendering profile page: {e}")
        flash('An error occurred while loading the profile page. Please try again.', 'error')
        return redirect(url_for('welcome'))

@app.route('/enhanced-profile')
def enhanced_profile():
    """Render the enhanced user profile page with optimized performance."""
    if 'username' not in session:
        flash('Please login first', 'error')
        return redirect(url_for('index'))

    # Verify session using enhanced service (with caching)
    if not enhanced_profile_service.is_session_valid():
        flash('Your session has expired. Please login again.', 'warning')
        session.clear()
        return redirect(url_for('index'))

    try:
        # Record start time for performance comparison
        overall_start_time = time.time()

        # Get session data for fallback
        session_data = {
            'username': session['username'],
            'first_name': session.get('first_name', ''),
            'last_name': session.get('last_name', ''),
            'default_site': session.get('default_site', ''),
            'insert_site': session.get('insert_site', '')
        }

        # Use enhanced service to build complete profile
        user_profile, available_sites = enhanced_profile_service.build_complete_profile(session_data)

        # Get performance statistics
        perf_stats = enhanced_profile_service.get_performance_stats()

        overall_time = time.time() - overall_start_time
        logger.info(f"🚀 ENHANCED PROFILE: Total page load time: {overall_time:.3f}s")
        logger.info(f"📊 ENHANCED STATS: Cache hit rate: {perf_stats['cache_hit_rate']:.1f}%, "
                   f"Avg response: {perf_stats['average_response_time']:.3f}s, "
                   f"Total requests: {perf_stats['total_requests']}")

        return render_template(
            'enhanced_profile.html',
            username=session['username'],
            user_profile=user_profile,
            available_sites=available_sites,
            performance_stats=perf_stats,
            page_load_time=overall_time
        )
    except Exception as e:
        logger.error(f"Error rendering enhanced profile page: {e}")
        flash('An error occurred while loading the enhanced profile page. Please try again.', 'error')
        return redirect(url_for('welcome'))

@app.route('/enhanced-workorders')
def enhanced_workorders():
    """Render the enhanced work orders page with optimized performance."""
    if 'username' not in session:
        flash('Please login first', 'error')
        return redirect(url_for('index'))

    # Verify session using enhanced service (with caching)
    if not enhanced_workorder_service.is_session_valid():
        flash('Your session has expired. Please login again.', 'warning')
        session.clear()
        return redirect(url_for('index'))

    try:
        # Record start time for performance comparison
        overall_start_time = time.time()



        # Get user's site ID for display (no work order fetching for lazy loading)
        try:
            user_site_id = enhanced_workorder_service._get_user_site_id()
        except:
            user_site_id = "Unknown"

        overall_time = time.time() - overall_start_time
        logger.info(f"⚡ ENHANCED WORKORDERS: Lightning-fast page load: {overall_time:.3f}s")
        logger.info(f"🔍 ENHANCED WO: Ready for search with site ID: {user_site_id}")

        # Return empty page ready for search
        return render_template(
            'enhanced_workorders.html',
            username=session['username'],
            user_site_id=user_site_id,
            page_load_time=overall_time
        )
    except Exception as e:
        logger.error(f"Error rendering enhanced work orders page: {e}")
        flash('An error occurred while loading the enhanced work orders page. Please try again.', 'error')
        return redirect(url_for('welcome'))

@app.route('/debug-workorders')
def debug_workorders():
    """Debug route to test different work order filters."""
    if 'username' not in session:
        flash('Please login first', 'error')
        return redirect(url_for('index'))

    # Verify session
    if not enhanced_workorder_service.is_session_valid():
        flash('Your session has expired. Please login again.', 'warning')
        session.clear()
        return redirect(url_for('index'))

    try:
        # Get user's site ID
        user_site_id = enhanced_workorder_service._get_user_site_id()
        if not user_site_id:
            return f"<h1>Debug: No user site ID found</h1>"

        # Test different filters
        base_url = getattr(enhanced_workorder_service.token_manager, 'base_url', '')
        api_url = f"{base_url}/oslc/os/mxapiwodetail"

        debug_results = []

        # Test 1: All work orders for site (no status filter)
        try:
            params1 = {
                "oslc.select": "wonum,status,siteid,istask,historyflag",
                "oslc.where": f"siteid=\"{user_site_id}\"",
                "oslc.pageSize": "10"
            }
            response1 = enhanced_workorder_service.token_manager.session.get(
                api_url, params=params1, timeout=(5.0, 15)
            )
            if response1.status_code == 200:
                data1 = response1.json()
                count1 = len(data1.get('member', []))
                debug_results.append(f"Test 1 - All WOs for site {user_site_id}: {count1} found")
            else:
                debug_results.append(f"Test 1 - Failed: {response1.status_code}")
        except Exception as e:
            debug_results.append(f"Test 1 - Error: {str(e)}")

        # Test 2: Enhanced status filter work orders (matching new implementation)
        try:
            params2 = {
                "oslc.select": "wonum,status,siteid,istask,historyflag,woclass",
                "oslc.where": f"(status=\"APPR\" or status=\"ASSIGN\" or status=\"READY\" or status=\"INPRG\" or status=\"PACK\" or status=\"DEFER\" or status=\"WAPPR\" or status=\"WGOVT\" or status=\"AWARD\" or status=\"MTLCXD\" or status=\"MTLISD\" or status=\"PISSUE\" or status=\"RTI\" or status=\"WMATL\" or status=\"WSERV\" or status=\"WSCH\") and (woclass=\"WORKORDER\" or woclass=\"ACTIVITY\") and siteid=\"{user_site_id}\"",
                "oslc.pageSize": "10"
            }
            response2 = enhanced_workorder_service.token_manager.session.get(
                api_url, params=params2, timeout=(5.0, 15)
            )
            if response2.status_code == 200:
                data2 = response2.json()
                count2 = len(data2.get('member', []))
                debug_results.append(f"Test 2 - Enhanced status filter (multiple statuses + woclass) for site {user_site_id}: {count2} found")
            else:
                debug_results.append(f"Test 2 - Failed: {response2.status_code}")
        except Exception as e:
            debug_results.append(f"Test 2 - Error: {str(e)}")

        # Test 3: Any status, non-task, non-history
        try:
            params3 = {
                "oslc.select": "wonum,status,siteid,istask,historyflag",
                "oslc.where": f"siteid=\"{user_site_id}\" and istask=0 and historyflag=0",
                "oslc.pageSize": "10"
            }
            response3 = enhanced_workorder_service.token_manager.session.get(
                api_url, params=params3, timeout=(5.0, 15)
            )
            if response3.status_code == 200:
                data3 = response3.json()
                count3 = len(data3.get('member', []))
                debug_results.append(f"Test 3 - Non-task, non-history for site {user_site_id}: {count3} found")

                # Show sample statuses
                if count3 > 0:
                    statuses = set()
                    for wo in data3.get('member', [])[:5]:
                        statuses.add(wo.get('status', 'Unknown'))
                    debug_results.append(f"Sample statuses found: {', '.join(statuses)}")
            else:
                debug_results.append(f"Test 3 - Failed: {response3.status_code}")
        except Exception as e:
            debug_results.append(f"Test 3 - Error: {str(e)}")

        # Return debug results
        html = f"""
        <h1>Work Order Debug Results for Site: {user_site_id}</h1>
        <ul>
        """
        for result in debug_results:
            html += f"<li>{result}</li>"
        html += """
        </ul>
        <p><a href="/enhanced-workorders">Back to Enhanced Work Orders</a></p>
        <p><a href="/welcome">Back to Welcome</a></p>
        """

        return html

    except Exception as e:
        return f"<h1>Debug Error: {str(e)}</h1>"

@app.route('/force-fresh-login')
def force_fresh_login():
    """Force a completely fresh login to get valid session."""
    # Clear all session data
    session.clear()

    # Clear token manager cache and force logout
    try:
        import os
        # Remove token cache files
        cache_dir = os.path.join(os.path.dirname(__file__), 'cache')
        if os.path.exists(cache_dir):
            for file in os.listdir(cache_dir):
                if file.endswith('.json'):
                    os.remove(os.path.join(cache_dir, file))

        # Clear token manager session
        if hasattr(token_manager, 'session'):
            token_manager.session.cookies.clear()

        # Clear enhanced service caches
        if 'enhanced_profile_service' in globals():
            enhanced_profile_service.clear_cache()
        if 'enhanced_workorder_service' in globals():
            enhanced_workorder_service.clear_cache()

        # Force token manager to clear tokens
        if hasattr(token_manager, 'clear_tokens'):
            token_manager.clear_tokens()

    except Exception as e:
        print(f"Error clearing caches: {e}")

    flash('All caches and tokens cleared. Please login again for fresh session.', 'info')
    return redirect(url_for('index'))

@app.route('/test-fresh-workorders')
def test_fresh_workorders():
    """Test work orders with completely fresh authentication."""
    if 'username' not in session:
        flash('Please login first', 'error')
        return redirect(url_for('index'))

    try:
        # Force fresh login
        username = session.get('username', '')
        password = session.get('password', '')

        if not username or not password:
            flash('Session expired. Please login again.', 'warning')
            return redirect(url_for('index'))

        # Create a completely fresh session
        import requests
        from requests.auth import HTTPBasicAuth

        fresh_session = requests.Session()
        base_url = "https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo"

        # Test different site filters to find work orders (updated to match new implementation)
        test_filters = [
            'siteid="LCVKWT"',
            '(status="APPR" or status="ASSIGN" or status="READY" or status="INPRG" or status="PACK" or status="DEFER" or status="WAPPR" or status="WGOVT" or status="AWARD" or status="MTLCXD" or status="MTLISD" or status="PISSUE" or status="RTI" or status="WMATL" or status="WSERV" or status="WSCH") and siteid="LCVKWT"',
            '(woclass="WORKORDER" or woclass="ACTIVITY") and siteid="LCVKWT"',
            'siteid="LCVKWT" and istask=0 and historyflag=0',
            '(status="APPR" or status="ASSIGN" or status="READY" or status="INPRG" or status="PACK" or status="DEFER" or status="WAPPR" or status="WGOVT" or status="AWARD" or status="MTLCXD" or status="MTLISD" or status="PISSUE" or status="RTI" or status="WMATL" or status="WSERV" or status="WSCH") and (woclass="WORKORDER" or woclass="ACTIVITY") and siteid="LCVKWT" and istask=0 and historyflag=0'
        ]

        html = f"""
        <h1>Fresh Work Order Test</h1>
        <h2>Testing different filters for site LCVKWT</h2>
        """

        for i, filter_clause in enumerate(test_filters, 1):
            html += f"<h3>Test {i}: {filter_clause}</h3>"

            params = {
                "oslc.select": "wonum,description,status,siteid,priority",
                "oslc.where": filter_clause,
                "oslc.pageSize": "10"
            }

            try:
                # Use basic auth for fresh request
                response = fresh_session.get(
                    f"{base_url}/oslc/os/mxapiwodetail",
                    params=params,
                    auth=HTTPBasicAuth(username, password),
                    timeout=30
                )

                html += f"<p><strong>Status:</strong> {response.status_code}</p>"

                if response.status_code == 200:
                    try:
                        data = response.json()
                        if isinstance(data, dict) and 'member' in data:
                            workorders = data['member']
                            html += f"<p><strong>Found:</strong> {len(workorders)} work orders</p>"

                            if workorders:
                                html += "<table border='1'><tr><th>WO Number</th><th>Description</th><th>Status</th><th>Site</th></tr>"
                                for wo in workorders[:5]:
                                    html += f"""
                                    <tr>
                                        <td>{wo.get('wonum', 'N/A')}</td>
                                        <td>{wo.get('description', 'N/A')[:50]}</td>
                                        <td>{wo.get('status', 'N/A')}</td>
                                        <td>{wo.get('siteid', 'N/A')}</td>
                                    </tr>
                                    """
                                html += "</table>"
                                break  # Found work orders, stop testing
                        else:
                            html += f"<p><strong>Response:</strong> {response.text[:300]}</p>"
                    except Exception as e:
                        html += f"<p><strong>Parse Error:</strong> {str(e)}</p>"
                else:
                    html += f"<p><strong>Error:</strong> {response.text[:300]}</p>"

            except Exception as e:
                html += f"<p><strong>Request Error:</strong> {str(e)}</p>"

            html += "<hr>"

        html += """
        <p><a href="/enhanced-workorders">Back to Enhanced Work Orders</a></p>
        <p><a href="/welcome">Back to Welcome</a></p>
        """

        return html

    except Exception as e:
        return f"<h1>Fresh Test Error: {str(e)}</h1><p><a href='/welcome'>Back to Welcome</a></p>"

@app.route('/workorder/<wonum>')
def workorder_detail(wonum):
    """Work order detail page with robust session handling and error recovery."""
    # Check if user is logged in via session
    if 'username' not in session:
        flash('Please login first', 'error')
        return redirect(url_for('index'))

    # Verify session is still valid
    if not enhanced_workorder_service.is_session_valid():
        flash('Your session has expired. Please login again.', 'warning')
        session.clear()
        return redirect(url_for('index'))

    try:
        # Record start time for performance
        start_time = time.time()

        # Step 1: Get user profile with session validation
        try:
            user_profile = enhanced_profile_service.get_user_profile()
            if user_profile and isinstance(user_profile, dict):
                user_site_id = user_profile.get('defaultSite', 'LCVKWT')
            else:
                user_site_id = 'LCVKWT'
                logger.warning("Profile service returned None, using fallback site LCVKWT")
        except Exception as e:
            user_site_id = 'LCVKWT'
            logger.warning(f"Profile service error: {e}, using fallback site LCVKWT")

        logger.info(f"🔍 WO DETAIL: Using site ID: {user_site_id} for work order: {wonum}")

        # Step 2: Get specific work order using enhanced lookup method (searches all sites)
        workorder = enhanced_workorder_service.get_workorder_by_wonum(wonum)

        if not workorder:
            flash(f'Work order {wonum} not found or not accessible', 'error')
            return redirect(url_for('enhanced_workorders'))

        # Step 3: Get work order tasks with optimized performance
        tasks = []
        try:
            # Define API URL for tasks
            tasks_api_url = f"{token_manager.base_url}/oslc/os/mxapiwodetail"

            # Fetch tasks for this work order (tasks have parent = wonum and istask = 1)
            task_filter_clause = f'parent="{wonum}" and istask=1 and historyflag=0'

            # Optimized field selection - only essential fields for faster response
            task_params = {
                "oslc.select": "wonum,description,status,priority,worktype,assignedto,owner,parent,istask,siteid,taskid,location,assetnum,targstartdate,schedstart,schedfinish,estdur,statusdate,reportdate,status_description",
                "oslc.where": task_filter_clause,
                "oslc.pageSize": "50",
                "lean": "1"  # Enable lean mode for faster response
            }

            task_response = token_manager.session.get(
                tasks_api_url,
                params=task_params,
                timeout=(3.0, 15),  # Reduced timeout for faster failure detection
                headers={"Accept": "application/json"},
                allow_redirects=True
            )

            # Check for session expiration in task response
            if 'login' in task_response.url.lower():
                tasks = []
            elif task_response.status_code == 200:
                try:
                    content_type = task_response.headers.get('content-type', '').lower()
                    if 'application/json' in content_type:
                        task_data = task_response.json()

                        if isinstance(task_data, dict):
                            # Check for both 'member' and 'rdfs:member' fields
                            if 'member' in task_data:
                                tasks = task_data['member']
                            elif 'rdfs:member' in task_data:
                                tasks = task_data['rdfs:member']
                            else:
                                tasks = []

                            if tasks:

                                # Clean and normalize task data - optimized for performance
                                cleaned_tasks = []
                                for task_data in tasks:
                                    if isinstance(task_data, dict):
                                        # Helper function to get field value (try both spi: prefix and direct)
                                        def get_field(field_name):
                                            return task_data.get(field_name, task_data.get(f'spi:{field_name}', ''))

                                        # Optimized task object with only essential fields
                                        cleaned_task = {
                                            'wonum': get_field('wonum'),
                                            'description': get_field('description'),
                                            'status': get_field('status'),
                                            'priority': get_field('priority'),
                                            'worktype': get_field('worktype'),
                                            'assignedto': get_field('assignedto'),
                                            'owner': get_field('owner'),
                                            'parent': get_field('parent'),
                                            'istask': get_field('istask') or 1,
                                            'siteid': get_field('siteid'),
                                            'taskid': get_field('taskid'),
                                            'location': get_field('location'),
                                            'assetnum': get_field('assetnum'),
                                            'targstartdate': get_field('targstartdate'),
                                            'schedstart': get_field('schedstart'),
                                            'schedfinish': get_field('schedfinish'),
                                            'estdur': get_field('estdur') or 0,
                                            'statusdate': get_field('statusdate'),
                                            'reportdate': get_field('reportdate'),
                                            'status_description': get_field('status_description')
                                        }
                                        cleaned_tasks.append(cleaned_task)

                                tasks = cleaned_tasks
                        else:
                            tasks = []
                    else:
                        tasks = []
                except Exception as e:
                    logger.error(f"Error parsing task response: {e}")
                    tasks = []
            else:
                tasks = []

        except Exception as e:
            logger.error(f"Error fetching tasks: {e}")
            tasks = []

        load_time = time.time() - start_time
        logger.info(f"🚀 WO DETAIL: Load time: {load_time:.2f}s, Tasks: {len(tasks)}")

        return render_template(
            'workorder_detail.html',
            workorder=workorder,
            tasks=tasks,
            user_site_id=user_site_id,
            load_time=load_time,
            auth_method="Session Cookies (Winning Method)"
        )

    except Exception as e:
        logger.error(f"Work order detail error: {str(e)}")
        flash(f'Error loading work order details: {str(e)}', 'error')
        return redirect(url_for('enhanced_workorders'))

# Complete MXAPIWODETAIL Service Implementation
class MXAPIWODetailService:
    """Complete implementation of all MXAPIWODETAIL API methods and actions"""

    def __init__(self, token_manager):
        self.token_manager = token_manager
        self.logger = logging.getLogger(__name__)

        # Standard Maximo Work Order Status Transitions
        self.status_transitions = {
            'WAPPR': ['APPR', 'CAN'],  # Waiting for Approval -> Approved, Cancelled
            'APPR': ['INPRG', 'CAN', 'CLOSE'],  # Approved -> In Progress, Cancelled, Closed
            'ASSIGN': ['INPRG', 'APPR', 'CAN'],  # Assigned -> In Progress, Approved, Cancelled
            'INPRG': ['COMP', 'CAN', 'CLOSE'],  # In Progress -> Complete, Cancelled, Closed
            'COMP': ['CLOSE', 'INPRG'],  # Complete -> Closed, In Progress
            'CLOSE': [],  # Closed (final state)
            'CAN': []  # Cancelled (final state)
        }

        # Available WSMethods for Work Orders
        self.available_methods = {
            'changeStatus': 'Change work order status',
            'approve': 'Approve work order',
            'start': 'Start work order (set to INPRG)',
            'complete': 'Complete work order (set to COMP)',
            'close': 'Close work order (set to CLOSE)',
            'cancel': 'Cancel work order (set to CAN)',
            'assign': 'Assign work order',
            'unassign': 'Unassign work order',
            'addLabor': 'Add labor to work order',
            'addMaterial': 'Add material to work order',
            'addTool': 'Add tool to work order',
            'addService': 'Add service to work order',
            'createFollowUp': 'Create follow-up work order',
            'duplicate': 'Duplicate work order',
            'route': 'Route work order',
            'plan': 'Plan work order',
            'schedule': 'Schedule work order',
            'addAttachment': 'Add attachment to work order',
            'deleteAttachment': 'Delete attachment from work order'
        }

    def get_api_url(self, action=None, resource_id=None):
        """Get the correct API URL for mxapiwodetail operations using session authentication"""
        # Use /oslc/ endpoint like the working work order calls, not /api/
        base_url = f"{self.token_manager.base_url}/oslc/os/mxapiwodetail"

        if resource_id:
            base_url += f"/{resource_id}"

        if action:
            base_url += f"?action={action}"

        return base_url

    def get_headers(self, method_override=None):
        """Get standard headers for API requests using session authentication"""
        headers = {
            "Accept": "application/json",
            "Content-Type": "application/json"
        }

        if method_override:
            headers["X-method-override"] = method_override

        return headers

    def get_workorder_resource_id(self, wonum):
        """Get the resource ID for a work order by querying the API"""
        try:
            # Query the work order to get its resource ID
            api_url = f"{self.token_manager.base_url}/oslc/os/mxapiwodetail"
            params = {
                "oslc.select": "wonum,rdf:about",
                "oslc.where": f'wonum="{wonum}"',
                "oslc.pageSize": "1"
            }

            self.logger.info(f"🔍 MXAPI: Querying resource ID for {wonum}")
            self.logger.info(f"🔍 MXAPI: URL: {api_url}")
            self.logger.info(f"🔍 MXAPI: Params: {params}")

            response = self.token_manager.session.get(
                api_url,
                params=params,
                timeout=(5.0, 15)
            )

            self.logger.info(f"🔍 MXAPI: Resource lookup response status: {response.status_code}")
            self.logger.info(f"🔍 MXAPI: Resource lookup response content: {response.text[:500]}")

            if response.status_code == 200:
                try:
                    data = response.json()
                    self.logger.info(f"🔍 MXAPI: Parsed JSON keys: {list(data.keys())}")

                    members = data.get('rdfs:member', data.get('member', []))
                    self.logger.info(f"🔍 MXAPI: Found {len(members)} members")

                    if members:
                        first_member = members[0]
                        self.logger.info(f"🔍 MXAPI: First member keys: {list(first_member.keys())}")

                        # Try multiple ways to get the resource ID
                        resource_id = None

                        # Method 1: Try rdf:about field
                        rdf_about = first_member.get('rdf:about', '')
                        self.logger.info(f"🔍 MXAPI: rdf:about value: {rdf_about}")

                        if rdf_about:
                            # Extract resource ID from rdf:about URL
                            # Format: https://domain/maximo/oslc/os/mxapiwodetail/_RESOURCE_ID_
                            resource_id = rdf_about.split('/')[-1]
                            self.logger.info(f"🔍 MXAPI: Found resource ID from rdf:about for {wonum}: {resource_id}")
                            return resource_id

                        # Method 2: Try href field
                        href = first_member.get('href', '')
                        self.logger.info(f"🔍 MXAPI: href value: {href}")

                        if href:
                            # Extract resource ID from href URL
                            # Format: https://domain/maximo/oslc/os/mxapiwodetail/_RESOURCE_ID_
                            resource_id = href.split('/')[-1]
                            self.logger.info(f"🔍 MXAPI: Found resource ID from href for {wonum}: {resource_id}")
                            return resource_id

                        # Method 3: Try to construct resource ID from wonum and siteid
                        # This is a fallback method based on Maximo's typical resource ID format
                        if wonum:
                            # Maximo often uses base64-like encoding for resource IDs
                            # Try common patterns
                            import base64
                            try:
                                # Pattern 1: Simple encoding
                                potential_id = f"_TENWS1dULz{wonum}"
                                self.logger.info(f"🔍 MXAPI: Trying constructed resource ID for {wonum}: {potential_id}")
                                return potential_id
                            except Exception as e:
                                self.logger.warning(f"🔍 MXAPI: Could not construct resource ID: {e}")

                        self.logger.warning(f"🔍 MXAPI: No resource ID found in any field for {wonum}")
                    else:
                        self.logger.warning(f"⚠️ MXAPI: No members found for work order {wonum}")

                except ValueError as json_error:
                    self.logger.error(f"JSON parsing error for {wonum}: {str(json_error)}")
                    self.logger.error(f"Raw response: {response.text}")
            else:
                self.logger.error(f"HTTP error {response.status_code} for {wonum}: {response.text}")

            self.logger.warning(f"⚠️ MXAPI: Could not find resource ID for work order {wonum}")
            return None

        except Exception as e:
            self.logger.error(f"Error getting resource ID for {wonum}: {str(e)}")
            return None

    def execute_wsmethod(self, method_name, wonum=None, data=None, bulk=False, resource_id=None):
        """Execute any WSMethod on work order(s) with proper resource ID handling"""
        try:
            if method_name not in self.available_methods:
                return {
                    'success': False,
                    'error': f'Unknown method: {method_name}',
                    'available_methods': list(self.available_methods.keys())
                }

            # For individual work order operations, we need the resource ID
            if not bulk and wonum and not resource_id:
                resource_id = self.get_workorder_resource_id(wonum)
                if not resource_id:
                    return {
                        'success': False,
                        'error': f'Could not find resource ID for work order {wonum}. This is required for status changes.'
                    }

            # Prepare URL and data based on operation type
            action = f"wsmethod:{method_name}"

            if bulk and isinstance(data, list):
                # Bulk operation - use collection URL with BULK header
                api_url = self.get_api_url(action=action)
                request_data = data
                headers = self.get_headers("BULK")
                self.logger.info(f"🔄 MXAPI: Bulk operation - {len(data)} work orders")
            else:
                # Individual operation - use resource-specific URL
                if resource_id:
                    api_url = self.get_api_url(action=action, resource_id=resource_id)
                    request_data = data or {}
                    # Don't include wonum in data when using resource ID in URL
                    headers = self.get_headers("PATCH")
                    self.logger.info(f"🔄 MXAPI: Individual operation with resource ID: {resource_id}")
                else:
                    # Fallback to collection URL (may not work for all operations)
                    api_url = self.get_api_url(action=action)
                    request_data = data or {}
                    if wonum:
                        request_data['wonum'] = wonum
                    headers = self.get_headers("PATCH")
                    self.logger.info(f"🔄 MXAPI: Individual operation without resource ID (fallback)")

            self.logger.info(f"🔄 MXAPI: Executing {method_name} on work order(s)")
            self.logger.info(f"🔄 MXAPI: URL: {api_url}")
            self.logger.info(f"🔄 MXAPI: Data: {request_data}")
            self.logger.info(f"🔄 MXAPI: Headers: {headers}")

            # Execute request using session authentication (same as working work order calls)
            response = self.token_manager.session.post(
                api_url,
                json=request_data,
                headers=headers,
                timeout=(5.0, 30)
            )

            return self._process_response(response, method_name)

        except Exception as e:
            self.logger.error(f"Error executing {method_name}: {str(e)}")
            return {'success': False, 'error': str(e)}

    def _process_response(self, response, method_name):
        """Process Maximo API response and handle errors"""
        try:
            self.logger.info(f"🔍 MXAPI RESPONSE: Status: {response.status_code}")
            self.logger.info(f"🔍 MXAPI RESPONSE: Content: {response.text}")

            if response.status_code in [200, 201, 204]:
                try:
                    response_json = response.json()

                    # Check for Maximo errors in successful HTTP responses
                    if isinstance(response_json, list):
                        errors = []
                        successes = []

                        for item in response_json:
                            if '_responsedata' in item and 'Error' in item['_responsedata']:
                                error_info = item['_responsedata']['Error']
                                errors.append({
                                    'code': error_info.get('reasonCode', 'Unknown'),
                                    'message': error_info.get('message', 'Unknown error')
                                })
                            else:
                                successes.append(item)

                        if errors:
                            self.logger.error(f"❌ MXAPI ERRORS: {errors}")
                            return {
                                'success': False,
                                'errors': errors,
                                'successes': successes,
                                'method': method_name
                            }

                    self.logger.info(f"✅ MXAPI: {method_name} executed successfully")
                    return {
                        'success': True,
                        'data': response_json,
                        'method': method_name
                    }

                except ValueError:
                    # Non-JSON response but successful HTTP status
                    return {
                        'success': True,
                        'message': f'{method_name} executed successfully',
                        'method': method_name
                    }
            else:
                return {
                    'success': False,
                    'error': f'HTTP {response.status_code}',
                    'details': response.text[:200],
                    'method': method_name
                }

        except Exception as e:
            self.logger.error(f"Error processing response: {str(e)}")
            return {'success': False, 'error': f'Response processing error: {str(e)}'}

    def get_workorder_attachments(self, wonum):
        """Get all attachments (doclinks) for a work order using proper MXAPIWODETAIL approach"""
        try:
            self.logger.info(f"📎 ATTACHMENTS: Getting attachments for work order {wonum}")

            # Try different approaches to get doclinks
            # Approach 1: Query with doclinks collection expanded
            api_url = f"{self.token_manager.base_url}/oslc/os/mxapiwodetail"
            params = {
                "oslc.select": "wonum,siteid,doclinks{*}",
                "oslc.where": f'wonum="{wonum}"',
                "oslc.pageSize": "1",
                "collectioncount": "1"
            }

            response = self.token_manager.session.get(
                api_url,
                params=params,
                timeout=(5.0, 15)
            )

            self.logger.info(f"📎 ATTACHMENTS: Response status: {response.status_code}")

            if response.status_code == 200:
                data = response.json()
                self.logger.info(f"📎 ATTACHMENTS: Response keys: {list(data.keys())}")

                if 'member' in data and data['member']:
                    wo_data = data['member'][0]
                    self.logger.info(f"📎 ATTACHMENTS: WO data keys: {list(wo_data.keys())}")

                    # Check for doclinks in different formats
                    doclinks = wo_data.get('doclinks', [])
                    self.logger.info(f"📎 ATTACHMENTS: Doclinks type: {type(doclinks)}, value: {doclinks}")

                    if isinstance(doclinks, list) and doclinks:
                        # Direct doclinks data as list
                        processed_attachments = []
                        for attachment in doclinks:
                            self.logger.info(f"📎 ATTACHMENTS: Processing attachment: {attachment}")

                            # Extract filename from document path if available
                            document = attachment.get('document', '')
                            filename = document.split('/')[-1] if document else attachment.get('urlname', 'Unknown File')
                            file_extension = filename.split('.')[-1].lower() if '.' in filename else ''

                            processed_attachment = {
                                'docinfoid': attachment.get('docinfoid'),
                                'document': document,
                                'description': attachment.get('description', ''),
                                'createdate': attachment.get('createdate'),
                                'changeby': attachment.get('changeby'),
                                'urltype': attachment.get('urltype', 'FILE'),
                                'urlname': attachment.get('urlname'),
                                'doctype': attachment.get('doctype', ''),
                                'filename': filename,
                                'file_extension': file_extension,
                                'href': attachment.get('href'),
                                'original_data': attachment
                            }
                            processed_attachments.append(processed_attachment)

                        self.logger.info(f"✅ ATTACHMENTS: Found {len(processed_attachments)} attachments for {wonum}")
                        return {
                            'success': True,
                            'attachments': processed_attachments,
                            'count': len(processed_attachments)
                        }

                    elif isinstance(doclinks, dict) and 'member' in doclinks:
                        # Doclinks as dict with member array (this is what we're getting!)
                        attachments = doclinks.get('member', [])
                        self.logger.info(f"📎 ATTACHMENTS: Found {len(attachments)} attachments in member array")

                        processed_attachments = []
                        for attachment in attachments:
                            # Get data from describedBy if available, otherwise use direct fields
                            described_by = attachment.get('describedBy', {})

                            # Use describedBy data if available, fallback to direct fields
                            filename = described_by.get('fileName') or attachment.get('document', 'Unknown File')
                            description = described_by.get('description') or attachment.get('description', '')
                            docinfoid = described_by.get('docinfoid') or attachment.get('docinfoid')
                            changeby = described_by.get('changeby') or described_by.get('createby') or attachment.get('changeby')
                            createdate = described_by.get('created') or attachment.get('createdate')
                            urltype = described_by.get('urlType') or attachment.get('urltype', 'FILE')
                            doctype = described_by.get('docType') or attachment.get('doctype', '')

                            file_extension = filename.split('.')[-1].lower() if '.' in filename else ''

                            processed_attachment = {
                                'docinfoid': docinfoid,
                                'document': filename,
                                'description': description,
                                'createdate': createdate,
                                'changeby': changeby,
                                'urltype': urltype,
                                'urlname': filename,
                                'doctype': doctype,
                                'filename': filename,
                                'file_extension': file_extension,
                                'href': attachment.get('href'),
                                'original_data': attachment
                            }
                            processed_attachments.append(processed_attachment)

                        self.logger.info(f"✅ ATTACHMENTS: Processed {len(processed_attachments)} attachments for {wonum}")
                        return {
                            'success': True,
                            'attachments': processed_attachments,
                            'count': len(processed_attachments)
                        }

                    elif isinstance(doclinks, dict) and 'rdf:resource' in doclinks:
                        # Reference URL approach
                        doclinks_url = doclinks['rdf:resource']
                        self.logger.info(f"📎 ATTACHMENTS: Found doclinks URL: {doclinks_url}")

                        doclinks_response = self.token_manager.session.get(
                            doclinks_url,
                            timeout=(5.0, 15)
                        )

                        if doclinks_response.status_code == 200:
                            doclinks_data = doclinks_response.json()
                            attachments = doclinks_data.get('member', [])

                            processed_attachments = []
                            for attachment in attachments:
                                document = attachment.get('document', '')
                                filename = document.split('/')[-1] if document else attachment.get('urlname', 'Unknown File')
                                file_extension = filename.split('.')[-1].lower() if '.' in filename else ''

                                processed_attachment = {
                                    'docinfoid': attachment.get('docinfoid'),
                                    'document': document,
                                    'description': attachment.get('description', ''),
                                    'createdate': attachment.get('createdate'),
                                    'changeby': attachment.get('changeby'),
                                    'urltype': attachment.get('urltype', 'FILE'),
                                    'urlname': attachment.get('urlname'),
                                    'doctype': attachment.get('doctype', ''),
                                    'filename': filename,
                                    'file_extension': file_extension,
                                    'href': attachment.get('href'),
                                    'original_data': attachment
                                }
                                processed_attachments.append(processed_attachment)

                            self.logger.info(f"✅ ATTACHMENTS: Found {len(processed_attachments)} attachments for {wonum}")
                            return {
                                'success': True,
                                'attachments': processed_attachments,
                                'count': len(processed_attachments)
                            }
                        else:
                            self.logger.error(f"❌ ATTACHMENTS: Doclinks URL query failed: HTTP {doclinks_response.status_code}")
                            return {
                                'success': False,
                                'error': f'Doclinks query failed: HTTP {doclinks_response.status_code}',
                                'details': doclinks_response.text[:200]
                            }
                    else:
                        # No attachments found
                        self.logger.info(f"📎 ATTACHMENTS: No attachments found for {wonum}")
                        return {
                            'success': True,
                            'attachments': [],
                            'count': 0
                        }
                else:
                    return {
                        'success': False,
                        'error': f'Work order {wonum} not found'
                    }
            else:
                self.logger.error(f"❌ ATTACHMENTS: HTTP {response.status_code} - {response.text[:500]}")
                return {
                    'success': False,
                    'error': f'HTTP {response.status_code}',
                    'details': response.text[:500]
                }

        except Exception as e:
            self.logger.error(f"❌ ATTACHMENTS: Error getting attachments for {wonum}: {str(e)}")
            return {'success': False, 'error': f'Error getting attachments: {str(e)}'}

    def add_workorder_attachment(self, wonum, file_data, description=None, doctype=None):
        """Add an attachment to a work order using proper OSLC DOCLINKS API as per IBM documentation"""
        try:
            self.logger.info(f"📎 ATTACHMENTS: Adding attachment to work order {wonum}")

            # Step 1: Get the work order resource ID
            resource_id = self.get_workorder_resource_id(wonum)
            if not resource_id:
                return {'success': False, 'error': f'Work order {wonum} not found'}

            # Step 2: Use the official OSLC approach from IBM documentation
            # POST binary content directly to the doclinks collection with proper headers
            api_url = f"{self.token_manager.base_url}/oslc/os/mxapiwodetail/{resource_id}/doclinks"

            # Prepare headers as per IBM OSLC documentation
            headers = self.get_headers()

            # Remove Content-Type to let requests handle binary content
            if 'Content-Type' in headers:
                del headers['Content-Type']

            # Set OSLC headers as per IBM documentation
            filename = file_data.get('filename', 'attachment.txt')
            file_content = file_data.get('content', b'')
            content_type = file_data.get('content_type', 'application/octet-stream')

            # Set required headers per IBM OSLC spec
            headers['slug'] = filename  # File name
            headers['Content-Type'] = content_type  # MIME type
            headers['x-document-meta'] = f"FILE/{doctype or 'Attachments'}"  # Document type (must be valid doctype)
            headers['x-document-description'] = description or filename  # Description

            self.logger.info(f"📎 ATTACHMENTS: Adding attachment via OSLC DOCLINKS to {api_url}")
            self.logger.info(f"📎 ATTACHMENTS: Upload Headers: {headers}")
            self.logger.info(f"📎 ATTACHMENTS: Upload File: {filename} ({len(file_content)} bytes)")
            self.logger.info(f"📎 ATTACHMENTS: Upload Content-Type: {content_type}")
            self.logger.info(f"📎 ATTACHMENTS: Upload Payload size: {len(file_content)} bytes")
            if len(file_content) < 1000:  # Log small file contents for debugging
                self.logger.info(f"📎 ATTACHMENTS: Upload Content preview: {file_content[:200]}")

            # POST binary content directly as per IBM OSLC documentation
            response = self.token_manager.session.post(
                api_url,
                data=file_content,  # Binary content in body
                headers=headers,
                timeout=(10.0, 60)
            )

            self.logger.info(f"📎 ATTACHMENTS: OSLC response status: {response.status_code}")
            self.logger.info(f"📎 ATTACHMENTS: OSLC response headers: {dict(response.headers)}")
            self.logger.info(f"📎 ATTACHMENTS: OSLC response: {response.text[:500]}")

            if response.status_code in [200, 201]:
                # Get the location header which contains the URL for the uploaded attachment
                location = response.headers.get('Location', '')
                link = response.headers.get('Link', '')

                self.logger.info(f"✅ ATTACHMENTS: Successfully uploaded attachment {filename} to {wonum}")
                self.logger.info(f"📎 ATTACHMENTS: Location: {location}")
                self.logger.info(f"📎 ATTACHMENTS: Link: {link}")

                return {
                    'success': True,
                    'message': 'Attachment uploaded successfully',
                    'filename': filename,
                    'location': location,
                    'link': link
                }
            else:
                self.logger.error(f"❌ ATTACHMENTS: Failed to upload attachment: {response.status_code}")
                return {
                    'success': False,
                    'error': f'Failed to upload attachment: HTTP {response.status_code}',
                    'details': response.text[:500]
                }

        except Exception as e:
            self.logger.error(f"❌ ATTACHMENTS: Error adding attachment to {wonum}: {str(e)}")
            return {'success': False, 'error': f'Error adding attachment: {str(e)}'}

    def download_attachment(self, wonum, identifier):
        """Download an attachment by identifier"""
        try:
            self.logger.info(f"📎 DOWNLOAD: Downloading attachment {identifier} from {wonum}")

            # Get resource ID
            resource_id = self.get_workorder_resource_id(wonum)
            if not resource_id:
                return {'success': False, 'error': f'Work order {wonum} not found'}

            # Download URL
            download_url = f"{self.token_manager.base_url}/oslc/os/mxapiwodetail/{resource_id}/doclinks/{identifier}"

            headers = self.get_headers()
            response = requests.get(download_url, headers=headers)

            if response.status_code == 200:
                self.logger.info(f"✅ DOWNLOAD: Successfully downloaded attachment {identifier}")
                return {
                    'success': True,
                    'content': response.content,
                    'content_type': response.headers.get('content-type', 'application/octet-stream')
                }
            else:
                self.logger.error(f"❌ DOWNLOAD: Failed to download attachment: {response.status_code}")
                return {'success': False, 'error': f'Download failed: HTTP {response.status_code}'}

        except Exception as e:
            self.logger.error(f"❌ DOWNLOAD: Error downloading attachment: {e}")
            return {'success': False, 'error': f'Download error: {str(e)}'}

    def download_workorder_attachment(self, wonum, docinfoid):
        """Download an attachment file from a work order"""
        try:
            self.logger.info(f"📎 ATTACHMENTS: Downloading attachment {docinfoid} from work order {wonum}")

            # First get the attachment details to verify it exists
            attachments_result = self.get_workorder_attachments(wonum)
            if not attachments_result['success']:
                return {'success': False, 'error': 'Failed to get attachment details'}

            # Find the specific attachment
            self.logger.info(f"📎 ATTACHMENTS: Looking for docinfoid: {docinfoid}")
            self.logger.info(f"📎 ATTACHMENTS: Available attachments: {[att.get('docinfoid') for att in attachments_result['attachments']]}")

            attachment = None
            for att in attachments_result['attachments']:
                if str(att.get('docinfoid')) == str(docinfoid):
                    attachment = att
                    break

            if not attachment:
                return {'success': False, 'error': 'Attachment not found'}

            # Debug: Log the attachment structure
            self.logger.info(f"📎 ATTACHMENTS: Attachment structure: {attachment}")

            # Try multiple download approaches to handle server issues
            download_attempts = []

            # Approach 1: Use the direct href from attachment metadata
            if 'href' in attachment:
                original_href = attachment['href']
                # Fix hostname if needed - use the base_url hostname
                from urllib.parse import urlparse, urlunparse
                parsed_base = urlparse(self.token_manager.base_url)
                parsed_href = urlparse(original_href)

                # Replace hostname with the one from base_url
                fixed_href = urlunparse((
                    parsed_href.scheme,
                    parsed_base.netloc,  # Use base_url hostname
                    parsed_href.path,
                    parsed_href.params,
                    parsed_href.query,
                    parsed_href.fragment
                ))

                download_attempts.append({
                    'url': fixed_href,
                    'description': 'Direct href from metadata (hostname corrected)'
                })

                # Also try without /meta/ in the path
                if '/meta/' in fixed_href:
                    no_meta_url = fixed_href.replace('/meta/', '/')
                    download_attempts.append({
                        'url': no_meta_url,
                        'description': 'Direct href without /meta/ (hostname corrected)'
                    })

                # Try with different URL patterns based on what Maximo actually provides
                # Pattern 1: Try the localref URL (if available)
                if 'original_data' in attachment and 'localref' in attachment['original_data']:
                    localref_url = attachment['original_data']['localref']
                    parsed_localref = urlparse(localref_url)
                    fixed_localref = urlunparse((
                        parsed_localref.scheme,
                        parsed_base.netloc,
                        parsed_localref.path,
                        parsed_localref.params,
                        parsed_localref.query,
                        parsed_localref.fragment
                    ))
                    download_attempts.append({
                        'url': fixed_localref,
                        'description': 'Localref URL (hostname corrected)'
                    })

            # Approach 2: Construct URL using resource ID and doclinks ID
            resource_id = self.get_workorder_resource_id(wonum)
            if resource_id:
                # Get the doclinks ID from the attachment
                doclinks_id = None

                # Try to get it from the attachment href or identifier
                # First try to get the identifier from the original_data if available
                if 'original_data' in attachment and 'describedBy' in attachment['original_data']:
                    doclinks_id = attachment['original_data']['describedBy'].get('identifier')

                # Fallback to parsing href
                if not doclinks_id and 'href' in attachment:
                    # Extract ID from href like: .../doclinks/188548 or .../doclinks/meta/188548
                    href_parts = attachment['href'].split('/')
                    if 'doclinks' in href_parts:
                        doclinks_idx = href_parts.index('doclinks')
                        if doclinks_idx + 1 < len(href_parts):
                            potential_id = href_parts[doclinks_idx + 1]
                            # Check if next part is 'meta'
                            if potential_id == 'meta' and doclinks_idx + 2 < len(href_parts):
                                doclinks_id = href_parts[doclinks_idx + 2]
                            else:
                                doclinks_id = potential_id

                # Final fallback to identifier if available
                if not doclinks_id and 'identifier' in attachment:
                    doclinks_id = attachment['identifier']

                if doclinks_id:
                    # Try different URL patterns for actual file content
                    constructed_url = f"{self.token_manager.base_url}/oslc/os/mxapiwodetail/{resource_id}/doclinks/{doclinks_id}"
                    download_attempts.append({
                        'url': constructed_url,
                        'description': 'Constructed URL without /meta/'
                    })

                    # Try with /content/ instead of /meta/
                    content_url = f"{self.token_manager.base_url}/oslc/os/mxapiwodetail/{resource_id}/doclinks/content/{doclinks_id}"
                    download_attempts.append({
                        'url': content_url,
                        'description': 'Constructed URL with /content/'
                    })

                    # Try with /download/ pattern
                    download_url = f"{self.token_manager.base_url}/oslc/os/mxapiwodetail/{resource_id}/doclinks/download/{doclinks_id}"
                    download_attempts.append({
                        'url': download_url,
                        'description': 'Constructed URL with /download/'
                    })

                    # Try alternative patterns based on Maximo OSLC documentation
                    # Pattern: Try with Accept header for binary content
                    binary_url = f"{self.token_manager.base_url}/oslc/os/mxapiwodetail/{resource_id}/doclinks/{doclinks_id}"
                    download_attempts.append({
                        'url': binary_url,
                        'description': 'Binary content with Accept header',
                        'headers': {'Accept': 'application/octet-stream'}
                    })

                    # Pattern: Try with specific OSLC content negotiation
                    oslc_url = f"{self.token_manager.base_url}/oslc/os/mxapiwodetail/{resource_id}/doclinks/{doclinks_id}"
                    download_attempts.append({
                        'url': oslc_url,
                        'description': 'OSLC content negotiation',
                        'headers': {'Accept': '*/*'}
                    })

            # Try each download approach
            base_headers = self.get_headers()

            for attempt in download_attempts:
                try:
                    self.logger.info(f"📎 ATTACHMENTS: Trying {attempt['description']}: {attempt['url']}")

                    # Use custom headers if specified, otherwise use base headers
                    request_headers = base_headers.copy()
                    if 'headers' in attempt:
                        request_headers.update(attempt['headers'])
                        self.logger.info(f"📎 ATTACHMENTS: Using custom headers: {attempt['headers']}")

                    # Use the authenticated session from token_manager instead of creating a new one
                    # This ensures proper authentication cookies and headers are included
                    response = self.token_manager.session.get(
                        attempt['url'],
                        headers=request_headers,
                        timeout=(10.0, 60),
                        stream=True
                    )

                    self.logger.info(f"📎 ATTACHMENTS: Response status: {response.status_code}")

                    if response.status_code == 200:
                        # Get content type and filename
                        content_type = response.headers.get('content-type', 'application/octet-stream')
                        # Try different filename sources based on how we processed the attachment
                        filename = (attachment.get('filename') or
                                  attachment.get('document') or
                                  attachment.get('urlname') or
                                  f'attachment_{docinfoid}')

                        # Read the file content first
                        file_content = response.content

                        # Check if we got actual file content (not JSON metadata)
                        if content_type.startswith('application/json'):
                            self.logger.warning(f"📎 ATTACHMENTS: Got JSON response, may be metadata instead of file content")
                            # Try the next approach
                            continue

                        # Check if this is actually file content or HTML error page
                        # Look for HTML content which indicates an error page
                        content_str = file_content.decode('utf-8', errors='ignore')
                        if (content_str.strip().startswith('<!doctype html') or
                            content_str.strip().startswith('<html') or
                            'login' in content_str.lower() and len(file_content) < 5000):
                            self.logger.warning(f"📎 ATTACHMENTS: Got HTML error page ({len(file_content)} bytes), trying next approach")
                            self.logger.info(f"📎 ATTACHMENTS: HTML content preview: {content_str[:200]}")
                            continue

                        # If we get here, we have actual file content!
                        self.logger.info(f"📎 ATTACHMENTS: ✅ Successfully downloaded file content ({len(file_content)} bytes)")
                        self.logger.info(f"📎 ATTACHMENTS: Content type: {content_type}")

                        # Validate file content based on file type
                        filename = attachment.get('filename', '').lower()
                        if filename.endswith('.pdf') and not file_content.startswith(b'%PDF'):
                            self.logger.warning(f"📎 ATTACHMENTS: Expected PDF but got different content, trying next approach")
                            continue
                        elif filename.endswith('.png') and not file_content.startswith(b'\x89PNG'):
                            self.logger.warning(f"📎 ATTACHMENTS: Expected PNG but got different content, trying next approach")
                            continue
                        elif filename.endswith('.jpg') or filename.endswith('.jpeg'):
                            if not (file_content.startswith(b'\xff\xd8\xff') or file_content.startswith(b'\xff\xe0')):
                                self.logger.warning(f"📎 ATTACHMENTS: Expected JPEG but got different content, trying next approach")
                                continue

                        # For small text files, log a preview
                        if len(file_content) < 500 and content_type and 'text' in content_type:
                            self.logger.info(f"📎 ATTACHMENTS: Text content preview: {content_str[:200]}")

                        # Also check if it's HTML content (error pages or metadata)
                        if content_type.startswith('text/html'):
                            self.logger.warning(f"📎 ATTACHMENTS: Got HTML response, may be error page or metadata instead of file content")
                            continue

                        self.logger.info(f"✅ ATTACHMENTS: Successfully downloaded {filename} ({len(file_content)} bytes) using {attempt['description']}")

                        return {
                            'success': True,
                            'filename': filename,
                            'content': file_content,
                            'content_type': content_type,
                            'size': len(file_content)
                        }
                    elif response.status_code == 500:
                        self.logger.warning(f"📎 ATTACHMENTS: Server error (500) for {attempt['description']}, trying next approach")
                        continue
                    else:
                        self.logger.warning(f"📎 ATTACHMENTS: HTTP {response.status_code} for {attempt['description']}, trying next approach")
                        continue

                except Exception as attempt_error:
                    self.logger.warning(f"📎 ATTACHMENTS: Error with {attempt['description']}: {str(attempt_error)}")
                    continue

            # If all attempts failed, check if this is a known issue with older files
            attachment_size = attachment.get('original_data', {}).get('describedBy', {}).get('attachmentSize', 0)
            filename = attachment.get('filename', 'Unknown')

            if attachment_size == 0:
                self.logger.warning(f"📎 ATTACHMENTS: File {filename} has attachmentSize: 0, likely a data issue with older files")
                self.logger.warning(f"📎 ATTACHMENTS: This file may not be properly stored in the system")
                return {
                    'success': False,
                    'error': 'File not accessible',
                    'details': f'The file "{filename}" appears to have a storage issue (attachmentSize: 0). This commonly occurs with older files that were not properly stored in the system.',
                    'suggestion': 'Try re-uploading the file if you have access to the original, or contact your system administrator.',
                    'attachment_info': attachment
                }

            self.logger.error(f"❌ ATTACHMENTS: All download attempts failed for attachment {docinfoid}")
            return {
                'success': False,
                'error': 'All download attempts failed',
                'attempts_tried': [attempt['description'] for attempt in download_attempts],
                'attachment_info': attachment
            }

        except Exception as e:
            self.logger.error(f"❌ ATTACHMENTS: Error downloading attachment {docinfoid}: {str(e)}")
            return {'success': False, 'error': f'Error downloading attachment: {str(e)}'}

    def delete_workorder_attachment(self, wonum, docinfoid):
        """Delete an attachment from a work order"""
        try:
            self.logger.info(f"📎 ATTACHMENTS: Deleting attachment {docinfoid} from work order {wonum}")

            # Get the work order resource ID
            resource_id = self.get_workorder_resource_id(wonum)
            if not resource_id:
                return {'success': False, 'error': f'Work order {wonum} not found'}

            # Get attachment details first to find the correct identifier
            attachments = self.get_workorder_attachments(wonum)
            if not attachments.get('success'):
                return {'success': False, 'error': 'Failed to get attachment details'}

            # Find the attachment to get its identifier
            target_attachment = None
            for attachment in attachments.get('attachments', []):
                if str(attachment.get('docinfoid')) == str(docinfoid):
                    target_attachment = attachment
                    break

            if not target_attachment:
                return {'success': False, 'error': f'Attachment {docinfoid} not found'}

            # Get the identifier from the attachment metadata
            identifier = target_attachment.get('original_data', {}).get('describedBy', {}).get('identifier')
            if not identifier:
                self.logger.warning(f"📎 ATTACHMENTS: No identifier found, using docinfoid as fallback")
                identifier = docinfoid

            headers = self.get_headers()

            # Try different approaches for deleting attachments
            delete_attempts = [
                {
                    'description': 'Direct DELETE to doclinks with identifier',
                    'url': f"{self.token_manager.base_url}/oslc/os/mxapiwodetail/{resource_id}/doclinks/{identifier}",
                    'method': 'DELETE'
                },
                {
                    'description': 'Direct DELETE to doclinks with docinfoid',
                    'url': f"{self.token_manager.base_url}/oslc/os/mxapiwodetail/{resource_id}/doclinks/{docinfoid}",
                    'method': 'DELETE'
                },
                {
                    'description': 'POST with delete action',
                    'url': f"{self.token_manager.base_url}/oslc/os/mxapiwodetail/{resource_id}",
                    'method': 'POST',
                    'payload': {
                        'doclinks': [{
                            'docinfoid': int(docinfoid),
                            '_action': 'Delete'
                        }]
                    }
                }
            ]

            for attempt in delete_attempts:
                try:
                    self.logger.info(f"📎 ATTACHMENTS: Trying {attempt['description']}")
                    self.logger.info(f"📎 ATTACHMENTS: URL: {attempt['url']}")

                    if attempt['method'] == 'DELETE':
                        response = self.token_manager.session.delete(
                            attempt['url'],
                            headers=headers,
                            timeout=(5.0, 30)
                        )
                    elif attempt['method'] == 'POST':
                        post_headers = headers.copy()
                        post_headers['Content-Type'] = 'application/json'
                        self.logger.info(f"📎 ATTACHMENTS: Payload: {attempt['payload']}")

                        response = self.token_manager.session.post(
                            attempt['url'],
                            json=attempt['payload'],
                            headers=post_headers,
                            timeout=(5.0, 30)
                        )

                    self.logger.info(f"📎 ATTACHMENTS: Response status: {response.status_code}")

                    if response.status_code in [200, 204]:
                        self.logger.info(f"✅ ATTACHMENTS: Successfully deleted attachment {docinfoid} using {attempt['description']}")
                        return {
                            'success': True,
                            'message': 'Attachment deleted successfully',
                            'method_used': attempt['description']
                        }
                    elif response.status_code == 404:
                        self.logger.warning(f"📎 ATTACHMENTS: Attachment not found (404) for {attempt['description']}")
                        continue
                    elif response.status_code == 501:
                        self.logger.warning(f"📎 ATTACHMENTS: Method not implemented (501) for {attempt['description']}")
                        continue
                    else:
                        self.logger.warning(f"📎 ATTACHMENTS: Failed with status {response.status_code} for {attempt['description']}")
                        self.logger.info(f"📎 ATTACHMENTS: Response: {response.text[:200]}")
                        continue

                except Exception as attempt_error:
                    self.logger.warning(f"📎 ATTACHMENTS: Error with {attempt['description']}: {str(attempt_error)}")
                    continue

            # If all attempts failed
            self.logger.error(f"❌ ATTACHMENTS: All delete attempts failed for attachment {docinfoid}")

            # Check if this is a known limitation with this Maximo version
            filename = target_attachment.get('filename', 'this file')
            return {
                'success': False,
                'error': 'Attachment deletion not supported',
                'details': f'This version of Maximo does not support programmatic deletion of attachments through the OSLC API. The file "{filename}" cannot be deleted through this interface.',
                'suggestion': 'To delete this attachment, please use the standard Maximo web interface or contact your system administrator.',
                'workaround': 'You can access the full Maximo interface to manage attachments with complete functionality.'
            }

        except Exception as e:
            self.logger.error(f"❌ ATTACHMENTS: Error deleting attachment {docinfoid} from {wonum}: {str(e)}")
            return {'success': False, 'error': f'Error deleting attachment: {str(e)}'}

# Initialize the complete MXAPIWODETAIL service
mxapi_service = MXAPIWODetailService(token_manager)

@app.route('/api/task/<task_wonum>/status', methods=['POST'])
def update_task_status(task_wonum):
    """Update task status using direct API call with fallback methods."""
    # Check if user is logged in
    if not hasattr(token_manager, 'username') or not token_manager.username:
        return jsonify({'success': False, 'error': 'Not logged in'})

    try:
        # Get the new status from request
        data = request.get_json()
        if not data or 'status' not in data:
            return jsonify({'success': False, 'error': 'Status is required'})

        new_status = data['status']
        bypass_signature = data.get('bypass_signature', False)  # For signature submission

        # Validate status
        valid_statuses = ['WAPPR', 'APPR', 'ASSIGN', 'INPRG', 'COMP', 'CLOSE', 'CAN', 'READY', 'PACK', 'DEFER',
                         'WGOVT', 'AWARD', 'MTLCXD', 'MTLISD', 'PISSUE', 'RTI', 'WMATL', 'WSERV', 'WSCH', 'SET']
        if new_status not in valid_statuses:
            return jsonify({'success': False, 'error': f'Invalid status: {new_status}'})

        # Check if signature is required for task (unless bypassing for signature submission)
        if not bypass_signature and is_signature_required(new_status, 'task'):
            logger.info(f"📝 SIGNATURE: Signature required for task {task_wonum} status change to {new_status}")
            return jsonify({
                'success': False,
                'signature_required': True,
                'status': new_status,
                'wo_type': 'task',
                'message': f'Digital signature required for status change to {new_status}'
            })

        logger.info(f"🔄 TASK STATUS: Updating task {task_wonum} -> {new_status}")

        # Method 1: Try using the MXAPI service first
        try:
            result = mxapi_service.execute_wsmethod(
                'changeStatus',
                wonum=task_wonum,
                data={'status': new_status}
            )

            if result.get('success'):
                logger.info(f"✅ TASK STATUS: Successfully updated via MXAPI service")
                return jsonify(result)
            else:
                logger.warning(f"⚠️ TASK STATUS: MXAPI service failed: {result.get('error', 'Unknown error')}")
        except Exception as e:
            logger.warning(f"⚠️ TASK STATUS: MXAPI service exception: {e}")

        # Method 2: Direct API call using collection URL (fallback)
        logger.info(f"🔄 TASK STATUS: Trying direct API call for task {task_wonum}")

        base_url = getattr(token_manager, 'base_url', '')
        api_url = f"{base_url}/oslc/os/mxapiwodetail"

        # Use collection URL with action parameter
        action_url = f"{api_url}?action=wsmethod:changeStatus"

        # Prepare request data
        request_data = {
            'wonum': task_wonum,
            'status': new_status
        }

        headers = {
            "Accept": "application/json",
            "Content-Type": "application/json",
            "X-method-override": "PATCH"
        }

        logger.info(f"🔄 TASK STATUS: Direct API URL: {action_url}")
        logger.info(f"🔄 TASK STATUS: Request data: {request_data}")

        response = token_manager.session.post(
            action_url,
            json=request_data,
            headers=headers,
            timeout=(5.0, 30)
        )

        logger.info(f"🔍 TASK STATUS: Response status: {response.status_code}")
        logger.info(f"🔍 TASK STATUS: Response content: {response.text[:500]}")

        if response.status_code in [200, 201, 204]:
            try:
                response_json = response.json()
                logger.info(f"✅ TASK STATUS: Successfully updated via direct API")
                return jsonify({
                    'success': True,
                    'data': response_json,
                    'method': 'direct_api'
                })
            except ValueError:
                # Non-JSON response but successful HTTP status
                logger.info(f"✅ TASK STATUS: Successfully updated (non-JSON response)")
                return jsonify({
                    'success': True,
                    'message': f'Task {task_wonum} status updated to {new_status}',
                    'method': 'direct_api'
                })
        else:
            logger.error(f"❌ TASK STATUS: Direct API failed with status {response.status_code}")
            return jsonify({
                'success': False,
                'error': f'HTTP {response.status_code}',
                'details': response.text[:200],
                'method': 'direct_api'
            })

    except Exception as e:
        logger.error(f"Error updating task status: {str(e)}")
        return jsonify({'success': False, 'error': str(e)})

# Complete MXAPIWODETAIL API Endpoints - All Methods Available
@app.route('/api/mxapiwodetail/methods', methods=['GET'])
def get_available_methods():
    """Get list of all available MXAPIWODETAIL methods"""
    return jsonify({
        'success': True,
        'methods': mxapi_service.available_methods,
        'status_transitions': mxapi_service.status_transitions
    })

@app.route('/api/mxapiwodetail/<wonum>/execute/<method_name>', methods=['POST'])
def execute_workorder_method(wonum, method_name):
    """Execute any MXAPIWODETAIL method on a specific work order"""
    try:
        data = request.get_json() or {}
        result = mxapi_service.execute_wsmethod(method_name, wonum=wonum, data=data)
        return jsonify(result)
    except Exception as e:
        logger.error(f"Error executing {method_name} on {wonum}: {str(e)}")
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/mxapiwodetail/bulk/<method_name>', methods=['POST'])
def execute_bulk_workorder_method(method_name):
    """Execute any MXAPIWODETAIL method on multiple work orders (bulk operation)"""
    try:
        data = request.get_json()
        if not isinstance(data, list):
            return jsonify({'success': False, 'error': 'Bulk operations require an array of work order data'})

        # For bulk operations, we need to add href to each work order if not present
        enhanced_data = []
        for item in data:
            if 'wonum' in item and 'href' not in item:
                # Get the resource ID for this work order
                wonum = item['wonum']
                resource_id = mxapi_service.get_workorder_resource_id(wonum)
                if resource_id:
                    # Add href to the item
                    item['href'] = f"{mxapi_service.token_manager.base_url}/oslc/os/mxapiwodetail/{resource_id}"
                    logger.info(f"🔗 BULK: Added href for {wonum}: {item['href']}")
                else:
                    logger.warning(f"⚠️ BULK: Could not get resource ID for {wonum}")
            enhanced_data.append(item)

        result = mxapi_service.execute_wsmethod(method_name, data=enhanced_data, bulk=True)
        return jsonify(result)
    except Exception as e:
        logger.error(f"Error executing bulk {method_name}: {str(e)}")
        return jsonify({'success': False, 'error': str(e)})

# Specific method endpoints for common operations
@app.route('/api/workorder/<wonum>/approve', methods=['POST'])
def approve_workorder(wonum):
    """Approve a work order"""
    try:
        data = request.get_json() or {}
        result = mxapi_service.execute_wsmethod('approve', wonum=wonum, data=data)
        return jsonify(result)
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/workorder/<wonum>/start', methods=['POST'])
def start_workorder(wonum):
    """Start a work order (set to INPRG)"""
    try:
        data = request.get_json() or {}
        result = mxapi_service.execute_wsmethod('start', wonum=wonum, data=data)
        return jsonify(result)
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/workorder/<wonum>/complete', methods=['POST'])
def complete_workorder(wonum):
    """Complete a work order (set to COMP)"""
    try:
        data = request.get_json() or {}
        result = mxapi_service.execute_wsmethod('complete', wonum=wonum, data=data)
        return jsonify(result)
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/workorder/<wonum>/close', methods=['POST'])
def close_workorder(wonum):
    """Close a work order (set to CLOSE)"""
    try:
        data = request.get_json() or {}
        result = mxapi_service.execute_wsmethod('close', wonum=wonum, data=data)
        return jsonify(result)
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/workorder/<wonum>/cancel', methods=['POST'])
def cancel_workorder(wonum):
    """Cancel a work order (set to CAN)"""
    try:
        data = request.get_json() or {}
        result = mxapi_service.execute_wsmethod('cancel', wonum=wonum, data=data)
        return jsonify(result)
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/workorder/<wonum>/assign', methods=['POST'])
def assign_workorder(wonum):
    """Assign a work order to a person or group"""
    try:
        data = request.get_json() or {}
        result = mxapi_service.execute_wsmethod('assign', wonum=wonum, data=data)
        return jsonify(result)
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/workorder/<wonum>/duplicate', methods=['POST'])
def duplicate_workorder(wonum):
    """Duplicate a work order"""
    try:
        data = request.get_json() or {}
        result = mxapi_service.execute_wsmethod('duplicate', wonum=wonum, data=data)
        return jsonify(result)
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/workorder/<wonum>/route', methods=['POST'])
def route_workorder(wonum):
    """Route a work order"""
    try:
        data = request.get_json() or {}
        result = mxapi_service.execute_wsmethod('route', wonum=wonum, data=data)
        return jsonify(result)
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

# Enhanced status change with validation
@app.route('/api/workorder/<wonum>/change-status', methods=['POST'])
def change_workorder_status(wonum):
    """Change work order status with validation and signature enforcement"""
    try:
        data = request.get_json()
        if not data or 'status' not in data:
            return jsonify({'success': False, 'error': 'Status is required'})

        new_status = data['status']
        wo_type = data.get('wo_type', 'parent')  # 'parent' or 'task'
        bypass_signature = data.get('bypass_signature', False)  # For signature submission

        # Add status validation logic here if needed
        valid_statuses = ['WAPPR', 'APPR', 'ASSIGN', 'INPRG', 'COMP', 'CLOSE', 'CAN', 'READY', 'PACK', 'DEFER',
                         'WGOVT', 'AWARD', 'MTLCXD', 'MTLISD', 'PISSUE', 'RTI', 'WMATL', 'WSERV', 'WSCH', 'SET']
        if new_status not in valid_statuses:
            return jsonify({'success': False, 'error': f'Invalid status: {new_status}'})

        # Check if signature is required (unless bypassing for signature submission)
        if not bypass_signature and is_signature_required(new_status, wo_type):
            logger.info(f"📝 SIGNATURE: Signature required for WO {wonum} status change to {new_status}")
            return jsonify({
                'success': False,
                'signature_required': True,
                'status': new_status,
                'wo_type': wo_type,
                'message': f'Digital signature required for status change to {new_status}'
            })

        # Proceed with status change
        result = mxapi_service.execute_wsmethod('changeStatus', wonum=wonum, data={'status': new_status})

        if result.get('success'):
            logger.info(f"✅ STATUS CHANGE: WO {wonum} -> {new_status} (signature: {'required' if not bypass_signature else 'captured'})")

        return jsonify(result)
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

# Get tasks for a work order
@app.route('/api/workorder/<wonum>/tasks', methods=['GET'])
def get_workorder_tasks(wonum):
    """Get all tasks for a specific work order"""
    try:
        # Check if user is logged in
        if not hasattr(token_manager, 'username') or not token_manager.username:
            return jsonify({'success': False, 'error': 'Not logged in'})

        logger.info(f"🔍 TASKS: Fetching tasks for work order {wonum}")

        # Get tasks using the enhanced service
        base_url = getattr(enhanced_workorder_service.token_manager, 'base_url', '')
        api_url = f"{base_url}/oslc/os/mxapiwodetail"

        # Query for tasks (istask=1) that belong to this work order
        params = {
            "oslc.select": "wonum,description,status,siteid,priority,worktype,assignedto,location,assetnum,schedstart,estdur,parent,istask,taskid",
            "oslc.where": f'parent="{wonum}" and istask=1',
            "oslc.pageSize": "50"
        }

        response = enhanced_workorder_service.token_manager.session.get(
            api_url, params=params, timeout=(5.0, 15)
        )

        if response.status_code == 200:
            data = response.json()
            tasks = data.get('member', [])

            logger.info(f"🔍 TASKS: Found {len(tasks)} tasks for work order {wonum}")

            # Clean and format task data
            cleaned_tasks = []
            for task in tasks:
                cleaned_task = {
                    'wonum': task.get('wonum', ''),
                    'description': task.get('description', ''),
                    'owner': task.get('owner', ''),
                    'owner_group': task.get('ownergroup', ''),
                    'lead': task.get('lead', ''),
                    'supervisor': task.get('supervisor', ''),
                    'crew': task.get('crew', ''),
                    'persongroup': task.get('persongroup', ''),
                    'parent': task.get('parent', ''),
                    'istask': task.get('istask', 0),
                    'status': task.get('status', ''),
                    'status_description': task.get('status_description', ''),
                    'siteid': task.get('siteid', ''),
                    'priority': task.get('priority', ''),
                    'worktype': task.get('worktype', ''),
                    'assignedto': task.get('assignedto', ''),
                    'location': task.get('location', ''),
                    'assetnum': task.get('assetnum', ''),
                    'schedstart': task.get('schedstart', ''),
                    'estdur': task.get('estdur', ''),
                    'taskid': task.get('taskid', ''),
                    'istask': task.get('istask', 0)
                }
                cleaned_tasks.append(cleaned_task)

            return jsonify({
                'success': True,
                'tasks': cleaned_tasks,
                'count': len(cleaned_tasks),
                'parent_wonum': wonum
            })
        else:
            logger.error(f"Failed to fetch tasks: {response.status_code}")
            return jsonify({
                'success': False,
                'error': f'Failed to fetch tasks: {response.status_code}',
                'tasks': []
            })

    except Exception as e:
        logger.error(f"Error fetching tasks for {wonum}: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e),
            'tasks': []
        })

# API Documentation Page
@app.route('/api-docs')
def api_documentation():
    """Complete API documentation for all MXAPIWODETAIL endpoints"""
    html = """
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Complete MXAPIWODETAIL API Documentation</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
        <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
        <style>
            body { background-color: #f8f9fa; }
            .api-header { background: linear-gradient(135deg, #007bff, #0056b3); color: white; padding: 30px 0; }
            .endpoint-card { margin: 20px 0; border-left: 4px solid #007bff; }
            .method-badge { font-size: 12px; padding: 4px 8px; border-radius: 4px; }
            .method-get { background: #28a745; color: white; }
            .method-post { background: #007bff; color: white; }
            .code-block { background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 4px; padding: 15px; margin: 10px 0; }
            .status-transition { background: #e9ecef; padding: 10px; border-radius: 4px; margin: 5px 0; }
        </style>
    </head>
    <body>
        <div class="api-header">
            <div class="container">
                <h1><i class="fas fa-code me-3"></i>Complete MXAPIWODETAIL API Documentation</h1>
                <p class="lead">All available Maximo Work Order API endpoints and methods</p>
            </div>
        </div>

        <div class="container mt-4">
            <!-- Overview Section -->
            <div class="card endpoint-card">
                <div class="card-header">
                    <h3><i class="fas fa-info-circle me-2"></i>API Overview</h3>
                </div>
                <div class="card-body">
                    <p>This API provides complete access to all IBM Maximo MXAPIWODETAIL methods and actions.
                    It supports both individual work order operations and bulk operations.</p>

                    <h5>Authentication</h5>
                    <p>All endpoints require user authentication. Make sure you're logged in before making API calls.</p>

                    <h5>Base URL</h5>
                    <div class="code-block">
                        <code>https://your-domain.com/api/</code>
                    </div>
                </div>
            </div>

            <!-- Available Methods -->
            <div class="card endpoint-card">
                <div class="card-header">
                    <h3><i class="fas fa-list me-2"></i>Available Methods</h3>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h5>Status Management</h5>
                            <ul>
                                <li><strong>changeStatus</strong> - Change work order status</li>
                                <li><strong>approve</strong> - Approve work order</li>
                                <li><strong>start</strong> - Start work order (set to INPRG)</li>
                                <li><strong>complete</strong> - Complete work order (set to COMP)</li>
                                <li><strong>close</strong> - Close work order (set to CLOSE)</li>
                                <li><strong>cancel</strong> - Cancel work order (set to CAN)</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h5>Work Order Management</h5>
                            <ul>
                                <li><strong>assign</strong> - Assign work order</li>
                                <li><strong>unassign</strong> - Unassign work order</li>
                                <li><strong>duplicate</strong> - Duplicate work order</li>
                                <li><strong>route</strong> - Route work order</li>
                                <li><strong>plan</strong> - Plan work order</li>
                                <li><strong>schedule</strong> - Schedule work order</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Status Transitions -->
            <div class="card endpoint-card">
                <div class="card-header">
                    <h3><i class="fas fa-exchange-alt me-2"></i>Status Transitions</h3>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="status-transition">
                                <strong>WAPPR (Waiting for Approval)</strong><br>
                                Can transition to: APPR, CAN
                            </div>
                            <div class="status-transition">
                                <strong>APPR (Approved)</strong><br>
                                Can transition to: INPRG, CAN, CLOSE
                            </div>
                            <div class="status-transition">
                                <strong>ASSIGN (Assigned)</strong><br>
                                Can transition to: INPRG, APPR, CAN
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="status-transition">
                                <strong>INPRG (In Progress)</strong><br>
                                Can transition to: COMP, CAN, CLOSE
                            </div>
                            <div class="status-transition">
                                <strong>COMP (Complete)</strong><br>
                                Can transition to: CLOSE, INPRG
                            </div>
                            <div class="status-transition">
                                <strong>CLOSE/CAN (Final States)</strong><br>
                                No further transitions allowed
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Core Endpoints -->
            <div class="card endpoint-card">
                <div class="card-header">
                    <h3><i class="fas fa-cogs me-2"></i>Core API Endpoints</h3>
                </div>
                <div class="card-body">

                    <!-- Get Methods -->
                    <div class="mb-4">
                        <h5><span class="method-badge method-get">GET</span> /api/mxapiwodetail/methods</h5>
                        <p>Get list of all available MXAPIWODETAIL methods and status transitions</p>
                        <div class="code-block">
                            <strong>Response:</strong><br>
                            <code>{
  "success": true,
  "methods": {...},
  "status_transitions": {...}
}</code>
                        </div>
                    </div>

                    <!-- Execute Method -->
                    <div class="mb-4">
                        <h5><span class="method-badge method-post">POST</span> /api/mxapiwodetail/{wonum}/execute/{method_name}</h5>
                        <p>Execute any MXAPIWODETAIL method on a specific work order</p>
                        <div class="code-block">
                            <strong>Request Body:</strong><br>
                            <code>{
  "status": "INPRG",
  "memo": "Starting work",
  "assignedto": "JOHN.DOE"
}</code>
                        </div>
                    </div>

                    <!-- Bulk Operations -->
                    <div class="mb-4">
                        <h5><span class="method-badge method-post">POST</span> /api/mxapiwodetail/bulk/{method_name}</h5>
                        <p>Execute any MXAPIWODETAIL method on multiple work orders (bulk operation)</p>
                        <div class="code-block">
                            <strong>Request Body:</strong><br>
                            <code>[
  {"wonum": "WO001", "status": "INPRG"},
  {"wonum": "WO002", "status": "COMP"},
  {"wonum": "WO003", "status": "CLOSE"}
]</code>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Specific Method Endpoints -->
            <div class="card endpoint-card">
                <div class="card-header">
                    <h3><i class="fas fa-tools me-2"></i>Specific Method Endpoints</h3>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h5>Status Change Operations</h5>
                            <ul class="list-unstyled">
                                <li><span class="method-badge method-post">POST</span> /api/workorder/{wonum}/approve</li>
                                <li><span class="method-badge method-post">POST</span> /api/workorder/{wonum}/start</li>
                                <li><span class="method-badge method-post">POST</span> /api/workorder/{wonum}/complete</li>
                                <li><span class="method-badge method-post">POST</span> /api/workorder/{wonum}/close</li>
                                <li><span class="method-badge method-post">POST</span> /api/workorder/{wonum}/cancel</li>
                                <li><span class="method-badge method-post">POST</span> /api/workorder/{wonum}/change-status</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h5>Work Order Operations</h5>
                            <ul class="list-unstyled">
                                <li><span class="method-badge method-post">POST</span> /api/workorder/{wonum}/assign</li>
                                <li><span class="method-badge method-post">POST</span> /api/workorder/{wonum}/duplicate</li>
                                <li><span class="method-badge method-post">POST</span> /api/workorder/{wonum}/route</li>
                                <li><span class="method-badge method-post">POST</span> /api/task/{wonum}/status</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Examples -->
            <div class="card endpoint-card">
                <div class="card-header">
                    <h3><i class="fas fa-code me-2"></i>Usage Examples</h3>
                </div>
                <div class="card-body">

                    <h5>1. Change Work Order Status</h5>
                    <div class="code-block">
                        <strong>POST</strong> /api/workorder/WO12345/change-status<br>
                        <strong>Body:</strong> {"status": "INPRG"}
                    </div>

                    <h5>2. Approve Multiple Work Orders</h5>
                    <div class="code-block">
                        <strong>POST</strong> /api/mxapiwodetail/bulk/approve<br>
                        <strong>Body:</strong> [{"wonum": "WO001"}, {"wonum": "WO002"}]
                    </div>

                    <h5>3. Assign Work Order</h5>
                    <div class="code-block">
                        <strong>POST</strong> /api/workorder/WO12345/assign<br>
                        <strong>Body:</strong> {"assignedto": "JOHN.DOE", "ownergroup": "MAINT"}
                    </div>

                    <h5>4. Update Task Status</h5>
                    <div class="code-block">
                        <strong>POST</strong> /api/task/TASK001/status<br>
                        <strong>Body:</strong> {"status": "COMP"}
                    </div>
                </div>
            </div>

            <!-- Navigation -->
            <div class="text-center mt-4 mb-4">
                <a href="/welcome" class="btn btn-primary btn-lg me-3">
                    <i class="fas fa-home me-2"></i>Back to Welcome
                </a>
                <a href="/enhanced-workorders" class="btn btn-outline-secondary btn-lg me-3">
                    <i class="fas fa-clipboard-list me-2"></i>Work Orders
                </a>
                <a href="/api-docs/mxapisite" class="btn btn-outline-info btn-lg">
                    <i class="fas fa-building me-2"></i>Site Access API
                </a>
            </div>
        </div>

        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    </body>
    </html>
    """
    return html

@app.route('/test-fresh-profile')
def test_fresh_profile():
    """Test fresh profile retrieval with current session."""
    try:
        # Use the existing token manager session with cookies
        if not hasattr(token_manager, 'session'):
            return "<h1>No session available</h1>"

        # Get fresh profile data directly
        profile_url = f"{token_manager.base_url}/oslc/whoami"
        profile_response = token_manager.session.get(profile_url, timeout=30)

        html = f"""
        <h1>Fresh Profile Test</h1>
        <h2>Direct Profile API Call</h2>
        <p><strong>URL:</strong> {profile_url}</p>
        <p><strong>Status:</strong> {profile_response.status_code}</p>
        """

        if profile_response.status_code == 200:
            try:
                profile_data = profile_response.json()
                html += f"<h3>Profile Data Retrieved:</h3>"
                html += f"<pre>{json.dumps(profile_data, indent=2)}</pre>"

                # Extract key fields
                html += f"<h3>Key Profile Fields:</h3>"
                html += f"<ul>"
                html += f"<li><strong>Username:</strong> {profile_data.get('userName', 'N/A')}</li>"
                html += f"<li><strong>Display Name:</strong> {profile_data.get('displayName', 'N/A')}</li>"
                html += f"<li><strong>Default Site:</strong> {profile_data.get('defaultSite', 'N/A')}</li>"
                html += f"<li><strong>Insert Site:</strong> {profile_data.get('insertSite', 'N/A')}</li>"
                html += f"</ul>"

            except Exception as e:
                html += f"<p><strong>Parse Error:</strong> {str(e)}</p>"
                html += f"<p><strong>Raw Response:</strong> {profile_response.text[:1000]}</p>"
        else:
            html += f"<p><strong>Error:</strong> {profile_response.text[:500]}</p>"

        html += """
        <p><a href="/enhanced-profile">Back to Enhanced Profile</a></p>
        <p><a href="/welcome">Back to Welcome</a></p>
        """

        return html

    except Exception as e:
        return f"<h1>Fresh Profile Test Error: {str(e)}</h1><p><a href='/welcome'>Back to Welcome</a></p>"

@app.route('/direct-workorders')
def direct_workorders():
    """Direct work order fetch using current session cookies."""
    if 'username' not in session:
        flash('Please login first', 'error')
        return redirect(url_for('index'))

    try:
        # Use the existing token manager session with cookies
        if not hasattr(token_manager, 'session'):
            return "<h1>No session available</h1>"

        # Get all sites first to find the correct one
        sites_url = f"{token_manager.base_url}/oslc/sites"
        sites_response = token_manager.session.get(sites_url, timeout=30)

        html = f"""
        <h1>Direct Work Order Fetch</h1>
        <h2>Step 1: Sites API Call</h2>
        <p><strong>URL:</strong> {sites_url}</p>
        <p><strong>Status:</strong> {sites_response.status_code}</p>
        """

        available_sites = []
        if sites_response.status_code == 200:
            try:
                sites_data = sites_response.json()
                if isinstance(sites_data, dict) and 'member' in sites_data:
                    available_sites = [site.get('siteid', 'Unknown') for site in sites_data['member'][:10]]
                    html += f"<p><strong>Available Sites:</strong> {', '.join(available_sites)}</p>"
                else:
                    html += f"<p><strong>Sites Response:</strong> {sites_response.text[:500]}</p>"
            except Exception as e:
                html += f"<p><strong>Sites Parse Error:</strong> {str(e)}</p>"
        else:
            html += f"<p><strong>Sites Error:</strong> {sites_response.text[:500]}</p>"

        # Try work orders with different site filters
        wo_url = f"{token_manager.base_url}/oslc/os/mxapiwodetail"

        # Test different site filters
        test_sites = available_sites[:3] if available_sites else ['IKWAJ', 'BEDFORD', 'CENTRAL']

        for test_site in test_sites:
            html += f"<h2>Step 2: Work Orders for Site {test_site}</h2>"

            params = {
                "oslc.select": "wonum,description,status,siteid,priority,worktype",
                "oslc.where": f"siteid=\"{test_site}\"",
                "oslc.pageSize": "5"
            }

            wo_response = token_manager.session.get(wo_url, params=params, timeout=30)
            html += f"<p><strong>Status:</strong> {wo_response.status_code}</p>"

            if wo_response.status_code == 200:
                try:
                    wo_data = wo_response.json()
                    if isinstance(wo_data, dict) and 'member' in wo_data:
                        workorders = wo_data['member']
                        html += f"<p><strong>Found:</strong> {len(workorders)} work orders</p>"

                        if workorders:
                            html += "<table border='1'><tr><th>WO Number</th><th>Description</th><th>Status</th><th>Site</th></tr>"
                            for wo in workorders[:5]:
                                html += f"""
                                <tr>
                                    <td>{wo.get('wonum', 'N/A')}</td>
                                    <td>{wo.get('description', 'N/A')[:50]}</td>
                                    <td>{wo.get('status', 'N/A')}</td>
                                    <td>{wo.get('siteid', 'N/A')}</td>
                                </tr>
                                """
                            html += "</table>"
                            break  # Found work orders, stop testing
                    else:
                        html += f"<p><strong>Response:</strong> {wo_response.text[:300]}</p>"
                except Exception as e:
                    html += f"<p><strong>Parse Error:</strong> {str(e)}</p>"
            else:
                html += f"<p><strong>Error:</strong> {wo_response.text[:300]}</p>"

        html += """
        <p><a href="/enhanced-workorders">Back to Enhanced Work Orders</a></p>
        <p><a href="/force-fresh-login">Force Fresh Login</a></p>
        <p><a href="/welcome">Back to Welcome</a></p>
        """

        return html

    except Exception as e:
        return f"<h1>Direct Test Error: {str(e)}</h1><p><a href='/welcome'>Back to Welcome</a></p>"

@app.route('/api/enhanced-workorders/available-sites', methods=['GET'])
def api_get_available_sites():
    """API endpoint for getting user's available sites for work order search."""
    if 'username' not in session:
        return jsonify({'error': 'Not authenticated'}), 401

    # Verify session
    if not enhanced_workorder_service.is_session_valid():
        return jsonify({'error': 'Session expired'}), 401

    try:
        # Get available sites from enhanced profile service
        sites = enhanced_profile_service.get_available_sites(use_cache=True)

        # Get user's default site for pre-selection
        user_profile = enhanced_profile_service.get_user_profile()
        default_site = user_profile.get('defaultSite', '') if user_profile else ''



        logger.info(f"🏢 API SITES: Retrieved {len(sites)} available sites for work order search")

        return jsonify({
            'success': True,
            'sites': sites,
            'default_site': default_site,
            'total_count': len(sites)
        })

    except Exception as e:
        logger.error(f"Error fetching available sites: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/enhanced-workorders/search', methods=['POST'])
def api_search_workorders():
    """API endpoint for searching work orders with pagination and filtering."""
    if 'username' not in session:
        return jsonify({'error': 'Not authenticated'}), 401

    # Verify session
    if not enhanced_workorder_service.is_session_valid():
        return jsonify({'error': 'Session expired'}), 401

    try:
        data = request.get_json() or {}

        # Extract search parameters
        search_criteria = data.get('search_criteria', {})
        page = data.get('page', 1)
        page_size = data.get('page_size', 20)

        # Validate page size (max 50 for performance)
        page_size = min(max(page_size, 1), 50)

        logger.info(f"🔍 API SEARCH: Criteria: {search_criteria}, Page: {page}, Size: {page_size}")

        # Execute search
        result = enhanced_workorder_service.search_workorders(
            search_criteria=search_criteria,
            page=page,
            page_size=page_size
        )

        return jsonify(result)

    except Exception as e:
        logger.error(f"Error in workorder search API: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/enhanced-workorder-details/<wonum>')
def enhanced_workorder_details(wonum):
    """Display detailed information for a specific work order using enhanced service."""
    # Just redirect to the working workorder_detail route
    return redirect(url_for('workorder_detail', wonum=wonum))


# Complete MXAPIWODETAIL Service Implementation

@app.route('/update-default-site', methods=['POST'])
def update_default_site():
    """Update the user's default site or insert site."""
    if 'username' not in session:
        return jsonify({'status': 'error', 'message': 'Not logged in'})

    # Verify that we're still logged in
    if not token_manager.is_logged_in():
        return jsonify({'status': 'error', 'message': 'Session expired'})

    site_id = request.form.get('site_id')
    if not site_id:
        return jsonify({'status': 'error', 'message': 'No site ID provided'})

    # Determine which site type to update (default or insert)
    site_type = request.form.get('site_type', 'default')
    site_type_display = "insert site" if site_type == 'insert' else "default site"

    # Make an API call to update the user's site setting
    try:
        # Construct the API URL for updating the user's profile
        update_url = f"{token_manager.base_url}/oslc/userprofile"

        # Prepare the update data based on site type
        # Note: We still need to use the spi: prefix for the API call
        if site_type == 'insert':
            update_data = {
                "spi:insertSite": site_id
            }
            # Also update the session
            session['insert_site'] = site_id
        else:
            update_data = {
                "spi:defaultSite": site_id
            }
            # Also update the session
            session['default_site'] = site_id

        logger.info(f"Updating {site_type_display} to {site_id}")

        # Make the API call with PATCH first (most efficient)
        try:
            response = token_manager.session.patch(
                update_url,
                json=update_data,
                headers={"Content-Type": "application/json"},
                timeout=(3.05, 10)  # Reduced timeout
            )

            # If successful, return immediately
            if response.status_code in [200, 201, 204]:
                logger.info(f"Successfully updated {site_type_display} to {site_id} with PATCH")

                # Force a refresh of the sites cache
                try:
                    token_manager.get_available_sites(use_mock=False, use_cache=False, force_refresh=True)
                except Exception as cache_error:
                    logger.warning(f"Error refreshing sites cache: {cache_error}")

                return jsonify({
                    'status': 'success',
                    'message': f'{site_type_display.capitalize()} updated to {site_id}',
                    'site_id': site_id,
                    'site_type': site_type
                })
        except Exception as patch_error:
            logger.warning(f"PATCH request failed: {patch_error}")

        # If PATCH failed, try PUT with minimal data
        try:
            # We don't need the full profile, just the essential fields
            # Note: We still need to use the spi: prefix for the API call
            minimal_profile = {
                "spi:userName": session['username']
            }

            # Add the site field we're updating
            if site_type == 'insert':
                minimal_profile["spi:insertSite"] = site_id
            else:
                minimal_profile["spi:defaultSite"] = site_id

            response = token_manager.session.put(
                update_url,
                json=minimal_profile,
                headers={"Content-Type": "application/json"},
                timeout=(3.05, 10)  # Reduced timeout
            )

            if response.status_code in [200, 201, 204]:
                logger.info(f"Successfully updated {site_type_display} to {site_id} with PUT")

                # Force a refresh of the sites cache
                try:
                    token_manager.get_available_sites(use_mock=False, use_cache=False, force_refresh=True)
                except Exception as cache_error:
                    logger.warning(f"Error refreshing sites cache: {cache_error}")

                return jsonify({
                    'status': 'success',
                    'message': f'{site_type_display.capitalize()} updated to {site_id}',
                    'site_id': site_id,
                    'site_type': site_type
                })
            else:
                logger.error(f"Failed to update {site_type_display}. Status code: {response.status_code}")
                return jsonify({
                    'status': 'error',
                    'message': f'Failed to update {site_type_display}. Status code: {response.status_code}'
                })
        except Exception as put_error:
            logger.error(f"PUT request failed: {put_error}")
            raise  # Re-raise to be caught by the outer exception handler

    except Exception as e:
        logger.error(f"Error updating {site_type_display}: {e}")
        return jsonify({
            'status': 'error',
            'message': f'Error updating {site_type_display}: {str(e)}'
        })

@app.route('/sync')
def sync():
    """Render the database sync page."""
    if 'username' not in session:
        flash('Please login first', 'error')
        return redirect(url_for('index'))

    # Verify that we're still logged in
    if not token_manager.is_logged_in():
        flash('Your session has expired. Please login again.', 'warning')
        session.clear()
        return redirect(url_for('index'))

    return render_template('sync.html')

@app.route('/test-profile')
def test_profile():
    """Simple test page to view and refresh profile data."""
    if 'username' not in session:
        flash('Please login first', 'error')
        return redirect(url_for('index'))

    try:
        # Get current profile data
        user_profile = enhanced_profile_service.get_user_profile()

        # Get session data
        session_data = {
            'username': session.get('username', ''),
            'default_site': session.get('default_site', ''),
            'insert_site': session.get('insert_site', ''),
            'first_name': session.get('first_name', ''),
            'last_name': session.get('last_name', '')
        }

        # Create simple HTML page with refresh button
        html = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>Profile Test - {session['username']}</title>
            <style>
                body {{ font-family: Arial, sans-serif; margin: 20px; }}
                .profile-box {{ border: 1px solid #ccc; padding: 15px; margin: 10px 0; }}
                .refresh-btn {{ background: #007bff; color: white; padding: 10px 20px; border: none; cursor: pointer; }}
                .refresh-btn:hover {{ background: #0056b3; }}
                .success {{ color: green; }}
                .error {{ color: red; }}
            </style>
        </head>
        <body>
            <h1>Profile Test for {session['username']}</h1>

            <div class="profile-box">
                <h3>Current Session Data:</h3>
                <p><strong>Username:</strong> {session_data['username']}</p>
                <p><strong>Default Site:</strong> {session_data['default_site']}</p>
                <p><strong>Insert Site:</strong> {session_data['insert_site']}</p>
                <p><strong>First Name:</strong> {session_data['first_name']}</p>
                <p><strong>Last Name:</strong> {session_data['last_name']}</p>
            </div>

            <div class="profile-box">
                <h3>Current Profile Service Data:</h3>
                <p><strong>Default Site:</strong> {user_profile.get('defaultSite', 'None') if user_profile else 'No profile data'}</p>
                <p><strong>Insert Site:</strong> {user_profile.get('insertSite', 'None') if user_profile else 'No profile data'}</p>
                <p><strong>First Name:</strong> {user_profile.get('firstName', 'None') if user_profile else 'No profile data'}</p>
                <p><strong>Last Name:</strong> {user_profile.get('lastName', 'None') if user_profile else 'No profile data'}</p>
            </div>

            <button class="refresh-btn" onclick="refreshProfile()">🔄 Refresh Profile from Maximo</button>
            <div id="result"></div>

            <p><a href="/welcome">← Back to Welcome</a></p>

            <script>
                function refreshProfile() {{
                    document.getElementById('result').innerHTML = '<p>Refreshing profile...</p>';

                    fetch('/api/refresh-profile', {{
                        method: 'POST',
                        headers: {{
                            'Content-Type': 'application/json',
                        }}
                    }})
                    .then(response => response.json())
                    .then(data => {{
                        if (data.success) {{
                            document.getElementById('result').innerHTML =
                                '<p class="success">✅ Profile refreshed successfully!</p>' +
                                '<p>New Default Site: ' + data.defaultSite + '</p>' +
                                '<p>New Insert Site: ' + data.insertSite + '</p>' +
                                '<p><em>Refresh this page to see updated data</em></p>';
                        }} else {{
                            document.getElementById('result').innerHTML =
                                '<p class="error">❌ Error: ' + data.error + '</p>';
                        }}
                    }})
                    .catch(error => {{
                        document.getElementById('result').innerHTML =
                            '<p class="error">❌ Network error: ' + error + '</p>';
                    }});
                }}
            </script>
        </body>
        </html>
        """

        return html

    except Exception as e:
        logger.error(f"Error in test profile page: {e}")
        return f"<h1>Error: {str(e)}</h1><p><a href='/welcome'>Back to Welcome</a></p>"

@app.route('/api/enhanced-profile', methods=['GET'])
def api_enhanced_profile():
    """API endpoint to get user profile data from Enhanced Profile service."""
    if 'username' not in session:
        return jsonify({'success': False, 'error': 'Not logged in'})

    try:
        # Get user profile from Enhanced Profile service
        profile = enhanced_profile_service.get_user_profile()

        if profile:
            return jsonify(profile)
        else:
            return jsonify({
                'success': False,
                'error': 'Failed to get profile'
            })
    except Exception as e:
        logger.error(f"Error getting profile via API: {e}")
        return jsonify({
            'success': False,
            'error': f'Error getting profile: {str(e)}'
        })

@app.route('/api/refresh-profile', methods=['POST'])
def refresh_profile():
    """API endpoint to refresh user profile data (for site changes)."""
    if 'username' not in session:
        return jsonify({'success': False, 'error': 'Not logged in'})

    try:
        username = session['username']
        logger.info(f"🔄 PROFILE REFRESH: Refreshing profile for user {username}")

        # Clear all profile caches
        if 'enhanced_profile_service' in globals():
            enhanced_profile_service.invalidate_user_profile_cache(username)
            logger.info("✅ PROFILE REFRESH: Enhanced profile cache invalidated")

        if 'enhanced_workorder_service' in globals():
            enhanced_workorder_service.clear_cache('all')
            logger.info("✅ PROFILE REFRESH: Work order cache cleared")

        # Clear token manager profile cache
        if hasattr(token_manager, '_clear_profile_cache'):
            token_manager._clear_profile_cache(username)
            logger.info("✅ PROFILE REFRESH: Token manager profile cache cleared")

        # Force fresh profile fetch
        fresh_profile = None
        if 'enhanced_profile_service' in globals():
            fresh_profile = enhanced_profile_service.force_profile_refresh(username)

        if not fresh_profile:
            # Fallback to token manager
            fresh_profile = token_manager.get_user_profile(use_mock=False, use_cache=False, force_refresh=True)

        if fresh_profile:
            # Update session with fresh profile data
            session['default_site'] = fresh_profile.get('defaultSite', '')
            session['insert_site'] = fresh_profile.get('insertSite', '')
            session['first_name'] = fresh_profile.get('firstName', '')
            session['last_name'] = fresh_profile.get('lastName', '')

            logger.info(f"✅ PROFILE REFRESH: Fresh profile loaded - default site: {fresh_profile.get('defaultSite', 'None')}")
            return jsonify({
                'success': True,
                'message': 'Profile refreshed successfully',
                'defaultSite': fresh_profile.get('defaultSite', ''),
                'insertSite': fresh_profile.get('insertSite', '')
            })
        else:
            logger.error("Failed to fetch fresh profile data")
            return jsonify({'success': False, 'error': 'Failed to refresh profile data'})

    except Exception as e:
        logger.error(f"Error refreshing profile: {e}")
        return jsonify({'success': False, 'error': str(e)})

@app.route('/logout')
def logout():
    """Handle logout with complete profile and cache cleanup including disk files."""
    username = session.get('username', 'User')

    # Clear session first
    session.clear()

    # Clear all profile and work order caches (memory + disk)
    try:
        # Clear enhanced service caches (memory + disk)
        if 'enhanced_profile_service' in globals():
            enhanced_profile_service.clear_cache('all')
            # Force clear disk cache for current user
            enhanced_profile_service.invalidate_user_profile_cache(username)
            logger.info("✅ LOGOUT: Enhanced profile service cache cleared (memory + disk)")

        if 'enhanced_workorder_service' in globals():
            enhanced_workorder_service.clear_cache('all')
            logger.info("✅ LOGOUT: Enhanced work order service cache cleared")

        # Clear token manager profile cache (memory + disk)
        if hasattr(token_manager, '_clear_profile_cache'):
            token_manager._clear_profile_cache(username)
            logger.info("✅ LOGOUT: Token manager profile cache cleared (memory + disk)")

        # Clear ALL disk cache files to prevent cross-user contamination
        import os
        import glob
        cache_patterns = [
            'cache/profile_*.pkl',
            'cache/enhanced_profile_*.pkl',
            'cache/workorder_*.pkl',
            'cache/sites_*.pkl'
        ]

        for pattern in cache_patterns:
            for cache_file in glob.glob(pattern):
                try:
                    os.remove(cache_file)
                    logger.info(f"✅ LOGOUT: Removed disk cache file: {cache_file}")
                except Exception as e:
                    logger.warning(f"Could not remove cache file {cache_file}: {e}")

    except Exception as e:
        logger.warning(f"Error clearing caches during logout: {e}")

    # Logout from token manager (clears tokens and session cookies)
    token_manager.logout()

    logger.info(f"✅ LOGOUT: User {username} logged out with COMPLETE cache cleanup (memory + all disk files)")
    flash(f'{username} has been logged out', 'info')
    return redirect(url_for('index'))

class MXAPISTEService:
    """Service class for handling MXAPISTE operations"""

    def __init__(self, token_manager):
        self.token_manager = token_manager
        self.logger = logging.getLogger(__name__)

        # Available WSMethods for Sites
        self.available_methods = {
            'getSiteInfo': 'Get site information',
            'getSiteHierarchy': 'Get site hierarchy',
            'getSiteAssets': 'Get assets associated with site',
            'getSiteLocations': 'Get locations in site',
            'getSiteWorkOrders': 'Get work orders for site',
            'getSiteInventory': 'Get inventory for site',
            'getSitePersonnel': 'Get personnel assigned to site',
            'getSiteEquipment': 'Get equipment in site',
            'getSiteDocuments': 'Get documents associated with site',
            'getSiteHistory': 'Get site history'
        }

    def get_api_url(self, action=None, resource_id=None):
        """Get the correct API URL for mxapiste operations using session authentication"""
        base_url = f"{self.token_manager.base_url}/oslc/os/mxapiste"

        if resource_id:
            base_url += f"/{resource_id}"

        if action:
            base_url += f"?action={action}"

        return base_url

    def get_headers(self, method_override=None):
        """Get standard headers for API requests using session authentication"""
        headers = {
            "Accept": "application/json",
            "Content-Type": "application/json"
        }

        if method_override:
            headers["X-method-override"] = method_override

        return headers

    def get_site_resource_id(self, siteid):
        """Get the resource ID for a site by querying the API"""
        try:
            # Query the site to get its resource ID
            api_url = self.get_api_url()
            params = {
                "oslc.select": "siteid,rdf:about",
                "oslc.where": f'siteid="{siteid}"',
                "oslc.pageSize": "1"
            }

            self.logger.info(f"🔍 MXAPI: Querying resource ID for site {siteid}")
            self.logger.info(f"🔍 MXAPI: URL: {api_url}")
            self.logger.info(f"🔍 MXAPI: Params: {params}")

            response = self.token_manager.session.get(
                api_url,
                params=params,
                timeout=(5.0, 15)
            )

            self.logger.info(f"🔍 MXAPI: Resource lookup response status: {response.status_code}")
            self.logger.info(f"🔍 MXAPI: Resource lookup response content: {response.text[:500]}")

            if response.status_code == 200:
                try:
                    data = response.json()
                    if 'member' in data and len(data['member']) > 0:
                        return data['member'][0]['rdf:about'].split('/')[-1]
                except (KeyError, IndexError, ValueError) as e:
                    self.logger.error(f"Error parsing site resource ID: {str(e)}")
            return None
        except Exception as e:
            self.logger.error(f"Error getting site resource ID: {str(e)}")
            return None

    def execute_wsmethod(self, method_name, siteid=None, data=None, bulk=False):
        """Execute a WSMethod on site(s)"""
        try:
            if method_name not in self.available_methods:
                return {
                    'success': False,
                    'error': f'Unknown method: {method_name}',
                    'available_methods': list(self.available_methods.keys())
                }

            # For individual site operations, we need the resource ID
            resource_id = None
            if not bulk and siteid:
                resource_id = self.get_site_resource_id(siteid)
                if not resource_id:
                    return {
                        'success': False,
                        'error': f'Could not find resource ID for site {siteid}'
                    }

            # Prepare URL and data based on operation type
            action = f"wsmethod:{method_name}"

            if bulk and isinstance(data, list):
                # Bulk operation - use collection URL with BULK header
                api_url = self.get_api_url(action=action)
                request_data = data
                headers = self.get_headers("BULK")
                self.logger.info(f"🔄 MXAPI: Bulk operation - {len(data)} sites")
            else:
                # Individual operation - use resource-specific URL
                if resource_id:
                    api_url = self.get_api_url(action=action, resource_id=resource_id)
                    request_data = data or {}
                    headers = self.get_headers("PATCH")
                    self.logger.info(f"🔄 MXAPI: Individual operation with resource ID: {resource_id}")
                else:
                    # Fallback to collection URL
                    api_url = self.get_api_url(action=action)
                    request_data = data or {}
                    if siteid:
                        request_data['siteid'] = siteid
                    headers = self.get_headers("PATCH")
                    self.logger.info(f"🔄 MXAPI: Individual operation without resource ID (fallback)")

            self.logger.info(f"🔄 MXAPI: Executing {method_name} on site(s)")
            self.logger.info(f"🔄 MXAPI: URL: {api_url}")
            self.logger.info(f"🔄 MXAPI: Data: {request_data}")
            self.logger.info(f"🔄 MXAPI: Headers: {headers}")

            # Execute request using session authentication
            response = self.token_manager.session.post(
                api_url,
                json=request_data,
                headers=headers,
                timeout=(5.0, 30)
            )

            return self._process_response(response, method_name)

        except Exception as e:
            self.logger.error(f"Error executing {method_name}: {str(e)}")
            return {'success': False, 'error': str(e)}

    def _process_response(self, response, method_name):
        """Process Maximo API response and handle errors"""
        try:
            self.logger.info(f"🔍 MXAPI RESPONSE: Status: {response.status_code}")
            self.logger.info(f"🔍 MXAPI RESPONSE: Content: {response.text}")

            if response.status_code in [200, 201, 204]:
                try:
                    response_json = response.json()

                    # Check for Maximo errors in successful HTTP responses
                    if isinstance(response_json, list):
                        errors = []
                        successes = []

                        for item in response_json:
                            if '_responsedata' in item and 'Error' in item['_responsedata']:
                                error_info = item['_responsedata']['Error']
                                errors.append({
                                    'code': error_info.get('reasonCode', 'Unknown'),
                                    'message': error_info.get('message', 'Unknown error')
                                })
                            else:
                                successes.append(item)

                        if errors:
                            self.logger.error(f"❌ MXAPI ERRORS: {errors}")
                            return {
                                'success': False,
                                'errors': errors,
                                'successes': successes,
                                'method': method_name
                            }

                    self.logger.info(f"✅ MXAPI: {method_name} executed successfully")
                    return {
                        'success': True,
                        'data': response_json,
                        'method': method_name
                    }

                except ValueError:
                    # Non-JSON response but successful HTTP status
                    return {
                        'success': True,
                        'message': f'{method_name} executed successfully',
                        'method': method_name
                    }
            else:
                return {
                    'success': False,
                    'error': f'HTTP {response.status_code}',
                    'details': response.text[:200],
                    'method': method_name
                }

        except Exception as e:
            self.logger.error(f"Error processing response: {str(e)}")
            return {'success': False, 'error': f'Response processing error: {str(e)}'}

# Initialize the MXAPISTE service
mxapiste_service = MXAPISTEService(token_manager)

# Initialize the Task Planned Materials service
from backend.services.task_planned_materials_service import TaskPlannedMaterialsService
task_materials_service = TaskPlannedMaterialsService(token_manager)

# Initialize the Task Labor service
from backend.services.task_labor_service import TaskLaborService
task_labor_service = TaskLaborService(token_manager)

# Initialize the Labor Deletion service (after task_labor_service is available)
labor_deletion_service = LaborDeletionService(token_manager, task_labor_service)

# Initialize the Inventory Search service
from backend.services.inventory_search_service import InventorySearchService
inventory_search_service = InventorySearchService(token_manager)

# Initialize the Material Request service
from backend.services.material_request_service import MaterialRequestService
material_request_service = MaterialRequestService(token_manager, task_materials_service, enhanced_profile_service, inventory_search_service)

# Initialize the Inventory Management service
from backend.services.inventory_management_service import InventoryManagementService
from backend.services.asset_related_records_service import AssetRelatedRecordsService
inventory_management_service = InventoryManagementService(token_manager)
asset_related_records_service = AssetRelatedRecordsService(token_manager)

# Now initialize asset creation service with related records service for cache invalidation
asset_creation_service = AssetCreationService(token_manager, asset_related_records_service)

# Initialize the Inventory Details service
from backend.services.inventory_details_service import InventoryDetailsService
inventory_details_service = InventoryDetailsService(token_manager)



# MXAPISTE API Endpoints
@app.route('/api/mxapiste/methods', methods=['GET'])
def get_available_site_methods():
    """Get list of all available MXAPISTE methods"""
    return jsonify({
        'success': True,
        'methods': mxapiste_service.available_methods
    })

@app.route('/api/mxapiste/<siteid>/execute/<method_name>', methods=['POST'])
def execute_site_method(siteid, method_name):
    """Execute any MXAPISTE method on a specific site"""
    try:
        data = request.get_json() or {}
        result = mxapiste_service.execute_wsmethod(method_name, siteid=siteid, data=data)
        return jsonify(result)
    except Exception as e:
        logger.error(f"Error executing {method_name} on {siteid}: {str(e)}")
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/mxapiste/bulk/<method_name>', methods=['POST'])
def execute_bulk_site_method(method_name):
    """Execute any MXAPISTE method on multiple sites (bulk operation)"""
    try:
        data = request.get_json()
        if not isinstance(data, list):
            return jsonify({'success': False, 'error': 'Bulk operations require an array of site data'})

        # For bulk operations, we need to add href to each site if not present
        enhanced_data = []
        for item in data:
            if 'siteid' in item and 'href' not in item:
                # Get the resource ID for this site
                siteid = item['siteid']
                resource_id = mxapiste_service.get_site_resource_id(siteid)
                if resource_id:
                    # Add href to the item
                    item['href'] = f"{mxapiste_service.token_manager.base_url}/oslc/os/mxapiste/{resource_id}"
                    logger.info(f"🔗 BULK: Added href for {siteid}: {item['href']}")
                else:
                    logger.warning(f"⚠️ BULK: Could not get resource ID for {siteid}")
            enhanced_data.append(item)

        result = mxapiste_service.execute_wsmethod(method_name, data=enhanced_data, bulk=True)
        return jsonify(result)
    except Exception as e:
        logger.error(f"Error executing bulk {method_name}: {str(e)}")
        return jsonify({'success': False, 'error': str(e)})

# Specific method endpoints for common operations
@app.route('/api/site/<siteid>/info', methods=['GET'])
def get_site_info(siteid):
    """Get site information"""
    try:
        # Use mxapiperuser to get site info
        api_url = f"{token_manager.base_url}/oslc/os/mxapiperuser"
        params = {
            "oslc.select": "siteid,description,status,type,address,contact,phone,email",
            "oslc.where": f'siteid="{siteid}"',
            "oslc.pageSize": "1"
        }

        response = token_manager.session.get(
            api_url,
            params=params,
            timeout=(5.0, 15)
        )

        if response.status_code == 200:
            data = response.json()
            if 'member' in data and len(data['member']) > 0:
                site_data = data['member'][0]

                # Clean and format site data
                cleaned_site = {
                    'siteid': site_data.get('siteid', ''),
                    'description': site_data.get('description', ''),
                    'status': site_data.get('status', ''),
                    'type': site_data.get('type', ''),
                    'address': site_data.get('address', ''),
                    'contact': site_data.get('contact', ''),
                    'phone': site_data.get('phone', ''),
                    'email': site_data.get('email', '')
                }

                return jsonify({
                    'success': True,
                    'data': cleaned_site
                })
            else:
                return jsonify({
                    'success': False,
                    'error': f'Could not find site {siteid}'
                })
        else:
            logger.error(f"Failed to fetch site info: {response.status_code}")
            return jsonify({
                'success': False,
                'error': f'Failed to fetch site info: {response.status_code}'
            })

    except Exception as e:
        logger.error(f"Error getting site info: {e}")
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/site/<siteid>/hierarchy', methods=['GET'])
def get_site_hierarchy(siteid):
    """Get site hierarchy"""
    try:
        result = mxapiste_service.execute_wsmethod('getSiteHierarchy', siteid=siteid)
        return jsonify(result)
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/site/<siteid>/assets', methods=['GET'])
def get_site_assets(siteid):
    """Get assets associated with site"""
    try:
        result = mxapiste_service.execute_wsmethod('getSiteAssets', siteid=siteid)
        return jsonify(result)
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/site/<siteid>/locations', methods=['GET'])
def get_site_locations(siteid):
    """Get locations in site"""
    try:
        result = mxapiste_service.execute_wsmethod('getSiteLocations', siteid=siteid)
        return jsonify(result)
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/site/<siteid>/workorders', methods=['GET'])
def get_site_workorders(siteid):
    """Get work orders for site"""
    try:
        result = mxapiste_service.execute_wsmethod('getSiteWorkOrders', siteid=siteid)
        return jsonify(result)
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/site/<siteid>/inventory', methods=['GET'])
def get_site_inventory(siteid):
    """Get inventory for site"""
    try:
        result = mxapiste_service.execute_wsmethod('getSiteInventory', siteid=siteid)
        return jsonify(result)
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/site/<siteid>/personnel', methods=['GET'])
def get_site_personnel(siteid):
    """Get personnel assigned to site"""
    try:
        result = mxapiste_service.execute_wsmethod('getSitePersonnel', siteid=siteid)
        return jsonify(result)
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/site/<siteid>/equipment', methods=['GET'])
def get_site_equipment(siteid):
    """Get equipment in site"""
    try:
        result = mxapiste_service.execute_wsmethod('getSiteEquipment', siteid=siteid)
        return jsonify(result)
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/site/<siteid>/documents', methods=['GET'])
def get_site_documents(siteid):
    """Get documents associated with site"""
    try:
        result = mxapiste_service.execute_wsmethod('getSiteDocuments', siteid=siteid)
        return jsonify(result)
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/site/<siteid>/history', methods=['GET'])
def get_site_history(siteid):
    """Get site history"""
    try:
        result = mxapiste_service.execute_wsmethod('getSiteHistory', siteid=siteid)
        return jsonify(result)
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

# MXAPISITE API Documentation Page
@app.route('/admin')
def admin_page():
    """Admin configuration page"""
    # Check if user is logged in
    if 'username' not in session or not token_manager.is_logged_in():
        return redirect(url_for('index'))

    return render_template('admin.html', username=session.get('username'))

# Signature Configuration API Endpoints
@app.route('/api/admin/signature-config', methods=['GET'])
def get_signature_config():
    """Get current signature configuration"""
    try:
        if 'username' not in session or not token_manager.is_logged_in():
            return jsonify({'success': False, 'error': 'Not authenticated'})

        return jsonify({
            'success': True,
            'config': signature_config
        })
    except Exception as e:
        logger.error(f"Error getting signature config: {str(e)}")
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/admin/signature-config', methods=['POST'])
def save_signature_config():
    """Save signature configuration"""
    try:
        if 'username' not in session or not token_manager.is_logged_in():
            return jsonify({'success': False, 'error': 'Not authenticated'})

        data = request.get_json()
        if not data:
            return jsonify({'success': False, 'error': 'No data provided'})

        # Validate the configuration
        valid_statuses = ['APPR', 'ASSIGN', 'READY', 'INPRG', 'PACK', 'DEFER', 'WAPPR',
                         'WGOVT', 'AWARD', 'MTLCXD', 'MTLISD', 'PISSUE', 'RTI', 'WMATL',
                         'WSERV', 'WSCH', 'COMP', 'SET']
        valid_scopes = ['parent', 'task']

        # Validate statuses
        if 'statuses' in data:
            for status in data['statuses']:
                if status not in valid_statuses:
                    return jsonify({'success': False, 'error': f'Invalid status: {status}'})

        # Validate scope
        if 'scope' in data:
            for scope in data['scope']:
                if scope not in valid_scopes:
                    return jsonify({'success': False, 'error': f'Invalid scope: {scope}'})

        # Update configuration
        signature_config.update(data)

        # Save to persistent storage
        if save_signature_config_to_file(signature_config):
            logger.info(f"📝 SIGNATURE CONFIG: Updated by {session.get('username')}")
            logger.info(f"📝 SIGNATURE CONFIG: Enabled: {signature_config.get('enabled', False)}")
            logger.info(f"📝 SIGNATURE CONFIG: Statuses: {signature_config.get('statuses', [])}")
            logger.info(f"📝 SIGNATURE CONFIG: Scope: {signature_config.get('scope', [])}")

            return jsonify({
                'success': True,
                'message': 'Signature configuration saved successfully',
                'config': signature_config
            })
        else:
            return jsonify({
                'success': False,
                'error': 'Failed to save configuration to persistent storage'
            })

    except Exception as e:
        logger.error(f"Error saving signature config: {str(e)}")
        return jsonify({'success': False, 'error': str(e)})

def is_signature_required(status, wo_type='parent'):
    """Check if signature is required for a status change"""
    try:
        logger.debug(f"🔍 SIGNATURE CHECK: Checking {status} for {wo_type}")
        logger.debug(f"🔍 SIGNATURE CHECK: Config enabled: {signature_config.get('enabled', False)}")
        logger.debug(f"🔍 SIGNATURE CHECK: Configured statuses: {signature_config.get('statuses', [])}")
        logger.debug(f"🔍 SIGNATURE CHECK: Configured scope: {signature_config.get('scope', [])}")

        if not signature_config.get('enabled', False):
            logger.debug(f"🔍 SIGNATURE CHECK: Signature system disabled")
            return False

        # Check if status requires signature
        if status not in signature_config.get('statuses', []):
            logger.debug(f"🔍 SIGNATURE CHECK: Status {status} not in configured statuses")
            return False

        # Check if scope applies
        if wo_type not in signature_config.get('scope', []):
            logger.debug(f"🔍 SIGNATURE CHECK: WO type {wo_type} not in configured scope")
            return False

        logger.info(f"✅ SIGNATURE CHECK: Signature required for {status} ({wo_type})")
        return True
    except Exception as e:
        logger.error(f"Error checking signature requirement: {str(e)}")
        return False

@app.route('/api/admin/signature-required', methods=['POST'])
def check_signature_required():
    """Check if signature is required for a status change"""
    try:
        if 'username' not in session or not token_manager.is_logged_in():
            return jsonify({'success': False, 'error': 'Not authenticated'})

        data = request.get_json()
        if not data or 'status' not in data:
            return jsonify({'success': False, 'error': 'Status is required'})

        status = data['status']
        wo_type = data.get('wo_type', 'parent')  # 'parent' or 'task'

        required = is_signature_required(status, wo_type)

        return jsonify({
            'success': True,
            'signature_required': required,
            'status': status,
            'wo_type': wo_type
        })

    except Exception as e:
        logger.error(f"Error checking signature requirement: {str(e)}")
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/signature/submit', methods=['POST'])
def submit_signature():
    """Submit signature and process status change"""
    try:
        if 'username' not in session or not token_manager.is_logged_in():
            return jsonify({'success': False, 'error': 'Not authenticated'})

        data = request.get_json()
        if not data:
            return jsonify({'success': False, 'error': 'No data provided'})

        required_fields = ['wonum', 'status', 'signature_data', 'customer_name', 'date_time']
        for field in required_fields:
            if field not in data:
                return jsonify({'success': False, 'error': f'Missing required field: {field}'})

        wonum = data['wonum']
        new_status = data['status']
        wo_type = data.get('wo_type', 'parent')
        signature_data = data['signature_data']
        customer_name = data['customer_name']
        comments = data.get('comments', '')
        date_time = data['date_time']

        logger.info(f"📝 SIGNATURE: Processing signature for WO {wonum} -> {new_status}")

        # 1. Generate PDF from signature data
        pdf_result = generate_signature_pdf(
            wonum=wonum,
            status=new_status,
            signature_data=signature_data,
            customer_name=customer_name,
            comments=comments,
            date_time=date_time,
            username=session.get('username')
        )

        if not pdf_result['success']:
            return jsonify({'success': False, 'error': f'PDF generation failed: {pdf_result["error"]}'})

        # 2. Handle task-level attachments if this is a task
        if wo_type == 'task':
            # Get parent work order number for task
            parent_wonum = get_parent_wonum_for_task(wonum)
            if parent_wonum:
                logger.info(f"📎 SIGNATURE: Task {wonum} belongs to parent {parent_wonum}")

                # Copy parent attachments to task level (keep parent intact)
                copy_result = copy_parent_attachments_to_task(wonum, parent_wonum)
                if copy_result:
                    logger.info(f"✅ SIGNATURE: Parent attachments copied to task {wonum}")
                else:
                    logger.warning(f"⚠️ SIGNATURE: Failed to copy parent attachments to task {wonum}")

            # Attach signature PDF to task level
            attachment_result = attach_signature_to_maximo(wonum, new_status, pdf_result['pdf_data'], wo_type)
        else:
            # For parent work orders, attach normally
            attachment_result = attach_signature_to_maximo(wonum, new_status, pdf_result['pdf_data'], wo_type)

        # 3. Change the status (simple and direct)
        status_result = process_status_change_with_signature(wonum, new_status, wo_type)

        if status_result['success']:
            logger.info(f"✅ SIGNATURE: Complete! WO {wonum} signed and status changed to {new_status}")
            return jsonify({
                'success': True,
                'message': 'Signature captured and status updated successfully',
                'pdf_attached': attachment_result.get('success', False)
            })
        else:
            return jsonify({
                'success': False,
                'error': f'Status change failed: {status_result.get("error")}'
            })

    except Exception as e:
        logger.error(f"❌ SIGNATURE: General error submitting signature: {str(e)}")
        return jsonify({'success': False, 'error': f'Signature submission failed: {str(e)}'})

def create_digital_signature_text(customer_name):
    """Create a digital signature from customer name"""
    try:
        from PIL import Image, ImageDraw, ImageFont

        # Create an image for the digital signature
        img = Image.new('RGBA', (400, 60), (255, 255, 255, 0))  # Transparent background
        draw = ImageDraw.Draw(img)

        try:
            # Try to use a script-like font (fallback to default if not available)
            font = ImageFont.truetype("/System/Library/Fonts/Brush Script MT.ttc", 36)
        except:
            try:
                font = ImageFont.truetype("/System/Library/Fonts/Helvetica.ttc", 32)
            except:
                font = ImageFont.load_default()

        # Draw the customer name in a signature style
        text = f"✓ {customer_name}"

        # Get text dimensions
        bbox = draw.textbbox((0, 0), text, font=font)
        text_width = bbox[2] - bbox[0]
        text_height = bbox[3] - bbox[1]

        # Center the text
        x = (400 - text_width) // 2
        y = (60 - text_height) // 2

        # Draw the text in blue color for digital signature
        draw.text((x, y), text, fill=(0, 0, 139, 255), font=font)  # Dark blue

        return img
    except Exception as e:
        logger.warning(f"⚠️ SIGNATURE PDF: Could not create digital signature text: {e}")
        return None

def generate_signature_pdf(wonum, status, signature_data, customer_name, comments, date_time, username):
    """Generate enhanced PDF with both handwritten and digital signatures"""
    try:
        import base64
        import io
        from reportlab.pdfgen import canvas
        from reportlab.lib.pagesizes import letter
        from reportlab.lib.utils import ImageReader
        from reportlab.lib.colors import black, blue
        from PIL import Image
        from datetime import datetime

        logger.info(f"📄 SIGNATURE PDF: Generating enhanced PDF for WO {wonum}")

        # Create PDF buffer
        buffer = io.BytesIO()
        p = canvas.Canvas(buffer, pagesize=letter)
        width, height = letter

        # Title
        p.setFont("Helvetica-Bold", 18)
        p.drawString(50, height - 50, f"Digital Signature Document")

        # Work order info
        p.setFont("Helvetica-Bold", 14)
        p.drawString(50, height - 80, f"Work Order: {wonum}")
        p.drawString(50, height - 100, f"Status Change: {status}")

        # Draw a border
        p.rect(40, 40, width - 80, height - 80, stroke=1, fill=0)

        # Customer information
        y_pos = height - 140
        p.setFont("Helvetica-Bold", 12)
        p.drawString(50, y_pos, "Customer Information:")

        p.setFont("Helvetica", 11)
        y_pos -= 25
        p.drawString(70, y_pos, f"Name: {customer_name}")
        y_pos -= 20
        p.drawString(70, y_pos, f"Date & Time: {date_time}")
        y_pos -= 20
        p.drawString(70, y_pos, f"Authorized by: {username}")

        # Comments section
        if comments:
            y_pos -= 30
            p.setFont("Helvetica-Bold", 12)
            p.drawString(50, y_pos, "Comments:")
            p.setFont("Helvetica", 11)
            y_pos -= 20
            # Handle long comments by wrapping
            comment_lines = [comments[i:i+80] for i in range(0, len(comments), 80)]
            for line in comment_lines:
                p.drawString(70, y_pos, line)
                y_pos -= 15

        # Digital Signature Section
        y_pos -= 40
        p.setFont("Helvetica-Bold", 14)
        p.setFillColor(blue)
        p.drawString(50, y_pos, "Digital Signature (Customer Name):")
        p.setFillColor(black)

        # Create and add digital signature
        digital_sig_img = create_digital_signature_text(customer_name)
        if digital_sig_img:
            digital_sig_buffer = io.BytesIO()
            digital_sig_img.save(digital_sig_buffer, format='PNG')
            digital_sig_buffer.seek(0)
            digital_sig_reader = ImageReader(digital_sig_buffer)

            y_pos -= 70
            p.drawImage(digital_sig_reader, 50, y_pos, 400, 60)
            logger.info(f"✅ SIGNATURE PDF: Digital signature added for {customer_name}")
        else:
            y_pos -= 40
            p.setFont("Helvetica", 12)
            p.drawString(70, y_pos, f"✓ {customer_name} (Digital Signature)")

        # Handwritten Signature Section
        y_pos -= 80
        p.setFont("Helvetica-Bold", 14)
        p.setFillColor(blue)
        p.drawString(50, y_pos, "Handwritten Signature:")
        p.setFillColor(black)

        # Process handwritten signature
        if signature_data and signature_data.startswith('data:image/png;base64,'):
            logger.info(f"🖊️ SIGNATURE PDF: Processing handwritten signature...")
            signature_base64 = signature_data.split(',')[1]
            signature_bytes = base64.b64decode(signature_base64)

            # Open and process the signature image
            signature_image = Image.open(io.BytesIO(signature_bytes))
            logger.info(f"📏 SIGNATURE PDF: Original signature size: {signature_image.size}, mode: {signature_image.mode}")

            # Convert to RGBA first to handle transparency properly
            if signature_image.mode != 'RGBA':
                signature_image = signature_image.convert('RGBA')

            # Create a white background and paste the signature
            white_bg = Image.new('RGB', signature_image.size, (255, 255, 255))
            white_bg.paste(signature_image, mask=signature_image.split()[-1])  # Use alpha channel as mask

            # Create image reader
            sig_buffer = io.BytesIO()
            white_bg.save(sig_buffer, format='PNG')
            sig_buffer.seek(0)
            sig_reader = ImageReader(sig_buffer)

            # Draw handwritten signature
            y_pos -= 120
            sig_width = 500
            sig_height = 100
            p.drawImage(sig_reader, 50, y_pos, sig_width, sig_height)
            logger.info(f"✅ SIGNATURE PDF: Handwritten signature added to PDF")
        else:
            logger.warning(f"⚠️ SIGNATURE PDF: No valid handwritten signature data provided")
            y_pos -= 60
            p.setFont("Helvetica", 12)
            p.drawString(70, y_pos, "[No handwritten signature provided]")

        # Signature box border
        p.rect(45, y_pos - 5, 510, 110, stroke=1, fill=0)

        # Verification section - RIGHT UNDERNEATH the handwritten signature
        y_pos -= 30  # Small gap after signature box
        p.setFont("Helvetica-Bold", 12)
        p.drawString(50, y_pos, "Verification:")
        p.setFont("Helvetica", 10)
        y_pos -= 20
        p.drawString(70, y_pos, "☑ Customer identity verified")
        y_pos -= 15
        p.drawString(70, y_pos, "☑ Digital signature captured")
        y_pos -= 15
        p.drawString(70, y_pos, "☑ Handwritten signature captured")
        y_pos -= 15
        p.drawString(70, y_pos, f"☑ Document generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

        # Footer with left and right alignment to prevent overlap
        footer_y = 90  # Start higher to avoid overlap
        p.setFont("Helvetica", 8)

        # Line 1: Left side - Generated by info, Right side - Date/Time
        p.drawString(50, footer_y, "Generated by Maximo Mobile App")
        p.drawRightString(width - 50, footer_y, f"Date: {date_time}")

        # Line 2: Left side - Compliance statement, Right side - Document type
        footer_y -= 18  # Increased spacing
        p.drawString(50, footer_y, "This is a digitally signed document for audit compliance.")
        p.drawRightString(width - 50, footer_y, f"Type: {status} Signature")

        # Line 3: Left side - Document ID (shortened), Right side - Timestamp
        footer_y -= 18  # Increased spacing
        doc_id_short = f"Document ID: {status}SIGNATURE_{wonum}"
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        p.drawString(50, footer_y, doc_id_short)
        p.drawRightString(width - 50, footer_y, f"Generated: {timestamp}")

        p.save()

        pdf_data = buffer.getvalue()
        buffer.close()

        logger.info(f"📄 SIGNATURE PDF: Enhanced PDF generated successfully ({len(pdf_data)} bytes)")

        return {
            'success': True,
            'pdf_data': pdf_data,
            'filename': f"{status}SIGNATURE_{wonum}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf"
        }

    except ImportError as e:
        logger.error(f"❌ SIGNATURE PDF: Missing required libraries: {str(e)}")
        return {
            'success': False,
            'error': 'PDF generation libraries not available. Please install reportlab and Pillow.'
        }
    except Exception as e:
        logger.error(f"❌ SIGNATURE PDF: Error generating PDF: {str(e)}")
        return {
            'success': False,
            'error': f'PDF generation failed: {str(e)}'
        }

def get_parent_wonum_for_task(task_wonum):
    """Get the parent work order number for a task"""
    try:
        logger.info(f"🔍 PARENT LOOKUP: Finding parent for task {task_wonum}")

        # Query the task to get its parent
        response = mxapi_service.token_manager.session.get(
            f"{mxapi_service.token_manager.base_url}/oslc/os/mxapiwodetail",
            params={
                'oslc.select': 'wonum,parent',
                'oslc.where': f'wonum="{task_wonum}"',
                'oslc.pageSize': '1'
            },
            timeout=(5.0, 15),
            headers={"Accept": "application/json"}
        )

        if response.status_code == 200:
            data = response.json()
            members = data.get('member', [])
            if members:
                parent = members[0].get('parent')
                if parent:
                    logger.info(f"✅ PARENT LOOKUP: Task {task_wonum} has parent {parent}")
                    return parent
                else:
                    logger.info(f"ℹ️ PARENT LOOKUP: Task {task_wonum} has no parent (is parent work order)")
                    return None
            else:
                logger.warning(f"⚠️ PARENT LOOKUP: Task {task_wonum} not found")
                return None
        else:
            logger.error(f"❌ PARENT LOOKUP: Failed to query task {task_wonum}: {response.status_code}")
            return None

    except Exception as e:
        logger.error(f"❌ PARENT LOOKUP: Error finding parent for task {task_wonum}: {e}")
        return None

def copy_parent_attachments_to_task(task_wonum, parent_wonum):
    """Copy all parent work order attachments to task level"""
    try:
        logger.info(f"📎 COPY ATTACHMENTS: Copying parent {parent_wonum} attachments to task {task_wonum}")

        # Get parent attachments
        parent_attachments = mxapi_service.get_workorder_attachments(parent_wonum)
        if not parent_attachments.get('success'):
            logger.error(f"❌ COPY ATTACHMENTS: Failed to get parent attachments: {parent_attachments.get('error')}")
            return False

        attachments = parent_attachments.get('attachments', [])
        logger.info(f"📎 COPY ATTACHMENTS: Found {len(attachments)} parent attachments to copy")

        copied_count = 0
        for attachment in attachments:
            try:
                # Skip if attachment is already a signature (avoid duplicates)
                filename = attachment.get('fileName', '')
                if 'SIGNATURE' in filename.upper():
                    logger.info(f"📎 COPY ATTACHMENTS: Skipping signature file: {filename}")
                    continue

                # Download the attachment content
                download_result = mxapi_service.download_attachment(parent_wonum, attachment.get('identifier'))
                if not download_result.get('success'):
                    logger.warning(f"⚠️ COPY ATTACHMENTS: Failed to download {filename}: {download_result.get('error')}")
                    continue

                # Prepare file data for task
                file_data = {
                    'filename': filename,
                    'content': download_result.get('content'),
                    'content_type': attachment.get('format', {}).get('label', 'application/octet-stream')
                }

                # Add to task with copied description
                description = f"[COPIED FROM PARENT] {attachment.get('description', filename)}"
                result = mxapi_service.add_workorder_attachment(
                    wonum=task_wonum,
                    file_data=file_data,
                    description=description,
                    doctype=attachment.get('docType', 'Attachments')
                )

                if result.get('success'):
                    copied_count += 1
                    logger.info(f"✅ COPY ATTACHMENTS: Copied {filename} to task {task_wonum}")
                else:
                    logger.warning(f"⚠️ COPY ATTACHMENTS: Failed to copy {filename}: {result.get('error')}")

            except Exception as e:
                logger.error(f"❌ COPY ATTACHMENTS: Error copying attachment {attachment.get('fileName', 'unknown')}: {e}")
                continue

        logger.info(f"✅ COPY ATTACHMENTS: Successfully copied {copied_count}/{len(attachments)} attachments to task {task_wonum}")
        return copied_count > 0

    except Exception as e:
        logger.error(f"❌ COPY ATTACHMENTS: Error copying parent attachments: {e}")
        return False

def attach_signature_to_maximo(wonum, status, pdf_data, wo_type='parent'):
    """Attach signature PDF to Maximo work order"""
    try:
        # Use existing attachment service
        filename = f"{status}SIGNATURE_{wonum}.pdf"
        description = f"Digital signature for status change to {status}"

        # Create proper file_data dictionary as expected by add_workorder_attachment
        file_data = {
            'filename': filename,
            'content': pdf_data,
            'content_type': 'application/pdf'
        }

        # Use the existing attachment method
        result = mxapi_service.add_workorder_attachment(
            wonum=wonum,
            file_data=file_data,
            description=description,
            doctype="Attachments"
        )

        if result.get('success'):
            logger.info(f"📎 SIGNATURE: Successfully attached signature PDF to WO {wonum}")
            return {
                'success': True,
                'attachment_info': result.get('attachment_info')
            }
        else:
            logger.error(f"❌ SIGNATURE: Failed to attach PDF: {result.get('error')}")
            return {
                'success': False,
                'error': result.get('error', 'Unknown attachment error')
            }

    except Exception as e:
        logger.error(f"❌ SIGNATURE: Error attaching PDF: {str(e)}")
        return {
            'success': False,
            'error': f'Attachment failed: {str(e)}'
        }

def process_status_change_with_signature(wonum, new_status, wo_type):
    """Process status change after signature capture - SIMPLE AND DIRECT"""
    try:
        logger.info(f"🔄 SIGNATURE STATUS: Processing {wo_type} WO {wonum} -> {new_status}")

        # Just use the existing MXAPI service - that's it!
        result = mxapi_service.execute_wsmethod(
            'changeStatus',
            wonum=wonum,
            data={'status': new_status}
        )

        if result.get('success'):
            logger.info(f"✅ SIGNATURE STATUS: Successfully changed status for WO {wonum}")
        else:
            logger.error(f"❌ SIGNATURE STATUS: Failed to change status: {result.get('error')}")

        return result

    except Exception as e:
        logger.error(f"❌ SIGNATURE STATUS: Error processing status change: {str(e)}")
        return {
            'success': False,
            'error': f'Status change failed: {str(e)}'
        }

# AI Insights for Signature Analytics
@app.route('/api/admin/signature-analytics', methods=['GET'])
def get_signature_analytics():
    """Get AI-powered signature analytics and trends"""
    try:
        if 'username' not in session or not token_manager.is_logged_in():
            return jsonify({'success': False, 'error': 'Not authenticated'})

        # Generate AI insights based on current configuration and usage patterns
        analytics = generate_signature_analytics()

        return jsonify({
            'success': True,
            'analytics': analytics
        })

    except Exception as e:
        logger.error(f"Error getting signature analytics: {str(e)}")
        return jsonify({'success': False, 'error': str(e)})

def generate_signature_analytics():
    """Generate AI-powered signature analytics"""
    try:
        current_config = signature_config

        # Calculate configuration metrics
        total_statuses = 18  # Total number of available statuses
        configured_statuses = len(current_config.get('statuses', []))
        coverage_percentage = (configured_statuses / total_statuses) * 100

        # Generate insights based on configuration
        insights = []

        # Coverage Analysis
        if coverage_percentage == 0:
            insights.append({
                'type': 'warning',
                'title': 'No Signature Requirements Configured',
                'content': 'Consider enabling signatures for critical status transitions like COMP (Complete) and APPR (Approved) to improve audit compliance.',
                'priority': 'high'
            })
        elif coverage_percentage < 20:
            insights.append({
                'type': 'info',
                'title': 'Low Signature Coverage',
                'content': f'You have {coverage_percentage:.1f}% signature coverage. This is optimal for minimal disruption while maintaining security.',
                'priority': 'medium'
            })
        elif coverage_percentage > 50:
            insights.append({
                'type': 'warning',
                'title': 'High Signature Coverage',
                'content': f'You have {coverage_percentage:.1f}% signature coverage. Consider reducing to prevent signature fatigue among users.',
                'priority': 'medium'
            })
        else:
            insights.append({
                'type': 'success',
                'title': 'Optimal Signature Coverage',
                'content': f'You have {coverage_percentage:.1f}% signature coverage. This provides good security without overwhelming users.',
                'priority': 'low'
            })

        # Status-specific recommendations
        critical_statuses = ['COMP', 'APPR', 'CLOSE']
        configured_critical = [s for s in critical_statuses if s in current_config.get('statuses', [])]

        if len(configured_critical) < len(critical_statuses):
            missing_critical = [s for s in critical_statuses if s not in configured_critical]
            insights.append({
                'type': 'recommendation',
                'title': 'Critical Status Recommendations',
                'content': f'Consider requiring signatures for these critical statuses: {", ".join(missing_critical)}. These represent key workflow milestones.',
                'priority': 'high'
            })

        # Scope analysis
        scope = current_config.get('scope', [])
        if 'parent' in scope and 'task' in scope:
            insights.append({
                'type': 'info',
                'title': 'Comprehensive Scope Coverage',
                'content': 'Signatures are configured for both parent and task work orders, providing complete audit coverage.',
                'priority': 'low'
            })
        elif len(scope) == 1:
            missing_scope = 'task' if 'parent' in scope else 'parent'
            insights.append({
                'type': 'suggestion',
                'title': 'Partial Scope Coverage',
                'content': f'Consider extending signature requirements to {missing_scope} work orders for complete audit coverage.',
                'priority': 'medium'
            })

        # Industry best practices
        insights.append({
            'type': 'trend',
            'title': 'Industry Trend Analysis',
            'content': 'Based on industry data, organizations typically require signatures for 15-25% of status transitions to balance security with usability.',
            'priority': 'low'
        })

        # Future predictions
        if configured_statuses > 0:
            insights.append({
                'type': 'prediction',
                'title': 'Usage Prediction',
                'content': f'With {configured_statuses} configured statuses, expect approximately {configured_statuses * 2}-{configured_statuses * 5} signature requests per day in a typical environment.',
                'priority': 'low'
            })

        # Security recommendations
        insights.append({
            'type': 'security',
            'title': 'Security Enhancement',
            'content': 'Digital signatures provide non-repudiation and audit trails. Consider periodic review of signature requirements based on compliance needs.',
            'priority': 'medium'
        })

        return {
            'configuration_summary': {
                'total_statuses': total_statuses,
                'configured_statuses': configured_statuses,
                'coverage_percentage': round(coverage_percentage, 1),
                'scope': scope,
                'enabled': current_config.get('enabled', False)
            },
            'insights': insights,
            'recommendations': {
                'optimal_coverage': '15-25%',
                'critical_statuses': critical_statuses,
                'review_frequency': 'Quarterly'
            },
            'generated_at': datetime.datetime.now().isoformat()
        }

    except Exception as e:
        logger.error(f"Error generating signature analytics: {str(e)}")
        return {
            'error': str(e),
            'insights': [],
            'generated_at': datetime.datetime.now().isoformat()
        }

# Issue Material Configuration API Endpoints
@app.route('/api/admin/issue-material-config', methods=['GET'])
def get_issue_material_config():
    """Get current issue material configuration"""
    try:
        if 'username' not in session or not token_manager.is_logged_in():
            return jsonify({'success': False, 'error': 'Not authenticated'})

        return jsonify({
            'success': True,
            'config': issue_material_config
        })
    except Exception as e:
        logger.error(f"Error getting issue material config: {str(e)}")
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/admin/issue-material-config', methods=['POST'])
def save_issue_material_config():
    """Save issue material configuration"""
    try:
        if 'username' not in session or not token_manager.is_logged_in():
            return jsonify({'success': False, 'error': 'Not authenticated'})

        data = request.get_json()
        if not data:
            return jsonify({'success': False, 'error': 'No data provided'})

        # Validate the configuration
        valid_statuses = ['APPR', 'ASSIGN', 'READY', 'INPRG', 'PACK', 'DEFER', 'WAPPR',
                         'WGOVT', 'AWARD', 'MTLCXD', 'MTLISD', 'PISSUE', 'RTI', 'WMATL',
                         'WSERV', 'WSCH', 'COMP', 'SET']

        # Validate work order statuses
        if 'work_order_statuses' in data:
            for status in data['work_order_statuses']:
                if status not in valid_statuses:
                    return jsonify({'success': False, 'error': f'Invalid work order status: {status}'})

        # Validate task statuses
        if 'task_statuses' in data:
            for status in data['task_statuses']:
                if status not in valid_statuses:
                    return jsonify({'success': False, 'error': f'Invalid task status: {status}'})

        # Ensure at least one status is selected for both work orders and tasks
        if not data.get('work_order_statuses') or not data.get('task_statuses'):
            return jsonify({'success': False, 'error': 'At least one status must be selected for both work orders and tasks'})

        # Update configuration
        issue_material_config.update(data)

        # Save to persistent storage
        if save_issue_material_config_to_file(issue_material_config):
            logger.info(f"📝 ISSUE MATERIAL CONFIG: Updated by {session.get('username')}")
            logger.info(f"📝 ISSUE MATERIAL CONFIG: Enabled: {issue_material_config.get('enabled', False)}")
            logger.info(f"📝 ISSUE MATERIAL CONFIG: WO Statuses: {issue_material_config.get('work_order_statuses', [])}")
            logger.info(f"📝 ISSUE MATERIAL CONFIG: Task Statuses: {issue_material_config.get('task_statuses', [])}")

            return jsonify({
                'success': True,
                'message': 'Issue material configuration saved successfully',
                'config': issue_material_config
            })
        else:
            return jsonify({
                'success': False,
                'error': 'Failed to save configuration to persistent storage'
            })

    except Exception as e:
        logger.error(f"Error saving issue material config: {str(e)}")
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api-docs/mxapiste')
def mxapiste_documentation():
    """Complete API documentation for all MXAPISTE endpoints"""
    html = """
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Complete MXAPISTE API Documentation</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
        <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
        <style>
            body { background-color: #f8f9fa; }
            .api-header { background: linear-gradient(135deg, #007bff, #0056b3); color: white; padding: 30px 0; }
            .endpoint-card { margin: 20px 0; border-left: 4px solid #007bff; }
            .method-badge { font-size: 12px; padding: 4px 8px; border-radius: 4px; }
            .method-get { background: #28a745; color: white; }
            .method-post { background: #007bff; color: white; }
            .code-block { background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 4px; padding: 15px; margin: 10px 0; }
        </style>
    </head>
    <body>
        <div class="api-header">
            <div class="container">
                <h1><i class="fas fa-building me-3"></i>Complete MXAPISTE API Documentation</h1>
                <p class="lead">All available Maximo Site API endpoints and methods</p>
            </div>
        </div>

        <div class="container mt-4">
            <!-- Overview Section -->
            <div class="card endpoint-card">
                <div class="card-header">
                    <h3><i class="fas fa-info-circle me-2"></i>API Overview</h3>
                </div>
                <div class="card-body">
                    <p>This API provides access to site-related information in Maximo, including site details, hierarchy, assets, and more.</p>

                    <h5>Authentication</h5>
                    <p>All endpoints require user authentication. Make sure you're logged in before making API calls.</p>

                    <h5>Base URL</h5>
                    <div class="code-block">
                        <code>https://your-domain.com/api/</code>
                    </div>
                </div>
            </div>

            <!-- Available Methods -->
            <div class="card endpoint-card">
                <div class="card-header">
                    <h3><i class="fas fa-list me-2"></i>Available Methods</h3>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h5>Site Information</h5>
                            <ul>
                                <li><strong>getSiteInfo</strong> - Get site information</li>
                                <li><strong>getSiteHierarchy</strong> - Get site hierarchy</li>
                                <li><strong>getSiteHistory</strong> - Get site history</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h5>Site Resources</h5>
                            <ul>
                                <li><strong>getSiteAssets</strong> - Get assets associated with site</li>
                                <li><strong>getSiteLocations</strong> - Get locations in site</li>
                                <li><strong>getSiteWorkOrders</strong> - Get work orders for site</li>
                                <li><strong>getSiteInventory</strong> - Get inventory for site</li>
                                <li><strong>getSitePersonnel</strong> - Get personnel assigned to site</li>
                                <li><strong>getSiteEquipment</strong> - Get equipment in site</li>
                                <li><strong>getSiteDocuments</strong> - Get documents associated with site</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Core Endpoints -->
            <div class="card endpoint-card">
                <div class="card-header">
                    <h3><i class="fas fa-cogs me-2"></i>Core API Endpoints</h3>
                </div>
                <div class="card-body">
                    <!-- Get Methods -->
                    <div class="mb-4">
                        <h5><span class="method-badge method-get">GET</span> /api/mxapiste/methods</h5>
                        <p>Get list of all available MXAPISTE methods</p>
                        <div class="code-block">
                            <strong>Response:</strong><br>
                            <code>{
  "success": true,
  "methods": {...}
}</code>
                        </div>
                    </div>

                    <!-- Execute Method -->
                    <div class="mb-4">
                        <h5><span class="method-badge method-post">POST</span> /api/mxapiste/{siteid}/execute/{method_name}</h5>
                        <p>Execute any MXAPISTE method on a specific site</p>
                        <div class="code-block">
                            <strong>Request Body:</strong><br>
                            <code>{
  "parameters": {
    "key": "value"
  }
}</code>
                        </div>
                    </div>

                    <!-- Bulk Operations -->
                    <div class="mb-4">
                        <h5><span class="method-badge method-post">POST</span> /api/mxapiste/bulk/{method_name}</h5>
                        <p>Execute any MXAPISTE method on multiple sites (bulk operation)</p>
                        <div class="code-block">
                            <strong>Request Body:</strong><br>
                            <code>[
  {"siteid": "SITE1", "parameters": {...}},
  {"siteid": "SITE2", "parameters": {...}}
]</code>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Specific Method Endpoints -->
            <div class="card endpoint-card">
                <div class="card-header">
                    <h3><i class="fas fa-tools me-2"></i>Specific Method Endpoints</h3>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h5>Site Information</h5>
                            <ul class="list-unstyled">
                                <li><span class="method-badge method-get">GET</span> /api/site/{siteid}/info</li>
                                <li><span class="method-badge method-get">GET</span> /api/site/{siteid}/hierarchy</li>
                                <li><span class="method-badge method-get">GET</span> /api/site/{siteid}/history</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h5>Site Resources</h5>
                            <ul class="list-unstyled">
                                <li><span class="method-badge method-get">GET</span> /api/site/{siteid}/assets</li>
                                <li><span class="method-badge method-get">GET</span> /api/site/{siteid}/locations</li>
                                <li><span class="method-badge method-get">GET</span> /api/site/{siteid}/workorders</li>
                                <li><span class="method-badge method-get">GET</span> /api/site/{siteid}/inventory</li>
                                <li><span class="method-badge method-get">GET</span> /api/site/{siteid}/personnel</li>
                                <li><span class="method-badge method-get">GET</span> /api/site/{siteid}/equipment</li>
                                <li><span class="method-badge method-get">GET</span> /api/site/{siteid}/documents</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Examples -->
            <div class="card endpoint-card">
                <div class="card-header">
                    <h3><i class="fas fa-code me-2"></i>Usage Examples</h3>
                </div>
                <div class="card-body">
                    <h5>1. Get Site Information</h5>
                    <div class="code-block">
                        <strong>GET</strong> /api/site/LCVKWT/info<br>
                        <strong>Response:</strong> {
  "success": true,
  "data": {
    "siteid": "LCVKWT",
    "description": "Kwajalein Site",
    "status": "ACTIVE",
    "type": "OPERATIONAL"
  }
}
                    </div>

                    <h5>2. Get Site Assets</h5>
                    <div class="code-block">
                        <strong>GET</strong> /api/site/LCVKWT/assets<br>
                        <strong>Response:</strong> {
  "success": true,
  "data": {
    "assets": [
      {
        "assetnum": "ASSET001",
        "description": "Main Generator",
        "status": "OPERATING"
      }
    ]
  }
}
                    </div>

                    <h5>3. Execute Custom Method</h5>
                    <div class="code-block">
                        <strong>POST</strong> /api/mxapiste/LCVKWT/execute/getSiteHierarchy<br>
                        <strong>Body:</strong> {
  "parameters": {
    "includeInactive": false
  }
}
                    </div>
                </div>
            </div>

            <!-- Navigation -->
            <div class="text-center mt-4 mb-4">
                <a href="/welcome" class="btn btn-primary btn-lg me-3">
                    <i class="fas fa-home me-2"></i>Back to Welcome
                </a>
                <a href="/api-docs/mxapiperuser" class="btn btn-outline-secondary btn-lg">
                    <i class="fas fa-users me-2"></i>User API Docs
                </a>
            </div>
        </div>

        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    </body>
    </html>
    """
    return html

# MXAPIPERUSER API Documentation Page
@app.route('/api-docs/mxapiperuser')
def mxapiperuser_documentation():
    """Complete API documentation for all MXAPIPERUSER endpoints"""
    html = """
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Complete MXAPIPERUSER API Documentation</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
        <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
        <style>
            body { background-color: #f8f9fa; }
            .api-header { background: linear-gradient(135deg, #28a745, #1e7e34); color: white; padding: 30px 0; }
            .endpoint-card { margin: 20px 0; border-left: 4px solid #28a745; }
            .method-badge { font-size: 12px; padding: 4px 8px; border-radius: 4px; }
            .method-get { background: #28a745; color: white; }
            .method-post { background: #007bff; color: white; }
            .code-block { background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 4px; padding: 15px; margin: 10px 0; }
        </style>
    </head>
    <body>
        <div class="api-header">
            <div class="container">
                <h1><i class="fas fa-users me-3"></i>Complete MXAPIPERUSER API Documentation</h1>
                <p class="lead">All available Maximo User API endpoints and methods</p>
            </div>
        </div>

        <div class="container mt-4">
            <!-- Overview Section -->
            <div class="card endpoint-card">
                <div class="card-header">
                    <h3><i class="fas fa-info-circle me-2"></i>API Overview</h3>
                </div>
                <div class="card-body">
                    <p>This API provides access to user-related information in Maximo, including available sites and user details.</p>

                    <h5>Authentication</h5>
                    <p>All endpoints require user authentication. Make sure you're logged in before making API calls.</p>

                    <h5>Base URL</h5>
                    <div class="code-block">
                        <code>https://your-domain.com/api/</code>
                    </div>
                </div>
            </div>

            <!-- Available Sites -->
            <div class="card endpoint-card">
                <div class="card-header">
                    <h3><i class="fas fa-list me-2"></i>Available Sites</h3>
                </div>
                <div class="card-body">
                    <h5>Endpoint</h5>
                    <div class="code-block">
                        <span class="method-badge method-get">GET</span> /api/mxapiperuser
                    </div>

                    <h5>Example Response</h5>
                    <div class="code-block">
                        <code>{
  "success": true,
  "sites": [
    {
      "siteid": "LCVKWT",
      "description": "Kwajalein Site",
      "status": "ACTIVE"
    },
    {
      "siteid": "BEDFORD",
      "description": "Bedford Site",
      "status": "ACTIVE"
    }
  ]
}</code>
                    </div>
                </div>
            </div>

            <!-- Site Information -->
            <div class="card endpoint-card">
                <div class="card-header">
                    <h3><i class="fas fa-info-circle me-2"></i>Site Information</h3>
                </div>
                <div class="card-body">
                    <h5>Endpoint</h5>
                    <div class="code-block">
                        <span class="method-badge method-get">GET</span> /api/site/{siteid}/info
                    </div>

                    <h5>Example Request</h5>
                    <div class="code-block">
                        <code>GET /api/site/LCVKWT/info</code>
                    </div>

                    <h5>Example Response</h5>
                    <div class="code-block">
                        <code>{
  "success": true,
  "data": {
    "siteid": "LCVKWT",
    "description": "Kwajalein Site",
    "status": "ACTIVE",
    "type": "OPERATIONAL",
    "address": "Kwajalein Atoll, Marshall Islands",
    "contact": "John Doe",
    "phone": "******-0123",
    "email": "<EMAIL>"
  }
}</code>
                    </div>
                </div>
            </div>

            <!-- Navigation -->
            <div class="text-center mt-4 mb-4">
                <a href="/welcome" class="btn btn-primary btn-lg me-3">
                    <i class="fas fa-home me-2"></i>Back to Welcome
                </a>
            </div>
        </div>

        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    </body>
    </html>
    """
    return html

# Site Access API Documentation Page
@app.route('/api-docs/mxapisite')
def mxapisite_documentation():
    """Complete API documentation for Site Access endpoints"""
    html = """
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Site Access API Documentation</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
        <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
        <style>
            body { background-color: #f8f9fa; }
            .api-header { background: linear-gradient(135deg, #17a2b8, #138496); color: white; padding: 30px 0; }
            .endpoint-card { margin: 20px 0; border-left: 4px solid #17a2b8; }
            .method-badge { font-size: 12px; padding: 4px 8px; border-radius: 4px; }
            .method-get { background: #28a745; color: white; }
            .method-post { background: #007bff; color: white; }
            .code-block { background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 4px; padding: 15px; margin: 10px 0; }
            .authallsites-badge { background: #ffc107; color: #212529; padding: 2px 6px; border-radius: 3px; font-size: 11px; }
            .mobile-responsive { font-size: 0.9rem; }
            @media (max-width: 768px) {
                .container { padding: 0.5rem; }
                .code-block { font-size: 0.8rem; padding: 10px; }
                .method-badge { font-size: 10px; }
            }
        </style>
    </head>
    <body>
        <div class="api-header">
            <div class="container">
                <h1><i class="fas fa-building me-3"></i>Site Access API Documentation</h1>
                <p class="lead">Lightning-fast site authorization data with AUTHALLSITES logic</p>
                <div class="mt-3">
                    <span class="badge bg-success me-2">Lightning Fast</span>
                    <span class="badge bg-info me-2">5-Min Cache</span>
                    <span class="authallsites-badge me-2">AUTHALLSITES Logic</span>
                    <span class="badge bg-warning text-dark">Mobile Friendly</span>
                </div>
            </div>
        </div>

        <div class="container mt-4">
            <!-- Overview Section -->
            <div class="card endpoint-card">
                <div class="card-header">
                    <h3><i class="fas fa-info-circle me-2"></i>API Overview</h3>
                </div>
                <div class="card-body">
                    <p>The Site Access API provides comprehensive access to user site authorization data from Maximo,
                    including person information, user accounts, group memberships, and site authorizations with
                    intelligent AUTHALLSITES logic.</p>

                    <div class="row mt-3">
                        <div class="col-md-6">
                            <h5><i class="fas fa-rocket me-2"></i>Performance Features</h5>
                            <ul>
                                <li><strong>Lightning-fast retrieval</strong> - Optimized queries</li>
                                <li><strong>5-minute intelligent caching</strong> - Reduced system load</li>
                                <li><strong>30-minute ALL sites cache</strong> - Maximum performance</li>
                                <li><strong>Optimized timeouts</strong> - 3.05s/8s/15s</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h5><i class="fas fa-key me-2"></i>AUTHALLSITES Logic</h5>
                            <ul>
                                <li><strong>AUTHALLSITES=1</strong> - Shows ALL sites in system</li>
                                <li><strong>AUTHALLSITES=0</strong> - Shows specific site authorizations</li>
                                <li><strong>Automatic detection</strong> - Smart logic per user</li>
                                <li><strong>Distinct sites only</strong> - No duplicates</li>
                            </ul>
                        </div>
                    </div>

                    <h5 class="mt-3">Authentication</h5>
                    <p>All endpoints require valid Maximo authentication via session cookies.</p>

                    <h5>Base URL</h5>
                    <div class="code-block">
                        <code>http://127.0.0.1:5008/api/site-access</code>
                    </div>
                </div>
            </div>

            <!-- Core Endpoints -->
            <div class="card endpoint-card">
                <div class="card-header">
                    <h3><i class="fas fa-cogs me-2"></i>Core API Endpoints</h3>
                </div>
                <div class="card-body">

                    <!-- Person Data -->
                    <div class="mb-4">
                        <h5><span class="method-badge method-get">GET</span> /api/site-access/{personid}/person</h5>
                        <p>Retrieve person table information with capitalized field names</p>
                        <div class="code-block">
                            <strong>Example:</strong> <code>GET /api/site-access/{personid}/person</code><br>
                            <strong>Response:</strong><br>
                            <code>{
  "success": true,
  "data": {
    "Personid": "{personid}",
    "Firstname": "Tinu",
    "Lastname": "Thomas",
    "Displayname": "Tinu Thomas",
    "Status": "ACTIVE",
    "Locationorg": "USNAVY",
    "Locationsite": "NSGBA"
  }
}</code>
                        </div>
                    </div>

                    <!-- MaxUser Data -->
                    <div class="mb-4">
                        <h5><span class="method-badge method-get">GET</span> /api/site-access/{personid}/maxuser</h5>
                        <p>Retrieve maxuser table information with capitalized field names</p>
                        <div class="code-block">
                            <strong>Example:</strong> <code>GET /api/site-access/{personid}/maxuser</code><br>
                            <strong>Response:</strong><br>
                            <code>{
  "success": true,
  "data": {
    "Userid": "{personid}",
    "Loginid": "<EMAIL>",
    "Status": "ACTIVE",
    "Type": "MAXUSER",
    "Defsite": "NSGBA"
  }
}</code>
                        </div>
                    </div>

                    <!-- Groups Data -->
                    <div class="mb-4">
                        <h5><span class="method-badge method-get">GET</span> /api/site-access/{personid}/groups</h5>
                        <p>Retrieve group memberships with AUTHALLSITES information</p>
                        <div class="code-block">
                            <strong>Example:</strong> <code>GET /api/site-access/{personid}/groups</code><br>
                            <strong>Response:</strong><br>
                            <code>{
  "success": true,
  "data": [
    {
      "Group Name": "MAXADMIN",
      "Description": "Maximo Administrators",
      "AUTHALLSITES": "1"
    },
    {
      "Group Name": "MAXEVERYONE",
      "Description": "All Maximo Users",
      "AUTHALLSITES": "0"
    }
  ]
}</code>
                        </div>
                    </div>

                    <!-- Sites Data -->
                    <div class="mb-4">
                        <h5><span class="method-badge method-get">GET</span> /api/site-access/{personid}/sites</h5>
                        <p>Retrieve site authorizations with intelligent AUTHALLSITES logic</p>
                        <div class="code-block">
                            <strong>AUTHALLSITES=1 Example:</strong> <code>GET /api/site-access/{admin-personid}/sites</code><br>
                            <strong>Response (ALL sites in system):</strong><br>
                            <code>{
  "success": true,
  "data": [
    {"Site ID": "BBOS", "Organization": "USNAVY"},
    {"Site ID": "CJORD", "Organization": "USARMY"},
    {"Site ID": "IKWAJ", "Organization": "USARMY"},
    {"Site ID": "LCVKWT", "Organization": "USARMY"},
    {"Site ID": "NSGBA", "Organization": "USNAVY"},
    {"Site ID": "THULE", "Organization": "USAF"}
  ]
}</code>
                        </div>
                        <div class="code-block">
                            <strong>AUTHALLSITES=0 Example:</strong> <code>GET /api/site-access/{user-personid}/sites</code><br>
                            <strong>Response (specific sites only):</strong><br>
                            <code>{
  "success": true,
  "data": [
    {"Site ID": "NSGBA", "Organization": "USNAVY"},
    {"Site ID": "LCVKWT", "Organization": "USARMY"},
    {"Site ID": "LGCAP", "Organization": "USARMY"},
    {"Site ID": "IKWAJ", "Organization": "USARMY"}
  ]
}</code>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Cache Management -->
            <div class="card endpoint-card">
                <div class="card-header">
                    <h3><i class="fas fa-database me-2"></i>Cache Management</h3>
                </div>
                <div class="card-body">

                    <!-- Clear Cache -->
                    <div class="mb-4">
                        <h5><span class="method-badge method-post">POST</span> /api/site-access/cache/clear</h5>
                        <p>Clear all caches (site access + all sites cache)</p>
                        <div class="code-block">
                            <strong>Response:</strong><br>
                            <code>{
  "success": true,
  "message": "All caches cleared successfully"
}</code>
                        </div>
                    </div>

                    <!-- Cache Stats -->
                    <div class="mb-4">
                        <h5><span class="method-badge method-get">GET</span> /api/site-access/cache/stats</h5>
                        <p>Retrieve cache performance statistics</p>
                        <div class="code-block">
                            <strong>Response:</strong><br>
                            <code>{
  "success": true,
  "data": {
    "cached_entries": 5,
    "cache_duration": 300,
    "all_sites_cached": true,
    "all_sites_fresh": true,
    "all_sites_count": 16,
    "all_sites_cache_duration": 1800
  }
}</code>
                        </div>
                    </div>
                </div>
            </div>

            <!-- AUTHALLSITES Logic -->
            <div class="card endpoint-card">
                <div class="card-header">
                    <h3><i class="fas fa-key me-2"></i>AUTHALLSITES Logic Explained</h3>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h5><span class="authallsites-badge">AUTHALLSITES=1</span> Users</h5>
                            <ul>
                                <li><strong>Access Level:</strong> ALL sites in system</li>
                                <li><strong>Data Source:</strong> Entire mxapisite endpoint (10,000 records)</li>
                                <li><strong>Cache Duration:</strong> 30 minutes (aggressive)</li>
                                <li><strong>Performance:</strong> Lightning-fast after first load</li>
                                <li><strong>Example Users:</strong> Admin users with AUTHALLSITES=1</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h5><span class="badge bg-secondary">AUTHALLSITES=0</span> Users</h5>
                            <ul>
                                <li><strong>Access Level:</strong> Specific authorized sites only</li>
                                <li><strong>Data Source:</strong> User's group site authorizations</li>
                                <li><strong>Cache Duration:</strong> 5 minutes (standard)</li>
                                <li><strong>Performance:</strong> Fast with intelligent caching</li>
                                <li><strong>Example Users:</strong> Standard users with AUTHALLSITES=0</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Usage Examples -->
            <div class="card endpoint-card">
                <div class="card-header">
                    <h3><i class="fas fa-code me-2"></i>Usage Examples</h3>
                </div>
                <div class="card-body">

                    <h5>JavaScript/Fetch</h5>
                    <div class="code-block">
                        <code>// Get person data
fetch('/api/site-access/{personid}/person')
  .then(response => response.json())
  .then(data => {
    if (data.success) {
      console.log('Person:', data.data.Displayname);
    }
  });

// Get sites with AUTHALLSITES logic
fetch('/api/site-access/{personid}/sites')
  .then(response => response.json())
  .then(data => {
    if (data.success) {
      console.log(`Found ${data.data.length} sites`);
      data.data.forEach(site => {
        console.log(`${site['Site ID']} (${site['Organization']})`);
      });
    }
  });</code>
                    </div>

                    <h5>cURL</h5>
                    <div class="code-block">
                        <code># Get group memberships
curl -X GET "http://127.0.0.1:5008/api/site-access/{personid}/groups" \\
  -H "Accept: application/json" \\
  --cookie-jar cookies.txt

# Clear cache
curl -X POST "http://127.0.0.1:5008/api/site-access/cache/clear" \\
  -H "Accept: application/json" \\
  --cookie-jar cookies.txt</code>
                    </div>
                </div>
            </div>

            <!-- Integration -->
            <div class="card endpoint-card">
                <div class="card-header">
                    <h3><i class="fas fa-puzzle-piece me-2"></i>Integration</h3>
                </div>
                <div class="card-body">
                    <h5>Enhanced Profile Page</h5>
                    <p>The Site Access API is integrated into the Enhanced Profile page with:</p>
                    <ul>
                        <li><strong>4-tab interface:</strong> Person | User Account | Group Memberships | Site Authorizations</li>
                        <li><strong>Dynamic loading:</strong> Data loads on-demand when tabs are clicked</li>
                        <li><strong>Mobile-responsive:</strong> Optimized for all devices</li>
                        <li><strong>Visual indicators:</strong> Green "YES (All Sites)" for AUTHALLSITES=1 users</li>
                    </ul>

                    <div class="code-block">
                        <strong>Access URL:</strong> <code>http://127.0.0.1:5008/enhanced-profile</code>
                    </div>
                </div>
            </div>

            <!-- Navigation -->
            <div class="text-center mt-4 mb-4">
                <a href="/welcome" class="btn btn-primary btn-lg me-3">
                    <i class="fas fa-home me-2"></i>Back to Welcome
                </a>
                <a href="/enhanced-profile" class="btn btn-success btn-lg me-3">
                    <i class="fas fa-user me-2"></i>Enhanced Profile
                </a>
                <a href="/api-docs" class="btn btn-outline-secondary btn-lg">
                    <i class="fas fa-code me-2"></i>Work Order API
                </a>
            </div>
        </div>

        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    </body>
    </html>
    """
    return html

@app.route('/api/test-mxapiperuser', methods=['GET'])
def test_mxapiperuser():
    """Test MXAPIPERUSER endpoint to verify it's working."""
    if not token_manager.is_logged_in():
        return jsonify({"error": "Not authenticated"}), 401

    try:
        # Use the current session URL or token manager's base URL
        base_url = session.get('maximo_url', token_manager.base_url)
        url = f"{base_url}/oslc/os/mxapiperuser"

        params = {
            "oslc.pageSize": "5",
            "lean": "1"
        }

        logger.info(f"🔧 TEST MXAPIPERUSER: Testing endpoint {url}")
        logger.info(f"🔧 TEST MXAPIPERUSER: Params: {params}")

        response = token_manager.session.get(
            url,
            params=params,
            timeout=(5.0, 15),
            headers={"Accept": "application/json"}
        )

        logger.info(f"🔧 TEST MXAPIPERUSER: Response status: {response.status_code}")

        if response.status_code == 200:
            try:
                data = response.json()
                persons = data.get('member', [])
                logger.info(f"🔧 TEST MXAPIPERUSER: Found {len(persons)} persons")

                return jsonify({
                    "success": True,
                    "count": len(persons),
                    "sample_data": persons[:3] if persons else [],
                    "endpoint": url,
                    "params": params,
                    "raw_response_length": len(response.text)
                })
            except Exception as json_error:
                logger.error(f"🔧 TEST MXAPIPERUSER: JSON parsing error: {json_error}")
                return jsonify({
                    "success": False,
                    "error": f"JSON parsing error: {str(json_error)}",
                    "response_text": response.text[:1000],
                    "response_length": len(response.text),
                    "response_headers": dict(response.headers),
                    "endpoint": url,
                    "params": params,
                    "raw_response_repr": repr(response.text[:200])
                })
        else:
            logger.error(f"🔧 TEST MXAPIPERUSER: Failed with status {response.status_code}")
            return jsonify({
                "success": False,
                "error": f"HTTP {response.status_code}",
                "response_text": response.text[:500]
            })

    except Exception as e:
        logger.error(f"🔧 TEST MXAPIPERUSER: Exception: {str(e)}")
        return jsonify({
            "success": False,
            "error": str(e)
        })

@app.route('/api/mxapiperuser', methods=['GET'])
def get_sites():
    """Get sites for the current user using the mxapisite endpoint."""
    if not token_manager.is_logged_in():
        return jsonify({"error": "Not authenticated"}), 401

    # Use the current session URL or token manager's base URL
    base_url = session.get('maximo_url', token_manager.base_url)
    url = f"{base_url}/api/os/mxapisite"
    headers = {
        "Accept": "application/json",
        "Content-Type": "application/json",
        "apikey": "dj9sia0tu2s0sktv3oq815amtv06ior0ahlsn70o"
    }

    # SQL query to get sites for current user
    sql_query = """
    SELECT s.*, m.description as group_description
    FROM SITEAUTH s
    JOIN maxgroup m ON s.groupname = m.groupname
    WHERE s.groupname IN (
        SELECT groupname
        FROM maxgroup
        WHERE EXISTS (
            SELECT 1
            FROM groupuser
            WHERE userid = '{current_user}'
            AND groupuser.groupname = maxgroup.groupname
        )
    )
    """

    try:
        response = requests.get(url, headers=headers, params={"_sql": sql_query})
        response.raise_for_status()

        # Parse the RDF response
        data = response.json()
        sites = []

        # Extract site information from the RDF response
        if "rdf:resource" in data:
            for resource in data["rdf:resource"]:
                site_url = resource.get("rdf:resource", "")
                if site_url:
                    # Extract site ID from URL
                    site_id = site_url.split("/")[-1]
                    sites.append({
                        "site_id": site_id,
                        "url": site_url
                    })

        return jsonify({
            "status": "success",
            "sites": sites,
            "count": len(sites)
        })
    except requests.exceptions.RequestException as e:
        logger.error(f"Error fetching sites: {str(e)}")
        return jsonify({
            "status": "error",
            "message": str(e)
        }), 500

@app.route('/api/mxapisite', methods=['GET'])
def get_sites_for_user():
    """Get sites for the current user using the mxapisite endpoint."""
    if not token_manager.is_logged_in():
        return jsonify({"error": "Not authenticated"}), 401

    # Use the current session URL or token manager's base URL
    base_url = session.get('maximo_url', token_manager.base_url)
    url = f"{base_url}/api/os/mxapisite"
    headers = {
        "Accept": "application/json",
        "Content-Type": "application/json",
        "apikey": "dj9sia0tu2s0sktv3oq815amtv06ior0ahlsn70o"
    }

    # SQL query to get sites for current user
    sql_query = """
    SELECT s.*, m.description as group_description
    FROM SITEAUTH s
    JOIN maxgroup m ON s.groupname = m.groupname
    WHERE s.groupname IN (
        SELECT groupname
        FROM maxgroup
        WHERE EXISTS (
            SELECT 1
            FROM groupuser
            WHERE userid = '{current_user}'
            AND groupuser.groupname = maxgroup.groupname
        )
    )
    """

    try:
        response = requests.get(url, headers=headers, params={"_sql": sql_query})
        response.raise_for_status()

        # Parse the RDF response
        data = response.json()
        sites = []

        # Extract site information from the RDF response
        if "rdf:resource" in data:
            for resource in data["rdf:resource"]:
                site_url = resource.get("rdf:resource", "")
                if site_url:
                    # Extract site ID from URL
                    site_id = site_url.split("/")[-1]
                    sites.append({
                        "site_id": site_id,
                        "url": site_url
                    })

        return jsonify({
            "status": "success",
            "sites": sites,
            "count": len(sites)
        })
    except requests.exceptions.RequestException as e:
        logger.error(f"Error fetching sites: {str(e)}")
        return jsonify({
            "status": "error",
            "message": str(e)
        }), 500

# Site Access API Routes
@app.route('/api/site-access/<person_id>/person', methods=['GET'])
def get_person_data(person_id):
    """Get person table data for the specified person ID."""
    if 'username' not in session:
        return jsonify({'success': False, 'error': 'Not authenticated'}), 401

    try:
        data = SiteAccessService.get_person_data(person_id)
        if data:
            return jsonify({'success': True, 'data': data})
        else:
            return jsonify({'success': False, 'error': 'No person data found'})
    except Exception as e:
        logger.error(f"Error getting person data for {person_id}: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/site-access/<person_id>/maxuser', methods=['GET'])
def get_maxuser_data(person_id):
    """Get maxuser table data for the specified person ID."""
    if 'username' not in session:
        return jsonify({'success': False, 'error': 'Not authenticated'}), 401

    try:
        data = SiteAccessService.get_maxuser_data(person_id)
        if data:
            return jsonify({'success': True, 'data': data})
        else:
            return jsonify({'success': False, 'error': 'No maxuser data found'})
    except Exception as e:
        logger.error(f"Error getting maxuser data for {person_id}: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/site-access/<person_id>/groups', methods=['GET'])
def get_groups_data(person_id):
    """Get group memberships data for the specified person ID."""
    if 'username' not in session:
        return jsonify({'success': False, 'error': 'Not authenticated'}), 401

    try:
        data = SiteAccessService.get_groups_data(person_id)
        return jsonify({'success': True, 'data': data})
    except Exception as e:
        logger.error(f"Error getting groups data for {person_id}: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/site-access/<person_id>/sites', methods=['GET'])
def get_sites_data(person_id):
    """Get site authorizations data for the specified person ID."""
    if 'username' not in session:
        return jsonify({'success': False, 'error': 'Not authenticated'}), 401

    try:
        data = SiteAccessService.get_sites_data(person_id)
        return jsonify({'success': True, 'data': data})
    except Exception as e:
        logger.error(f"Error getting sites data for {person_id}: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/site-access/cache/clear', methods=['POST'])
def clear_site_access_cache():
    """Clear the site access cache."""
    if 'username' not in session:
        return jsonify({'success': False, 'error': 'Not authenticated'}), 401

    try:
        SiteAccessService.clear_cache()
        return jsonify({'success': True, 'message': 'Cache cleared successfully'})
    except Exception as e:
        logger.error(f"Error clearing site access cache: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/site-access/cache/stats', methods=['GET'])
def get_site_access_cache_stats():
    """Get site access cache statistics."""
    if 'username' not in session:
        return jsonify({'success': False, 'error': 'Not authenticated'}), 401

    try:
        stats = SiteAccessService.get_cache_stats()
        return jsonify({'success': True, 'data': stats})
    except Exception as e:
        logger.error(f"Error getting site access cache stats: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

# Task Planned Materials API Endpoints
@app.route('/api/task/<task_wonum>/planned-materials', methods=['GET'])
def get_task_planned_materials(task_wonum):
    """Get planned materials for a specific task."""
    try:
        # Check if user is logged in
        if not hasattr(token_manager, 'username') or not token_manager.username:
            return jsonify({'success': False, 'error': 'Not logged in'})

        # Get task status for logging purposes (materials now available for all statuses)
        task_status = request.args.get('status', '')
        logger.info(f"📦 MATERIALS API: Loading materials for task {task_wonum} with status {task_status}")

        # Get work order's site ID (not user's site ID)
        workorder_site_id = task_materials_service.get_workorder_site_id(task_wonum)

        logger.info(f"📦 MATERIALS API: Fetching materials for task {task_wonum}, work order site {workorder_site_id}")

        # Fetch planned materials using work order's site ID
        materials, metadata = task_materials_service.get_task_planned_materials(task_wonum, workorder_site_id)

        return jsonify({
            'success': True,
            'materials': materials,
            'metadata': metadata,
            'show_materials': True,
            'task_wonum': task_wonum,
            'site_id': workorder_site_id
        })

    except Exception as e:
        logger.error(f"Error fetching planned materials for task {task_wonum}: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e),
            'show_materials': False
        })

@app.route('/api/task/planned-materials/cache/clear', methods=['POST'])
def clear_materials_cache():
    """Clear the planned materials cache."""
    try:
        # Check if user is logged in
        if not hasattr(token_manager, 'username') or not token_manager.username:
            return jsonify({'success': False, 'error': 'Not logged in'})

        task_materials_service.clear_cache()
        return jsonify({'success': True, 'message': 'Materials cache cleared'})

    except Exception as e:
        logger.error(f"Error clearing materials cache: {str(e)}")
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/task/labor/cache/clear', methods=['POST'])
def clear_labor_cache():
    """Clear the labor cache."""
    try:
        # Check if user is logged in
        if not hasattr(token_manager, 'username') or not token_manager.username:
            return jsonify({'success': False, 'error': 'Not logged in'})

        result = task_labor_service.clear_cache()
        return jsonify(result)

    except Exception as e:
        logger.error(f"Error clearing labor cache: {str(e)}")
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/task/planned-materials/cache/stats', methods=['GET'])
def get_materials_cache_stats():
    """Get planned materials cache statistics."""
    try:
        # Check if user is logged in
        if not hasattr(token_manager, 'username') or not token_manager.username:
            return jsonify({'success': False, 'error': 'Not logged in'})

        stats = task_materials_service.get_cache_stats()
        return jsonify({'success': True, 'stats': stats})

    except Exception as e:
        logger.error(f"Error getting cache stats: {str(e)}")
        return jsonify({'success': False, 'error': str(e)})

# Task Labor API Endpoints (following exact same pattern as materials)
@app.route('/api/task/<task_wonum>/labor-records', methods=['GET'])
def get_task_labor_records(task_wonum):
    """Get labor records for a specific task using mxapiwodetail/labtrans."""
    try:
        # Check if user is logged in
        if not hasattr(token_manager, 'username') or not token_manager.username:
            return jsonify({'success': False, 'error': 'Not logged in'})

        # Get task status for access control
        task_status = request.args.get('status', '')
        logger.info(f"👷 LABOR API: Loading labor records for task {task_wonum} with status {task_status}")

        # Get work order's site ID (not user's site ID)
        workorder_site_id = task_labor_service.get_workorder_site_id(task_wonum)

        logger.info(f"👷 LABOR API: Fetching labor records for task {task_wonum}, work order site {workorder_site_id}")

        # Fetch labor records using work order's site ID
        result = task_labor_service.get_task_labor(task_wonum, workorder_site_id, task_status)

        return jsonify(result)

    except Exception as e:
        logger.error(f"👷 LABOR API: Error getting labor records for task {task_wonum}: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e),
            'show_labor': False
        })

@app.route('/api/task/labor-records/cache/clear', methods=['POST'])
def clear_labor_records_cache():
    """Clear the labor records cache."""
    try:
        # Check if user is logged in
        if not hasattr(token_manager, 'username') or not token_manager.username:
            return jsonify({'success': False, 'error': 'Not logged in'})

        result = task_labor_service.clear_cache()
        return jsonify(result)

    except Exception as e:
        logger.error(f"Error clearing labor cache: {str(e)}")
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/task/labor-records/cache/stats', methods=['GET'])
def get_labor_records_cache_stats():
    """Get labor records cache statistics."""
    try:
        # Check if user is logged in
        if not hasattr(token_manager, 'username') or not token_manager.username:
            return jsonify({'success': False, 'error': 'Not logged in'})

        stats = task_labor_service.get_cache_stats()
        return jsonify({'success': True, 'stats': stats})

    except Exception as e:
        logger.error(f"Error getting labor cache stats: {str(e)}")
        return jsonify({'success': False, 'error': str(e)})


# Inventory Search API Endpoints
@app.route('/api/test-inventory-fields', methods=['GET'])
def test_inventory_fields():
    """Test endpoint to investigate available fields in MXAPIINVENTORY."""
    try:
        logger.info("🔧 TESTING: Investigating MXAPIINVENTORY fields")

        # Test with minimal fields first
        base_url = token_manager.base_url
        inventory_url = f"{base_url}/oslc/os/mxapiinventory"

        # Test 1: Test the fields from the user's original requirements
        # Based on user requirements: ITEMNUM, DESCRIPTION, ISSUEUNIT, ORDERUNIT, CURBALTOTAL, AVBLBALANCE, etc.
        test_fields = [
            "itemnum", "siteid", "location",
            "issueunit", "orderunit", "curbaltotal", "avblbalance",
            "status", "abc", "vendor", "manufacturer", "modelnum",
            "itemtype", "rotating", "conditioncode", "itemsetid"
        ]

        params = {
            "oslc.select": ",".join(test_fields),
            "oslc.where": 'siteid="LCVKWT"',
            "oslc.pageSize": "1",
            "lean": "1"
        }

        response = token_manager.session.get(
            inventory_url,
            params=params,
            timeout=(5.0, 30),
            headers={"Accept": "application/json"}
        )

        result = {
            'test': 'user_required_fields',
            'fields_tested': test_fields,
            'status_code': response.status_code,
            'success': response.status_code == 200
        }

        if response.status_code == 200:
            try:
                data = response.json()
                result['response_keys'] = list(data.keys())
                items = data.get('member', [])
                result['items_found'] = len(items)
                if items:
                    result['available_fields'] = list(items[0].keys())
                    result['sample_item'] = items[0]
            except Exception as e:
                result['json_error'] = str(e)
                result['raw_response'] = response.text[:200]
        else:
            result['error_response'] = response.text[:200]

        return jsonify(result)

    except Exception as e:
        logger.error(f"🔧 TESTING: Error investigating fields: {str(e)}")
        return jsonify({
            'success': False,
            'error': f'Field investigation failed: {str(e)}'
        }), 500

@app.route('/api/test-item-fields', methods=['GET'])
def test_item_fields():
    """Test endpoint to investigate available fields in MXAPIITEM."""
    try:
        logger.info("🔧 TESTING: Investigating MXAPIITEM fields")

        # Test with item fields for description
        base_url = token_manager.base_url
        item_url = f"{base_url}/oslc/os/mxapiitem"

        # Test fields that might be in MXAPIITEM
        test_fields = [
            "itemnum", "description", "status", "itemtype",
            "issueunit", "orderunit", "manufacturer", "vendor",
            "modelnum", "itemsetid"
        ]

        params = {
            "oslc.select": ",".join(test_fields),
            "oslc.where": 'status="ACTIVE"',
            "oslc.pageSize": "1",
            "lean": "1"
        }

        response = token_manager.session.get(
            item_url,
            params=params,
            timeout=(5.0, 30),
            headers={"Accept": "application/json"}
        )

        result = {
            'test': 'mxapiitem_fields',
            'fields_tested': test_fields,
            'status_code': response.status_code,
            'success': response.status_code == 200
        }

        if response.status_code == 200:
            try:
                data = response.json()
                result['response_keys'] = list(data.keys())
                items = data.get('member', [])
                result['items_found'] = len(items)
                if items:
                    result['available_fields'] = list(items[0].keys())
                    result['sample_item'] = items[0]
            except Exception as e:
                result['json_error'] = str(e)
                result['raw_response'] = response.text[:200]
        else:
            result['error_response'] = response.text[:200]

        return jsonify(result)

    except Exception as e:
        logger.error(f"🔧 TESTING: Error investigating MXAPIITEM fields: {str(e)}")
        return jsonify({
            'success': False,
            'error': f'MXAPIITEM field investigation failed: {str(e)}'
        }), 500

@app.route('/api/test-specific-item', methods=['GET'])
def test_specific_item():
    """Test endpoint to check if a specific item exists in MXAPIITEM with any status."""
    try:
        item_num = request.args.get('itemnum', '6210-60-V00-0181')
        logger.info(f"🔧 TESTING: Checking if item {item_num} exists in MXAPIITEM")

        base_url = token_manager.base_url
        item_url = f"{base_url}/oslc/os/mxapiitem"

        # Test without status filter to see if item exists at all
        test_fields = ["itemnum", "description", "status", "itemtype", "issueunit", "orderunit"]

        params = {
            "oslc.select": ",".join(test_fields),
            "oslc.where": f'itemnum="{item_num}"',  # No status filter
            "oslc.pageSize": "5",
            "lean": "1"
        }

        response = token_manager.session.get(
            item_url,
            params=params,
            timeout=(5.0, 30),
            headers={"Accept": "application/json"}
        )

        result = {
            'test': 'specific_item_check',
            'item_searched': item_num,
            'status_code': response.status_code,
            'success': response.status_code == 200
        }

        if response.status_code == 200:
            try:
                data = response.json()
                items = data.get('member', [])
                result['items_found'] = len(items)
                result['items'] = items

                if items:
                    result['message'] = f'Item {item_num} EXISTS in MXAPIITEM'
                    for item in items:
                        logger.info(f"🔧 TESTING: Found item {item.get('itemnum')} with status {item.get('status')}")
                else:
                    result['message'] = f'Item {item_num} does NOT exist in MXAPIITEM'

            except Exception as e:
                result['json_error'] = str(e)
                result['raw_response'] = response.text[:200]
        else:
            result['error_response'] = response.text[:200]

        return jsonify(result)

    except Exception as e:
        logger.error(f"🔧 TESTING: Error checking specific item: {str(e)}")
        return jsonify({
            'success': False,
            'error': f'Specific item check failed: {str(e)}'
        }), 500

@app.route('/api/inventory/search', methods=['GET'])
def search_inventory_items():
    """Search inventory items by item number or description."""
    try:
        # Check if user is logged in
        if not hasattr(token_manager, 'username') or not token_manager.username:
            return jsonify({'success': False, 'error': 'Not logged in'})

        # Get search parameters
        search_term = request.args.get('q', '').strip()
        site_id = request.args.get('siteid', '').strip()
        limit = int(request.args.get('limit', 20))

        if not search_term:
            return jsonify({
                'success': False,
                'error': 'Search term is required'
            })

        if not site_id:
            return jsonify({
                'success': False,
                'error': 'Site ID is required'
            })

        logger.info(f"🔍 INVENTORY API: Searching for '{search_term}' in site {site_id}")

        # Perform inventory search
        items, metadata = inventory_search_service.search_inventory_items(search_term, site_id, limit)

        return jsonify({
            'success': True,
            'items': items,
            'metadata': metadata,
            'search_term': search_term,
            'site_id': site_id,
            'count': len(items)
        })

    except Exception as e:
        logger.error(f"Error searching inventory: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/api/inventory/item-details/<itemnum>', methods=['GET'])
def get_item_details(itemnum):
    """Get detailed item information from MXAPIITEM."""
    try:
        # Check if user is logged in
        if not hasattr(token_manager, 'username') or not token_manager.username:
            return jsonify({'success': False, 'error': 'Not logged in'})

        if not itemnum:
            return jsonify({
                'success': False,
                'error': 'Item number is required'
            })

        logger.info(f"🔍 INVENTORY API: Getting details for item {itemnum}")

        # Get item details
        item_details = inventory_search_service._get_item_details(itemnum)

        if not item_details:
            return jsonify({
                'success': False,
                'error': f'Item {itemnum} not found'
            })

        return jsonify({
            'success': True,
            'item': item_details,
            'itemnum': itemnum
        })

    except Exception as e:
        logger.error(f"Error getting item details for {itemnum}: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/api/inventory/cache/clear', methods=['POST'])
def clear_inventory_cache():
    """Clear the inventory search cache."""
    try:
        # Check if user is logged in
        if not hasattr(token_manager, 'username') or not token_manager.username:
            return jsonify({'success': False, 'error': 'Not logged in'})

        inventory_search_service.clear_cache()
        return jsonify({'success': True, 'message': 'Inventory search cache cleared'})

    except Exception as e:
        logger.error(f"Error clearing inventory cache: {str(e)}")
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/inventory/cache/stats', methods=['GET'])
def get_inventory_cache_stats():
    """Get inventory search cache statistics."""
    try:
        # Check if user is logged in
        if not hasattr(token_manager, 'username') or not token_manager.username:
            return jsonify({'success': False, 'error': 'Not logged in'})

        stats = inventory_search_service.get_cache_stats()
        return jsonify({'success': True, 'stats': stats})

    except Exception as e:
        logger.error(f"Error getting inventory cache stats: {str(e)}")
        return jsonify({'success': False, 'error': str(e)})

# Inventory Management API Endpoints
@app.route('/inventory-management')
def inventory_management():
    """Inventory Management page."""
    if 'username' not in session:
        return redirect(url_for('login'))
    return render_template('inventory_management.html')

@app.route('/asset-management')
def asset_management():
    """Asset Management page."""
    if 'username' not in session:
        return redirect(url_for('login'))
    return render_template('asset_management.html')

@app.route('/debug-workorder')
def debug_workorder():
    """Debug work order dropdown page."""
    if 'username' not in session:
        return redirect(url_for('login'))
    return send_from_directory('.', 'debug_workorder.html')

@app.route('/test-asset-relationships')
def test_asset_relationships():
    """Test asset relationships for workorders and service requests."""
    if 'username' not in session:
        return redirect(url_for('login'))

    try:
        # Get some assets to test with
        assets, metadata = asset_management_service.search_assets(
            search_term="",
            site_id="",
            limit=5,
            page=0,
            status_filter=""
        )

        if not assets:
            return "<h1>No assets found for testing</h1>"

        html = """
        <!DOCTYPE html>
        <html>
        <head>
            <title>Asset Relationship Testing</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 20px; }
                .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; }
                .success { color: green; }
                .error { color: red; }
                .warning { color: orange; }
                pre { background: #f5f5f5; padding: 10px; overflow-x: auto; }
            </style>
        </head>
        <body>
            <h1>🔍 Asset Relationship Testing Results</h1>
        """

        # Test with first asset
        test_asset = assets[0]
        assetnum = test_asset.get('assetnum', '')
        siteid = test_asset.get('siteid', '')

        html += f"""
        <div class="test-section">
            <h2>Testing Asset: {assetnum} ({siteid})</h2>
            <p><strong>Description:</strong> {test_asset.get('description', 'N/A')}</p>
            <p><strong>Status:</strong> {test_asset.get('status', 'N/A')}</p>
        </div>
        """

        # Test workorder relationships
        html += test_workorder_relationships_web(assetnum, siteid)

        # Test service request methods
        html += test_service_request_methods_web(assetnum, siteid)

        html += """
        </body>
        </html>
        """

        return html

    except Exception as e:
        return f"<h1>Error: {str(e)}</h1>"

def test_workorder_relationships_web(assetnum, siteid):
    """Test workorder relationships via web interface."""
    html = '<div class="test-section"><h3>🔧 Testing Workorder Relationships</h3>'

    # Test different relationship names
    relationship_names = ["workorder", "openwo", "allwo", "workorders"]

    base_url = getattr(token_manager, 'base_url', '')
    api_url = f"{base_url}/oslc/os/mxapiasset"

    for rel_name in relationship_names:
        try:
            params = {
                "oslc.select": f"assetnum,siteid,rel.{rel_name}{{wonum,description,status,priority}}",
                "oslc.where": f'assetnum="{assetnum}" and siteid="{siteid}"',
                "oslc.pageSize": "1",
                "lean": "1"
            }

            response = token_manager.session.get(
                api_url,
                params=params,
                timeout=(5.0, 15),
                headers={"Accept": "application/json"}
            )

            if response.status_code == 200:
                data = response.json()
                if 'member' in data and len(data['member']) > 0:
                    asset_data = data['member'][0]
                    if rel_name in asset_data:
                        workorders = asset_data[rel_name]
                        if isinstance(workorders, list) and len(workorders) > 0:
                            html += f'<p class="success">✅ rel.{rel_name}: Found {len(workorders)} workorders</p>'
                            for wo in workorders[:3]:  # Show first 3
                                html += f'<p style="margin-left: 20px;">- {wo.get("wonum", "No wonum")} - {wo.get("description", "No description")[:50]}...</p>'
                        else:
                            html += f'<p class="warning">⚠️ rel.{rel_name}: Relationship exists but no workorders found</p>'
                    else:
                        html += f'<p class="error">❌ rel.{rel_name}: Relationship not found in response</p>'
                else:
                    html += f'<p class="error">❌ rel.{rel_name}: No asset data returned</p>'
            else:
                html += f'<p class="error">❌ rel.{rel_name}: API error {response.status_code}</p>'

        except Exception as e:
            html += f'<p class="error">❌ rel.{rel_name}: Error - {str(e)}</p>'

    html += '</div>'
    return html

def test_service_request_methods_web(assetnum, siteid):
    """Test service request methods via web interface."""
    html = '<div class="test-section"><h3>🎫 Testing Service Request Methods</h3>'

    base_url = getattr(token_manager, 'base_url', '')

    # Method 1: Direct MXAPISR query
    html += '<h4>Method 1: Direct MXAPISR Query</h4>'
    try:
        api_url = f"{base_url}/oslc/os/mxapisr"
        params = {
            "oslc.select": "ticketid,description,status,priority,reportdate,assetnum,siteid",
            "oslc.where": f'assetnum="{assetnum}" and siteid="{siteid}"',
            "oslc.pageSize": "10",
            "lean": "1"
        }

        response = token_manager.session.get(
            api_url,
            params=params,
            timeout=(5.0, 15),
            headers={"Accept": "application/json"}
        )

        if response.status_code == 200:
            data = response.json()
            if 'member' in data and len(data['member']) > 0:
                html += f'<p class="success">✅ MXAPISR: Found {len(data["member"])} service requests</p>'
                for sr in data['member'][:3]:  # Show first 3
                    html += f'<p style="margin-left: 20px;">- {sr.get("ticketid", "No ID")} - {sr.get("description", "No description")[:50]}...</p>'
            else:
                html += '<p class="warning">⚠️ MXAPISR: Accessible but no service requests found for this asset</p>'
        else:
            html += f'<p class="error">❌ MXAPISR: API error {response.status_code}</p>'

    except Exception as e:
        html += f'<p class="error">❌ MXAPISR: Error - {str(e)}</p>'

    # Method 2: Asset relationship queries
    html += '<h4>Method 2: Asset Relationship Queries</h4>'
    relationship_names = ["servicerequest", "sr", "ticket", "tickets"]

    api_url = f"{base_url}/oslc/os/mxapiasset"

    for rel_name in relationship_names:
        try:
            params = {
                "oslc.select": f"assetnum,siteid,rel.{rel_name}{{ticketid,description,status,priority}}",
                "oslc.where": f'assetnum="{assetnum}" and siteid="{siteid}"',
                "oslc.pageSize": "1",
                "lean": "1"
            }

            response = token_manager.session.get(
                api_url,
                params=params,
                timeout=(5.0, 15),
                headers={"Accept": "application/json"}
            )

            if response.status_code == 200:
                data = response.json()
                if 'member' in data and len(data['member']) > 0:
                    asset_data = data['member'][0]
                    if rel_name in asset_data:
                        srs = asset_data[rel_name]
                        if isinstance(srs, list) and len(srs) > 0:
                            html += f'<p class="success">✅ rel.{rel_name}: Found {len(srs)} service requests</p>'
                            for sr in srs[:3]:  # Show first 3
                                html += f'<p style="margin-left: 20px;">- {sr.get("ticketid", "No ID")} - {sr.get("description", "No description")[:50]}...</p>'
                        else:
                            html += f'<p class="warning">⚠️ rel.{rel_name}: Relationship exists but no service requests found</p>'
                    else:
                        html += f'<p class="error">❌ rel.{rel_name}: Relationship not found in response</p>'

        except Exception as e:
            html += f'<p class="error">❌ rel.{rel_name}: Error - {str(e)}</p>'

    html += '</div>'
    return html

@app.route('/oslc/os/mxapiasset/<assetnum>/related-records')
def get_asset_related_records_oslc(assetnum):
    """OSLC endpoint to get related workorders and service requests for an asset."""
    if 'username' not in session:
        return jsonify({'error': 'Not authenticated'}), 401

    try:
        siteid = request.args.get('siteid', '')
        if not siteid:
            return jsonify({'error': 'siteid parameter is required'}), 400

        # Get related records using OSLC service
        result = asset_related_records_service.get_related_records(assetnum, siteid)

        # Format response in OSLC style
        oslc_response = {
            'member': [result],
            'responseInfo': {
                'totalCount': 1,
                'pagenum': 1,
                'href': request.url
            }
        }

        return jsonify(oslc_response)

    except Exception as e:
        logger.error(f"❌ OSLC: Error getting related records for asset {assetnum}: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/qr-inventory-scanner')
def qr_inventory_scanner():
    """QR Code Inventory Scanner page."""
    if 'username' not in session:
        return redirect(url_for('login'))
    return render_template('qr_inventory_scanner.html')

@app.route('/asset-qr-scanner')
def asset_qr_scanner():
    """Asset QR Code Scanner page."""
    if 'username' not in session:
        return redirect(url_for('login'))
    return render_template('asset_qr_scanner.html')





@app.route('/api/inventory/management/search', methods=['GET'])
def inventory_management_search():
    """Search inventory items using the inventory management service."""
    try:
        # Check if user is logged in
        if not hasattr(token_manager, 'username') or not token_manager.username:
            return jsonify({'success': False, 'error': 'Not logged in'})

        # Get search parameters
        search_term = request.args.get('q', '').strip()
        site_id = request.args.get('siteid', '').strip()
        status_filter = request.args.get('status', '').strip()
        limit = int(request.args.get('limit', 10))
        page = int(request.args.get('page', 0))

        if not site_id:
            return jsonify({
                'success': False,
                'error': 'Site ID is required'
            })

        if not search_term:
            return jsonify({
                'success': False,
                'error': 'Search term is required'
            })

        logger.info(f"🔍 INVENTORY MGMT API: Search term='{search_term}', site='{site_id}', status='{status_filter}', limit={limit}, page={page}")

        # Search by term with optional status filter
        items, metadata = inventory_management_service.search_inventory_items(
            search_term=search_term,
            site_id=site_id,
            limit=limit,
            page=page,
            status_filter=status_filter
        )

        return jsonify({
            'success': True,
            'items': items,
            'metadata': metadata
        })

    except Exception as e:
        logger.error(f"Error in inventory management search: {str(e)}")

        # Provide more specific error messages
        error_message = str(e)
        if "Session not available" in error_message:
            error_message = "Session expired. Please refresh the page and try again."
        elif "Token manager not available" in error_message:
            error_message = "Authentication error. Please log in again."
        elif "Base URL not available" in error_message:
            error_message = "Configuration error. Please contact support."

        return jsonify({
            'success': False,
            'error': error_message
        }), 500

@app.route('/api/inventory/management/cache/clear', methods=['POST'])
def clear_inventory_management_cache():
    """Clear the inventory management cache."""
    try:
        # Check if user is logged in
        if not hasattr(token_manager, 'username') or not token_manager.username:
            return jsonify({'success': False, 'error': 'Not logged in'})

        inventory_management_service.clear_cache()
        return jsonify({'success': True, 'message': 'Inventory management cache cleared'})

    except Exception as e:
        logger.error(f"Error clearing inventory management cache: {str(e)}")
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/inventory/management/item-details/<itemnum>', methods=['GET'])
def get_inventory_item_details(itemnum):
    """Get comprehensive inventory item details from MXAPIINVENTORY."""
    try:
        # Check if user is logged in
        if not hasattr(token_manager, 'username') or not token_manager.username:
            return jsonify({'success': False, 'error': 'Not logged in'})

        if not itemnum:
            return jsonify({
                'success': False,
                'error': 'Item number is required'
            })

        # Get site ID from request parameters
        site_id = request.args.get('siteid', '').strip()
        if not site_id:
            return jsonify({
                'success': False,
                'error': 'Site ID is required'
            })

        logger.info(f"🔍 INVENTORY DETAILS: Getting comprehensive details for item {itemnum} in site {site_id}")

        # Get comprehensive item details from MXAPIINVENTORY
        base_url = getattr(token_manager, 'base_url', '')
        api_url = f"{base_url}/oslc/os/mxapiinventory"

        # Complete field selection based on user mapping requirements
        select_fields = [
            "itemnum", "siteid", "location", "binnum",
            "issueunit", "orderunit", "curbaltotal", "avblbalance",
            "status", "itemtype", "itemsetid", "abc",
            "reservedqty", "hardreservedqty", "softreservedqty",
            "minlevel", "maxlevel", "orderqty", "deliverytime",
            "benchstock", "shrinkageacc", "glaccount", "controlacc",
            "statusdate", "lastissuedate"
        ]

        # Related table fields - INVCOST
        invcost_fields = [
            "invcost.avgcost", "invcost.lastcost", "invcost.stdcost",
            "invcost.conditioncode"
        ]

        # Related table fields - INVBALANCES
        invbalances_fields = [
            "invbalances.binnum", "invbalances.curbal", "invbalances.location",
            "invbalances.conditioncode", "invbalances.lotnum",
            "invbalances.physcnt", "invbalances.physcntdate",
            "invbalances.unitcost", "invbalances.stagingbin", "invbalances.stagedcurbal"
        ]

        # Related table fields - INVVENDOR
        invvendor_fields = [
            "invvendor.currencycode", "invvendor.vendor",
            "invvendor.manufacturer", "invvendor.modelnum", "invvendor.contractnum"
        ]

        all_fields = select_fields + invcost_fields + invbalances_fields + invvendor_fields

        params = {
            "oslc.select": ",".join(all_fields),
            "oslc.where": f'itemnum="{itemnum}" and siteid="{site_id}"',
            "oslc.pageSize": "1",
            "lean": "0"  # Get full response with related tables
        }

        response = token_manager.session.get(
            api_url,
            params=params,
            timeout=(3.05, 10),
            headers={"Accept": "application/json"}
        )

        if response.status_code == 200:
            data = response.json()
            items = data.get('member', [])

            if items:
                item_details = items[0]

                # Enhance with MXAPIITEM data for missing fields (description, etc.)
                try:
                    item_api_url = f"{base_url}/oslc/os/mxapiitem"
                    item_params = {
                        "oslc.select": "itemnum,description,issueunit,orderunit,status,rotating,conditionenabled,lottype",
                        "oslc.where": f'itemnum="{itemnum}"',
                        "oslc.pageSize": "1",
                        "lean": "1"
                    }

                    item_response = token_manager.session.get(
                        item_api_url,
                        params=item_params,
                        timeout=(3.05, 10),
                        headers={"Accept": "application/json"}
                    )

                    if item_response.status_code == 200:
                        item_data = item_response.json()
                        item_items = item_data.get('member', [])
                        if item_items:
                            # Add description and other MXAPIITEM fields - only actual values
                            item_master = item_items[0]
                            if 'description' in item_master and item_master['description']:
                                item_details['description'] = item_master['description']
                            if 'rotating' in item_master:
                                item_details['rotating'] = item_master['rotating']
                            if 'conditionenabled' in item_master:
                                item_details['conditionenabled'] = item_master['conditionenabled']
                            if 'lottype' in item_master and item_master['lottype']:
                                item_details['lottype'] = item_master['lottype']
                            logger.info(f"✅ INVENTORY DETAILS: Enhanced with MXAPIITEM data")
                except Exception as e:
                    logger.warning(f"⚠️ INVENTORY DETAILS: Could not enhance with MXAPIITEM data: {str(e)}")

                logger.info(f"✅ INVENTORY DETAILS: Found comprehensive details for {itemnum}")

                return jsonify({
                    'success': True,
                    'item': item_details,
                    'itemnum': itemnum,
                    'siteid': site_id
                })
            else:
                # Item not found in MXAPIINVENTORY, try to get details from MXAPIITEM (direct issue item)
                logger.info(f"🔍 INVENTORY DETAILS: Item {itemnum} not found in MXAPIINVENTORY for site {site_id}, checking MXAPIITEM")

                try:
                    item_api_url = f"{base_url}/oslc/os/mxapiitem"
                    item_params = {
                        "oslc.select": "itemnum,description,unitcost,rotating,conditionenabled,lottype,status,issueunit,orderunit,itemtype,itemsetid",
                        "oslc.where": f'itemnum="{itemnum}"',
                        "oslc.pageSize": "1",
                        "lean": "1"
                    }

                    logger.info(f"🔍 INVENTORY DETAILS: MXAPIITEM API URL: {item_api_url}")
                    logger.info(f"🔍 INVENTORY DETAILS: MXAPIITEM params: {item_params}")

                    item_response = token_manager.session.get(
                        item_api_url,
                        params=item_params,
                        timeout=(3.05, 10),
                        headers={"Accept": "application/json"}
                    )

                    logger.info(f"🔍 INVENTORY DETAILS: MXAPIITEM response status: {item_response.status_code}")

                    if item_response.status_code == 200:
                        item_data = item_response.json()
                        item_items = item_data.get('member', [])
                        if item_items:
                            # Create item details from MXAPIITEM data (direct issue item)
                            item_master = item_items[0]
                            item_details = {
                                'itemnum': item_master.get('itemnum', itemnum),
                                'description': item_master.get('description'),
                                'status': item_master.get('status'),
                                'issueunit': item_master.get('issueunit'),
                                'orderunit': item_master.get('orderunit'),
                                'itemtype': item_master.get('itemtype'),
                                'itemsetid': item_master.get('itemsetid'),
                                'unitcost': item_master.get('unitcost'),
                                'rotating': item_master.get('rotating'),
                                'conditionenabled': item_master.get('conditionenabled'),
                                'lottype': item_master.get('lottype'),
                                'siteid': site_id,
                                'location': 'Direct Issue',
                                'source': 'direct_issue',
                                # Direct issue items don't have inventory balances or vendor info
                                'curbaltotal': None,
                                'avblbalance': None,
                                'binnum': None,
                                'abc': None,
                                'reservedqty': None,
                                'hardreservedqty': None,
                                'softreservedqty': None,
                                'minlevel': None,
                                'maxlevel': None,
                                'orderqty': None,
                                'deliverytime': None,
                                'benchstock': None,
                                'shrinkageacc': None,
                                'glaccount': None,
                                'controlacc': None,
                                'statusdate': None,
                                'lastissuedate': None,
                                'invvendor': [],
                                'invcost': [],
                                'invbalances': []
                            }

                            logger.info(f"✅ INVENTORY DETAILS: Found direct issue item details for {itemnum}")
                            logger.info(f"🔍 INVENTORY DETAILS: Raw API response: {item_master}")
                            logger.info(f"🔍 INVENTORY DETAILS: Final item_details: {item_details}")

                            return jsonify({
                                'success': True,
                                'item': item_details,
                                'itemnum': itemnum,
                                'siteid': site_id
                            })
                        else:
                            logger.warning(f"⚠️ INVENTORY DETAILS: Item {itemnum} not found in MXAPIITEM either")
                            return jsonify({
                                'success': False,
                                'error': f'Item {itemnum} not found'
                            })
                    else:
                        logger.error(f"❌ INVENTORY DETAILS: MXAPIITEM API returned status {item_response.status_code}")
                        logger.error(f"❌ INVENTORY DETAILS: MXAPIITEM response text: {item_response.text[:500]}")
                        return jsonify({
                            'success': False,
                            'error': f'Item {itemnum} not found in site {site_id}'
                        })
                except Exception as e:
                    logger.error(f"❌ INVENTORY DETAILS: Error checking MXAPIITEM: {str(e)}")
                    return jsonify({
                        'success': False,
                        'error': f'Item {itemnum} not found in site {site_id}'
                    })
        else:
            logger.error(f"❌ INVENTORY DETAILS: API returned status {response.status_code}")
            logger.error(f"❌ INVENTORY DETAILS: Response text: {response.text[:500]}")
            logger.error(f"❌ INVENTORY DETAILS: Request URL: {api_url}")
            logger.error(f"❌ INVENTORY DETAILS: Request params: {params}")
            return jsonify({
                'success': False,
                'error': f'API error: {response.status_code}'
            })

    except Exception as e:
        logger.error(f"Error getting inventory item details for {itemnum}: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        })



    except Exception as e:
        logger.error(f"Error getting inventory cache stats: {str(e)}")
        return jsonify({'success': False, 'error': str(e)})


@app.route('/api/inventory/comprehensive-details/<itemnum>', methods=['GET'])
def get_comprehensive_inventory_details(itemnum):
    """Get comprehensive inventory details from both MXAPIITEM and MXAPIINVENTORY endpoints."""
    try:
        # Check if user is logged in
        if not hasattr(token_manager, 'username') or not token_manager.username:
            return jsonify({'success': False, 'error': 'Not logged in'})

        # Get site ID parameter
        site_id = request.args.get('siteid', '').strip()
        if not site_id:
            return jsonify({
                'success': False,
                'error': 'Site ID is required'
            })

        # Get optional inventory ID parameter for specific record details
        inventory_id = request.args.get('inventoryid', '').strip() or None

        logger.info(f"📦 COMPREHENSIVE DETAILS API: Getting details for '{itemnum}' in site '{site_id}'")

        # Initialize inventory details service
        from backend.services.inventory_details_service import InventoryDetailsService
        details_service = InventoryDetailsService(token_manager)

        # Get comprehensive details
        details, metadata = details_service.get_inventory_details(itemnum, site_id, inventory_id)

        if details:
            logger.info(f"✅ COMPREHENSIVE DETAILS API: Found details for '{itemnum}' in {metadata.get('load_time', 0):.2f}s")
            return jsonify({
                'success': True,
                'details': details,
                'metadata': metadata,
                'itemnum': itemnum,
                'siteid': site_id
            })
        else:
            logger.warning(f"⚠️ COMPREHENSIVE DETAILS API: No details found for '{itemnum}' in site '{site_id}'")
            return jsonify({
                'success': False,
                'error': f'No details found for item {itemnum} in site {site_id}',
                'metadata': metadata
            })

    except Exception as e:
        logger.error(f"❌ COMPREHENSIVE DETAILS API: Error getting details for '{itemnum}': {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        })


@app.route('/api/inventory/availability/<itemnum>', methods=['GET'])
def get_inventory_availability(itemnum):
    """Get comprehensive item availability data from multiple Maximo endpoints."""
    try:
        # Get site ID parameter
        site_id = request.args.get('siteid', '').strip()
        if not site_id:
            return jsonify({
                'success': False,
                'error': 'Site ID is required'
            })

        if not itemnum:
            return jsonify({
                'success': False,
                'error': 'Item number is required'
            })

        logger.info(f"🔍 COMPREHENSIVE AVAILABILITY API: Getting availability for item '{itemnum}' in site '{site_id}'")

        # Build API URL using established pattern
        base_url = getattr(token_manager, 'base_url', '')
        api_url = f"{base_url}/oslc/os/mxapiinventory"

        # Select availability-related fields based on discovered patterns
        availability_fields = [
            "itemnum", "siteid", "location", "inventoryid", "itemsetid", "status",
            "avblbalance", "curbaltotal", "reservedqty", "hardreservedqty", "softreservedqty",
            "minlevel", "maxlevel", "orderqty", "deliverytime", "benchstock",
            "issueunit", "orderunit", "itemtype", "abc", "statusdate", "lastissuedate",
            "orgid", "expiredqty", "invreserveqty", "shippedqty", "stagedqty"
        ]

        # Add ALL nested array fields for comprehensive availability data
        nested_fields = [
            "invcost",           # Cost information
            "invbalances",       # Detailed balance records with lots/bins
            "invvendor",         # Vendor information and alternates
            "itemcondition",     # Item condition data
            "matusetrans",       # Material use transactions (reservations/usage)
            "matrectrans",       # Material receipt transactions (purchasing)
            "transfercuritem"    # Transfer information
        ]
        all_fields = availability_fields + nested_fields

        # Build OSLC where clause
        where_clause = f'itemnum="{itemnum}" and siteid="{site_id}" and status!="OBSOLETE"'

        params = {
            "oslc.select": ",".join(all_fields),
            "oslc.where": where_clause,
            "oslc.pageSize": "50",  # Get all inventory records for the item
            "lean": "1"
        }

        # Make API call using established authentication pattern
        response = token_manager.session.get(
            api_url,
            params=params,
            timeout=(3.05, 15),
            headers={"Accept": "application/json"}
        )

        if response.status_code != 200:
            logger.error(f"❌ AVAILABILITY API: HTTP {response.status_code}: {response.text}")
            return jsonify({
                'success': False,
                'error': f'Maximo API error: HTTP {response.status_code}'
            })

        # Parse response with debugging
        logger.info(f"🔍 AVAILABILITY API: Response status={response.status_code}, content-type={response.headers.get('content-type')}")
        logger.info(f"🔍 AVAILABILITY API: Response text (first 200 chars): {response.text[:200]}")

        try:
            data = response.json()
            inventory_records = data.get('member', [])
        except json.JSONDecodeError as e:
            logger.error(f"❌ AVAILABILITY API: JSON decode error: {str(e)}")
            logger.error(f"❌ AVAILABILITY API: Full response text: {response.text}")
            return jsonify({
                'success': False,
                'error': f'Invalid JSON response from Maximo API: {str(e)}'
            })

        logger.info(f"✅ AVAILABILITY API: Found {len(inventory_records)} inventory records for '{itemnum}'")

        # Process nested arrays following established patterns
        processed_records = []
        for record in inventory_records:
            processed_record = dict(record)

            # Process invcost array
            if 'invcost' in record and isinstance(record['invcost'], list) and record['invcost']:
                processed_record['cost_info'] = record['invcost'][0] if record['invcost'] else {}

            # Process invbalances array (lots and bins)
            if 'invbalances' in record and isinstance(record['invbalances'], list):
                processed_record['balance_details'] = record['invbalances']
                processed_record['balance_count'] = len(record['invbalances'])
                # Extract lot information
                processed_record['lots_info'] = [
                    {
                        'binnum': bal.get('binnum'),
                        'lotnum': bal.get('lotnum'),
                        'conditioncode': bal.get('conditioncode'),
                        'curbal': bal.get('curbal'),
                        'physcnt': bal.get('physcnt'),
                        'physcntdate': bal.get('physcntdate'),
                        'reconciled': bal.get('reconciled'),
                        'stagedcurbal': bal.get('stagedcurbal'),
                        'stagingbin': bal.get('stagingbin')
                    }
                    for bal in record['invbalances']
                ]
            else:
                processed_record['balance_details'] = []
                processed_record['balance_count'] = 0
                processed_record['lots_info'] = []

            # Process invvendor array (vendor and alternate items)
            if 'invvendor' in record and isinstance(record['invvendor'], list):
                processed_record['vendor_info'] = record['invvendor'][0] if record['invvendor'] else {}
                processed_record['all_vendors'] = record['invvendor']
                processed_record['alternate_items'] = [
                    {
                        'vendor': vendor.get('vendor'),
                        'manufacturer': vendor.get('manufacturer'),
                        'modelnum': vendor.get('modelnum'),
                        'catalogcode': vendor.get('catalogcode'),
                        'orderunit': vendor.get('orderunit'),
                        'conversion': vendor.get('conversion')
                    }
                    for vendor in record['invvendor']
                ]
            else:
                processed_record['vendor_info'] = {}
                processed_record['all_vendors'] = []
                processed_record['alternate_items'] = []

            # Process matusetrans array (reservations and usage)
            if 'matusetrans' in record and isinstance(record['matusetrans'], list):
                processed_record['reservations_usage'] = [
                    {
                        'quantity': usage.get('quantity'),
                        'actualdate': usage.get('actualdate'),
                        'wonum': usage.get('wonum'),
                        'mrnum': usage.get('mrnum'),
                        'mrlinenum': usage.get('mrlinenum'),
                        'issuetype': usage.get('issuetype'),
                        'binnum': usage.get('binnum'),
                        'lotnum': usage.get('lotnum'),
                        'conditioncode': usage.get('conditioncode'),
                        'storeloc': usage.get('storeloc'),
                        'assetnum': usage.get('assetnum'),
                        'linecost': usage.get('linecost')
                    }
                    for usage in record['matusetrans']
                ]
                processed_record['reservations_count'] = len(record['matusetrans'])
            else:
                processed_record['reservations_usage'] = []
                processed_record['reservations_count'] = 0

            # Process matrectrans array (material receipt transactions - as Maximo provides)
            if 'matrectrans' in record and isinstance(record['matrectrans'], list):
                processed_record['matrectrans_data'] = [
                    {
                        # Core transaction info
                        'matrectransid': receipt.get('matrectransid'),
                        'issuetype': receipt.get('issuetype'),
                        'issuetype_description': receipt.get('issuetype_description'),
                        'linetype': receipt.get('linetype'),
                        'linetype_description': receipt.get('linetype_description'),

                        # Quantities and costs
                        'receiptquantity': receipt.get('receiptquantity'),
                        'quantity': receipt.get('quantity'),
                        'unitcost': receipt.get('unitcost'),
                        'linecost': receipt.get('linecost'),
                        'actualcost': receipt.get('actualcost'),
                        'receivedunit': receipt.get('receivedunit'),

                        # Dates
                        'actualdate': receipt.get('actualdate'),
                        'transdate': receipt.get('transdate'),
                        'financialperiod': receipt.get('financialperiod'),

                        # PO information (when applicable)
                        'ponum': receipt.get('ponum'),
                        'polinenum': receipt.get('polinenum'),
                        'vendor': receipt.get('vendor'),

                        # Location information
                        'fromsiteid': receipt.get('fromsiteid'),
                        'fromstoreloc': receipt.get('fromstoreloc'),
                        'tostoreloc': receipt.get('tostoreloc'),
                        'fromconditioncode': receipt.get('fromconditioncode'),
                        'conditioncode': receipt.get('conditioncode'),

                        # Additional Maximo fields
                        'enterby': receipt.get('enterby'),
                        'enterbyemail': receipt.get('enterbyemail'),
                        'description': receipt.get('description'),
                        'currencycode': receipt.get('currencycode'),
                        'exchangerate': receipt.get('exchangerate'),
                        'commodity': receipt.get('commodity'),
                        'commoditygroup': receipt.get('commoditygroup')
                    }
                    for receipt in record['matrectrans']
                ]
                processed_record['matrectrans_count'] = len(record['matrectrans'])
            else:
                processed_record['matrectrans_data'] = []
                processed_record['matrectrans_count'] = 0

            processed_records.append(processed_record)

        # Calculate comprehensive summary availability data
        total_available = sum(float(record.get('avblbalance', 0) or 0) for record in processed_records)
        total_current = sum(float(record.get('curbaltotal', 0) or 0) for record in processed_records)
        total_reserved = sum(float(record.get('reservedqty', 0) or 0) for record in processed_records)
        total_lots = sum(record.get('balance_count', 0) for record in processed_records)
        total_reservations = sum(record.get('reservations_count', 0) for record in processed_records)
        total_matrectrans = sum(record.get('matrectrans_count', 0) for record in processed_records)
        total_vendors = sum(len(record.get('all_vendors', [])) for record in processed_records)

        availability_summary = {
            'total_available_balance': total_available,
            'total_current_balance': total_current,
            'total_reserved_quantity': total_reserved,
            'total_locations': len(processed_records),
            'total_lots_bins': total_lots,
            'total_reservation_records': total_reservations,
            'total_matrectrans_records': total_matrectrans,
            'total_vendor_records': total_vendors,
            'item_number': itemnum,
            'site_id': site_id
        }

        # Fetch additional availability data from separate endpoints (with error handling)
        po_data = fetch_purchase_orders(itemnum, site_id)
        pr_data = fetch_purchase_requisitions(itemnum, site_id)
        reservations_data = fetch_reservations(itemnum, site_id, processed_records)

        # Update summary with additional data
        availability_summary.update({
            'total_purchase_orders': len(po_data),
            'total_purchase_requisitions': len(pr_data),
            'total_reservations': len(reservations_data)
        })

        return jsonify({
            'success': True,
            'availability_summary': availability_summary,
            'inventory_records': processed_records,
            'purchase_orders': po_data,
            'purchase_requisitions': pr_data,
            'reservations': reservations_data,
            'metadata': {
                'itemnum': itemnum,
                'siteid': site_id,
                'record_count': len(processed_records),
                'api_endpoints': ['MXAPIINVENTORY', 'MXAPIPO', 'MXAPIPR', 'MXAPIINVRES'],
                'query_timestamp': datetime.datetime.now().isoformat()
            }
        })

    except Exception as e:
        logger.error(f"❌ AVAILABILITY API: Error getting availability for '{itemnum}': {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        })


def fetch_purchase_orders(itemnum, site_id):
    """Fetch purchase order data for the item from MXAPIPO endpoint.

    Uses Session Token authentication as primary method with API Key fallback.
    Filters for ACTIVE purchase orders only (excludes CAN and CLOSE status).
    """
    try:
        base_url = getattr(token_manager, 'base_url', '')
        if not base_url:
            base_url = os.environ.get('MAXIMO_BASE_URL', 'https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo')

        # Check if user is authenticated for session auth
        user_authenticated = hasattr(token_manager, 'username') and token_manager.username

        # TEMPORARY FIX: Use API key directly since we know it works reliably
        # Session auth with OSLC nested filtering has issues
        api_key = os.environ.get('MAXIMO_API_KEY')
        if api_key:
            api_url = f"{base_url}/api/os/mxapipo"
            auth_method = "API Key (Reliable)"
            headers = {
                "Accept": "application/json",
                "apikey": api_key
            }
            logger.info("🔍 PO DATA: Using API key for reliable results")
        elif user_authenticated:
            # Fallback to session if no API key
            api_url = f"{base_url}/oslc/os/mxapipo"
            auth_method = "Session Token (Fallback)"
            headers = {"Accept": "application/json"}
            logger.info("🔍 PO DATA: No API key, falling back to session auth")
        else:
            logger.warning("⚠️ PO DATA: No authentication available")
            return []

        # Select PO fields as shown in Maximo availability
        po_fields = [
            "ponum", "status", "status_description", "currencycode",
            "totalcost", "pretaxtotal", "siteid", "vendor", "description",
            "orderdate", "requireddate", "receipts", "internal"
        ]

        # Add nested poline fields for item-specific data
        nested_fields = ["poline"]
        all_fields = po_fields + nested_fields

        # FIXED: Use nested filter like PRs - this is the correct approach that works
        # The issue was pagination - we need to filter by itemnum directly, not post-process
        where_clause = f'poline.itemnum="{itemnum}" and poline.siteid="{site_id}" and status!="CAN" and status!="CLOSE"'

        params = {
            "oslc.select": ",".join(all_fields),
            "oslc.where": where_clause,
            "oslc.pageSize": "20",  # Reduced page size to avoid timeouts
            "lean": "1"
        }

        logger.info(f"🔍 PO QUERY: URL={api_url}")
        logger.info(f"🔍 PO QUERY: AUTH={auth_method}")
        logger.info(f"🔍 PO QUERY: WHERE={where_clause}")
        logger.info(f"🔍 PO QUERY: PARAMS={params}")

        # Make the request - API key is now primary method
        if auth_method.startswith("API Key"):
            # Use API key directly (most reliable)
            response = requests.get(
                api_url,
                params=params,
                timeout=(3.05, 30),  # Increased timeout for PO queries
                headers=headers
            )
        else:
            # Use session authentication (fallback)
            response = token_manager.session.get(
                api_url,
                params=params,
                timeout=(3.05, 30),  # Increased timeout for PO queries
                headers=headers
            )

            # Check if session auth failed (returns HTML login page)
            if response.status_code == 200 and response.text.strip().startswith('<!doctype html>'):
                logger.warning("⚠️ PO DATA: Session auth returned login page, no API key available")
                return []

        logger.info(f"🔍 PO RESPONSE: Status={response.status_code}")
        logger.info(f"🔍 PO RESPONSE: Headers={dict(response.headers)}")
        if response.status_code != 200:
            logger.error(f"🔍 PO RESPONSE: Full Text={response.text}")
        else:
            logger.info(f"🔍 PO RESPONSE: Text={response.text[:500]}")

        if response.status_code == 200:
            try:
                data = response.json()
                po_records = data.get('member', [])
            except json.JSONDecodeError as e:
                logger.error(f"❌ PO DATA: JSON decode error - likely session authentication issue for MXAPIPO endpoint")
                logger.error(f"❌ PO DATA: Response content type: {response.headers.get('content-type', 'unknown')}")
                logger.error(f"❌ PO DATA: Response indicates HTML login page instead of JSON data")
                return []

            # Process MXAPIPO/POLINE data - filter by itemnum only (site already filtered at PO level)
            processed_pos = []
            for po in po_records:
                if 'poline' in po and isinstance(po['poline'], list):
                    for line in po['poline']:
                        # Filter by itemnum only - siteid is already filtered at PO level
                        if line.get('itemnum') == itemnum:
                            processed_pos.append({
                            'ponum': po.get('ponum'),
                            'storeroom': line.get('storeloc'),
                            'internal': po.get('internal', False),
                            'status': po.get('status'),
                            'status_description': po.get('status_description'),
                            'currency': po.get('currencycode'),
                            'unit_cost': line.get('unitcost'),
                            'quantity_ordered': line.get('orderqty'),
                            'quantity_received': line.get('receivedqty', 0),
                            'vendor': po.get('vendor'),
                            'order_date': po.get('orderdate'),
                            'required_date': po.get('requireddate'),
                            'line_number': line.get('polinenum'),
                            'line_cost': line.get('linecost')
                        })

            logger.info(f"✅ PO DATA: Found {len(processed_pos)} purchase order lines for '{itemnum}'")
            return processed_pos
        else:
            logger.warning(f"⚠️ PO DATA: HTTP {response.status_code} from MXAPIPO - session authentication may not work for this endpoint")
            logger.warning(f"⚠️ PO DATA: Content type: {response.headers.get('content-type', 'unknown')}")
            return []

    except Exception as e:
        logger.error(f"❌ PO DATA: Error fetching PO data: {str(e)}")
        return []


def fetch_reservations(itemnum, site_id, inventory_records):
    """Fetch reservation data for the item from MXAPIINVRES endpoint.

    Straightforward approach: inventory.location = INVRESERVE.location
    and inventory.itemnum = INVRESERVE.itemnum and inventory.siteid = INVRESERVE.siteid
    """
    try:
        base_url = getattr(token_manager, 'base_url', '')
        if not base_url:
            base_url = os.environ.get('MAXIMO_BASE_URL', 'https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo')

        # Check if user is authenticated for session auth
        user_authenticated = hasattr(token_manager, 'username') and token_manager.username

        # Use API key directly since we know it works reliably
        api_key = os.environ.get('MAXIMO_API_KEY')
        if api_key:
            api_url = f"{base_url}/api/os/mxapiinvres"
            auth_method = "API Key (Reliable)"
            headers = {
                "Accept": "application/json",
                "apikey": api_key
            }
            logger.info("🔍 RESERVATIONS DATA: Using API key for reliable results")
        elif user_authenticated:
            # Fallback to session if no API key
            api_url = f"{base_url}/oslc/os/mxapiinvres"
            auth_method = "Session Token (Fallback)"
            headers = {"Accept": "application/json"}
            logger.info("🔍 RESERVATIONS DATA: No API key, falling back to session auth")
        else:
            logger.warning("⚠️ RESERVATIONS DATA: No authentication available")
            return []

        # Extract unique locations from inventory records
        locations = set()
        for record in inventory_records:
            # Get location from the main record
            if record.get('location'):
                locations.add(record.get('location'))

            # Get locations from balance details (invbalances)
            balance_details = record.get('balance_details', [])
            for balance in balance_details:
                if balance.get('location'):
                    locations.add(balance.get('location'))

        locations = list(locations)
        logger.info(f"🔍 RESERVATIONS: Found {len(locations)} unique locations: {locations}")

        # FIXED: Use simple query without complex location filter to avoid HTTP 400
        # The complex location filter with 12 OR conditions causes HTTP 400 error
        where_clause = f'itemnum="{itemnum}" and siteid="{site_id}"'
        logger.info(f"🔍 RESERVATIONS: Using simplified item+site filter (avoiding complex location filter)")

        params = {
            "oslc.select": "requestnum,reservationtype,itemnum,itemtype,description,storeroom,conditioncode,reservedqty,wonum,taskid,tostoreroom,tosite,ponum,requireddate,location,siteid",
            "oslc.where": where_clause,
            "oslc.pageSize": "50",
            "lean": "1"
        }

        logger.info(f"🔍 RESERVATIONS QUERY: URL={api_url}")
        logger.info(f"🔍 RESERVATIONS QUERY: AUTH={auth_method}")
        logger.info(f"🔍 RESERVATIONS QUERY: WHERE={where_clause}")

        # Make the request
        if auth_method.startswith("API Key"):
            response = requests.get(api_url, params=params, timeout=(3.05, 15), headers=headers)
        else:
            response = token_manager.session.get(api_url, params=params, timeout=(3.05, 15), headers=headers)

            # Check if session auth failed
            if response.status_code == 200 and response.text.strip().startswith('<!doctype html>'):
                logger.warning("⚠️ RESERVATIONS DATA: Session auth returned login page")
                return []

        logger.info(f"🔍 RESERVATIONS RESPONSE: Status={response.status_code}")

        if response.status_code == 200:
            try:
                data = response.json()
                reservation_records = data.get('member', [])

                # Process MXAPIINVRES data - straightforward mapping
                processed_reservations = []
                for reservation in reservation_records:
                    processed_reservations.append({
                        'request_num': reservation.get('requestnum'),
                        'reservation_type': reservation.get('reservationtype'),
                        'item_num': reservation.get('itemnum'),
                        'item_type': reservation.get('itemtype'),
                        'description': reservation.get('description'),
                        'storeroom': reservation.get('storeroom'),
                        'storeroom_site': reservation.get('siteid'),  # Direct field, not nested
                        'condition_code': reservation.get('conditioncode'),
                        'reserved_quantity': reservation.get('reservedqty'),
                        'work_order': reservation.get('wonum'),
                        'task': reservation.get('taskid'),
                        'to_storeroom': reservation.get('tostoreroom'),
                        'to_site': reservation.get('tosite'),
                        'purchase_order': reservation.get('ponum'),
                        'required_date': reservation.get('requireddate'),
                        'location': reservation.get('location')  # Include location for verification
                    })

                logger.info(f"✅ RESERVATIONS DATA: Found {len(processed_reservations)} reservations for '{itemnum}'")

                # If no results with location filter, try fallback
                if len(processed_reservations) == 0 and locations:
                    logger.info(f"🔍 RESERVATIONS: Trying fallback without location filter")
                    fallback_where = f'itemnum="{itemnum}" and siteid="{site_id}"'
                    fallback_params = dict(params)
                    fallback_params["oslc.where"] = fallback_where

                    if auth_method.startswith("API Key"):
                        fallback_response = requests.get(api_url, params=fallback_params, timeout=(3.05, 15), headers=headers)
                    else:
                        fallback_response = token_manager.session.get(api_url, params=fallback_params, timeout=(3.05, 15), headers=headers)

                    if fallback_response.status_code == 200:
                        try:
                            fallback_data = fallback_response.json()
                            fallback_records = fallback_data.get('member', [])

                            for reservation in fallback_records:
                                processed_reservations.append({
                                    'request_num': reservation.get('requestnum'),
                                    'reservation_type': reservation.get('reservationtype'),
                                    'item_num': reservation.get('itemnum'),
                                    'item_type': reservation.get('itemtype'),
                                    'description': reservation.get('description'),
                                    'storeroom': reservation.get('storeroom'),
                                    'storeroom_site': reservation.get('siteid'),
                                    'condition_code': reservation.get('conditioncode'),
                                    'reserved_quantity': reservation.get('reservedqty'),
                                    'work_order': reservation.get('wonum'),
                                    'task': reservation.get('taskid'),
                                    'to_storeroom': reservation.get('tostoreroom'),
                                    'to_site': reservation.get('tosite'),
                                    'purchase_order': reservation.get('ponum'),
                                    'required_date': reservation.get('requireddate'),
                                    'location': reservation.get('location')
                                })

                            logger.info(f"✅ RESERVATIONS FALLBACK: Found {len(fallback_records)} additional reservations")
                        except json.JSONDecodeError:
                            logger.error("❌ RESERVATIONS FALLBACK: JSON decode error")

                return processed_reservations

            except json.JSONDecodeError as e:
                logger.error(f"❌ RESERVATIONS DATA: JSON decode error: {str(e)}")
                return []
        else:
            logger.warning(f"⚠️ RESERVATIONS DATA: HTTP {response.status_code}")
            return []

    except Exception as e:
        logger.error(f"❌ RESERVATIONS DATA: Error: {str(e)}")
        return []


def fetch_purchase_requisitions(itemnum, site_id):
    """Fetch purchase requisition data for the item from MXAPIPR endpoint.

    Uses Session Token authentication as primary method with API Key fallback.
    Filters for ACTIVE purchase requisitions only (excludes CAN and CLOSE status).
    """
    try:
        base_url = getattr(token_manager, 'base_url', '')
        if not base_url:
            base_url = os.environ.get('MAXIMO_BASE_URL', 'https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo')

        # Check if user is authenticated for session auth
        user_authenticated = hasattr(token_manager, 'username') and token_manager.username

        # TEMPORARY FIX: Use API key directly since we know it works reliably
        # Session auth with OSLC nested filtering has issues
        api_key = os.environ.get('MAXIMO_API_KEY')
        if api_key:
            api_url = f"{base_url}/api/os/mxapipr"
            auth_method = "API Key (Reliable)"
            headers = {
                "Accept": "application/json",
                "apikey": api_key
            }
            logger.info("🔍 PR DATA: Using API key for reliable results")
        elif user_authenticated:
            # Fallback to session if no API key
            api_url = f"{base_url}/oslc/os/mxapipr"
            auth_method = "Session Token (Fallback)"
            headers = {"Accept": "application/json"}
            logger.info("🔍 PR DATA: No API key, falling back to session auth")
        else:
            logger.warning("⚠️ PR DATA: No authentication available")
            return []

        # Select PR fields as shown in Maximo availability
        pr_fields = [
            "prnum", "status", "status_description", "currencycode",
            "totalcost", "siteid", "requestedby", "description",
            "requestdate", "requireddate", "internal"
        ]

        # Add nested prline fields for item-specific data
        nested_fields = ["prline"]
        all_fields = pr_fields + nested_fields

        # ENHANCED: Filter for specific item AND exclude cancelled/closed PRs
        # This shows only ACTIVE purchase requisitions (excludes CAN, CLOSE)
        where_clause = f'prline.itemnum="{itemnum}" and prline.siteid="{site_id}" and status!="CAN" and status!="CLOSE"'

        params = {
            "oslc.select": ",".join(all_fields),
            "oslc.where": where_clause,
            "oslc.pageSize": "50",
            "lean": "1"
        }

        logger.info(f"🔍 PR QUERY: URL={api_url}")
        logger.info(f"🔍 PR QUERY: AUTH={auth_method}")
        logger.info(f"🔍 PR QUERY: WHERE={where_clause}")
        logger.info(f"🔍 PR QUERY: PARAMS={params}")

        # Make the request - API key is now primary method
        if auth_method.startswith("API Key"):
            # Use API key directly (most reliable)
            response = requests.get(
                api_url,
                params=params,
                timeout=(3.05, 15),
                headers=headers
            )
        else:
            # Use session authentication (fallback)
            response = token_manager.session.get(
                api_url,
                params=params,
                timeout=(3.05, 15),
                headers=headers
            )

            # Check if session auth failed (returns HTML login page)
            if response.status_code == 200 and response.text.strip().startswith('<!doctype html>'):
                logger.warning("⚠️ PR DATA: Session auth returned login page, no API key available")
                return []

        if response.status_code == 200:
            try:
                data = response.json()
                pr_records = data.get('member', [])
            except json.JSONDecodeError as e:
                logger.error(f"❌ PR DATA: JSON decode error: {str(e)}")
                logger.error(f"❌ PR DATA: Response text: {response.text[:200]}")
                return []

            # Process MXAPIPR/PRLINE data - filter by itemnum and siteid in processing
            processed_prs = []
            for pr in pr_records:
                if 'prline' in pr and isinstance(pr['prline'], list):
                    for line in pr['prline']:
                        # Filter by itemnum only - siteid is already filtered at PR level
                        if line.get('itemnum') == itemnum:
                            processed_prs.append({
                            'prnum': pr.get('prnum'),
                            'storeroom': line.get('storeloc'),
                            'internal': pr.get('internal', False),
                            'status': pr.get('status'),
                            'status_description': pr.get('status_description'),
                            'currency': pr.get('currencycode'),
                            'unit_cost': line.get('unitcost'),
                            'quantity_ordered': line.get('qty'),
                            'requested_by': pr.get('requestedby'),
                            'request_date': pr.get('requestdate'),
                            'required_date': pr.get('requireddate'),
                            'line_number': line.get('prlinenum'),
                            'line_cost': line.get('linecost')
                        })

            logger.info(f"✅ PR DATA: Found {len(processed_prs)} purchase requisition lines for '{itemnum}'")
            return processed_prs
        else:
            logger.warning(f"⚠️ PR DATA: HTTP {response.status_code} from MXAPIPR")
            return []

    except Exception as e:
        logger.error(f"❌ PR DATA: Error fetching PR data: {str(e)}")
        return []


# Inventory Management API Endpoints - Sites
@app.route('/api/inventory/available-sites', methods=['GET'])
def api_get_inventory_available_sites():
    """API endpoint for getting user's available sites for inventory search."""
    if 'username' not in session:
        return jsonify({'error': 'Not authenticated'}), 401

    try:
        # Get available sites from enhanced profile service
        sites = enhanced_profile_service.get_available_sites(use_cache=True)

        # Get user's default site for pre-selection
        user_profile = enhanced_profile_service.get_user_profile()
        default_site = user_profile.get('defaultSite', '') if user_profile else ''

        logger.info(f"🏢 INVENTORY SITES: Retrieved {len(sites)} available sites for inventory search")

        return jsonify({
            'success': True,
            'sites': sites,
            'default_site': default_site,
            'total_count': len(sites)
        })

    except Exception as e:
        logger.error(f"Error fetching inventory available sites: {e}")
        return jsonify({'error': str(e)}), 500


# Asset Management API Endpoints
@app.route('/api/asset/search', methods=['GET'])
def search_assets():
    """Search assets using MXAPIASSET endpoint."""
    try:
        # Check if user is logged in
        if not hasattr(token_manager, 'username') or not token_manager.username:
            return jsonify({'success': False, 'error': 'Not logged in'})

        # Get search parameters
        search_term = request.args.get('q', '').strip()
        site_id = request.args.get('siteid', '').strip()
        status_filter = request.args.get('status', '').strip()
        type_filter = request.args.get('type', '').strip()
        limit = int(request.args.get('limit', 20))
        page = int(request.args.get('page', 0))

        logger.info(f"🔍 ASSET: Searching for '{search_term}' in site '{site_id}' with status '{status_filter}' and type '{type_filter}'")

        # Perform search using asset management service
        assets, metadata = asset_management_service.search_assets(
            search_term=search_term,
            site_id=site_id,
            limit=limit,
            page=page,
            status_filter=status_filter
        )

        return jsonify({
            'success': True,
            'assets': assets,
            'metadata': metadata
        })

    except Exception as e:
        logger.error(f"Error searching assets: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/api/asset/create-workorder', methods=['POST'])
def create_asset_workorder():
    """Create a work order for an asset using the asset creation service."""
    try:
        # Check if user is logged in
        if not hasattr(token_manager, 'username') or not token_manager.username:
            return jsonify({'success': False, 'error': 'Not logged in'})

        # Get request data
        data = request.get_json()
        if not data:
            return jsonify({'success': False, 'error': 'No data provided'})

        # Validate required fields
        required_fields = ['assetnum', 'siteid', 'description']
        for field in required_fields:
            if not data.get(field):
                return jsonify({'success': False, 'error': f'{field} is required'})

        logger.info(f"🔧 ASSET: Creating work order for asset {data.get('assetnum')} in site {data.get('siteid')}")

        # Use asset creation service to create work order
        result = asset_creation_service.create_work_order(data)

        if result.get('success'):
            logger.info(f"✅ ASSET: Successfully created work order for asset {data.get('assetnum')}")
            return jsonify(result)
        else:
            logger.error(f"❌ ASSET: Failed to create work order for asset {data.get('assetnum')}: {result.get('error')}")
            return jsonify(result)

    except Exception as e:
        logger.error(f"Error creating work order for asset: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/api/asset/create-servicerequest', methods=['POST'])
def create_asset_servicerequest():
    """Create a service request for an asset using the asset creation service."""
    try:
        # Check if user is logged in
        if not hasattr(token_manager, 'username') or not token_manager.username:
            return jsonify({'success': False, 'error': 'Not logged in'})

        # Get request data
        data = request.get_json()
        if not data:
            return jsonify({'success': False, 'error': 'No data provided'})

        # Validate required fields
        required_fields = ['assetnum', 'siteid', 'description']
        for field in required_fields:
            if not data.get(field):
                return jsonify({'success': False, 'error': f'{field} is required'})

        logger.info(f"🔧 ASSET: Creating service request for asset {data.get('assetnum')} in site {data.get('siteid')}")

        # Use asset creation service to create service request
        result = asset_creation_service.create_service_request(data)

        if result.get('success'):
            logger.info(f"✅ ASSET: Successfully created service request for asset {data.get('assetnum')}")
            return jsonify(result)
        else:
            logger.error(f"❌ ASSET: Failed to create service request for asset {data.get('assetnum')}: {result.get('error')}")
            return jsonify(result)

    except Exception as e:
        logger.error(f"Error creating service request for asset: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/api/current-user', methods=['GET'])
def get_current_user():
    """Get current user information including personid for form population."""
    try:
        # Check if user is logged in
        if not hasattr(token_manager, 'username') or not token_manager.username:
            return jsonify({'success': False, 'error': 'Not logged in'})

        # Get user profile
        user_profile = token_manager.get_user_profile(use_cache=True)
        if not user_profile:
            return jsonify({'success': False, 'error': 'Could not retrieve user profile'})

        # Extract relevant user information
        user_info = {
            'success': True,
            'username': token_manager.username,
            'personid': user_profile.get('personid', token_manager.username),
            'displayname': user_profile.get('displayname', ''),
            'firstname': user_profile.get('firstName', ''),
            'lastname': user_profile.get('lastName', ''),
            'defaultsite': user_profile.get('defaultSite', ''),
            'insertsite': user_profile.get('insertSite', ''),
            'orgid': user_profile.get('orgid', 'USARMY')
        }

        return jsonify(user_info)

    except Exception as e:
        logger.error(f"Error getting current user info: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        })

# QR Scanner Related Records Endpoints
@app.route('/api/qr-scanner/related-records', methods=['POST'])
def get_qr_related_records():
    """Get comprehensive related records for QR scanned assets."""
    try:
        # Check if user is logged in
        if not hasattr(token_manager, 'username') or not token_manager.username:
            return jsonify({
                'success': False,
                'error': 'User not logged in'
            })

        # Get request data
        data = request.get_json()
        if not data:
            return jsonify({
                'success': False,
                'error': 'No data provided'
            })

        assetnum = data.get('assetnum')
        siteid = data.get('siteid')

        if not assetnum or not siteid:
            return jsonify({
                'success': False,
                'error': 'Asset number and site ID are required'
            })

        # Initialize the QR Related Records Service
        qr_records_service = QRRelatedRecordsService(token_manager)

        # Get comprehensive related records
        import asyncio
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)

        try:
            related_records = loop.run_until_complete(
                qr_records_service.get_comprehensive_related_records(assetnum, siteid)
            )
        finally:
            loop.close()

        return jsonify(related_records)

    except Exception as e:
        logger.error(f"❌ QR RELATED RECORDS: Error in API endpoint: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        })

# AI Work Order Analysis Endpoints
@app.route('/api/ai-analysis/analyze-workorder', methods=['POST'])
def analyze_workorder_creation():
    """Perform AI analysis for work order creation including duplication detection."""
    try:
        # Check if user is logged in
        if not hasattr(token_manager, 'username') or not token_manager.username:
            return jsonify({'success': False, 'error': 'Not logged in'})

        # Get request data
        data = request.get_json()
        if not data:
            return jsonify({'success': False, 'error': 'No data provided'})

        # Validate required fields
        asset_data = data.get('asset_data', {})
        user_description = data.get('user_description', '')

        if not asset_data.get('assetnum') or not asset_data.get('siteid'):
            return jsonify({'success': False, 'error': 'Asset number and site ID are required'})

        if not user_description.strip():
            return jsonify({'success': False, 'error': 'Work description is required'})

        logger.info(f"🤖 AI ANALYSIS: Starting analysis for asset {asset_data.get('assetnum')} in site {asset_data.get('siteid')}")

        # Perform AI analysis using the service
        import asyncio
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)

        try:
            analysis_result = loop.run_until_complete(
                ai_analysis_service.analyze_work_order_creation(asset_data, user_description)
            )
        finally:
            loop.close()

        if analysis_result.get('success'):
            logger.info(f"✅ AI ANALYSIS: Completed analysis for asset {asset_data.get('assetnum')}")
            return jsonify(analysis_result)
        else:
            logger.error(f"❌ AI ANALYSIS: Failed for asset {asset_data.get('assetnum')}: {analysis_result.get('error')}")
            return jsonify(analysis_result)

    except Exception as e:
        logger.error(f"Error in AI work order analysis: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/api/ai-analysis/update-decision', methods=['POST'])
def update_ai_analysis_decision():
    """Update user decision for an AI analysis."""
    try:
        # Check if user is logged in
        if not hasattr(token_manager, 'username') or not token_manager.username:
            return jsonify({'success': False, 'error': 'Not logged in'})

        # Get request data
        data = request.get_json()
        if not data:
            return jsonify({'success': False, 'error': 'No data provided'})

        analysis_id = data.get('analysis_id')
        user_decision = data.get('user_decision')
        created_workorder_num = data.get('created_workorder_num')

        if not analysis_id or not user_decision:
            return jsonify({'success': False, 'error': 'Analysis ID and user decision are required'})

        logger.info(f"📝 AI ANALYSIS: Updating decision for {analysis_id}: {user_decision}")

        # Update decision using the service
        import asyncio
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)

        try:
            loop.run_until_complete(
                ai_analysis_service.update_user_decision(analysis_id, user_decision, created_workorder_num)
            )
        finally:
            loop.close()

        return jsonify({'success': True, 'message': 'Decision updated successfully'})

    except Exception as e:
        logger.error(f"Error updating AI analysis decision: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/api/asset/details/<assetnum>', methods=['GET'])
def get_asset_details(assetnum):
    """Get detailed information for a specific asset."""
    try:
        # Check if user is logged in
        if not hasattr(token_manager, 'username') or not token_manager.username:
            return jsonify({'success': False, 'error': 'Not logged in'})

        # Get site ID parameter
        site_id = request.args.get('siteid', '').strip()
        if not site_id:
            return jsonify({
                'success': False,
                'error': 'Site ID is required'
            })

        logger.info(f"🔍 ASSET: Getting details for asset '{assetnum}' in site '{site_id}'")
        logger.info(f"🔍 ASSET: Asset number length: {len(assetnum)}, Site ID length: {len(site_id)}")
        logger.info(f"🔍 ASSET: Asset number type: {type(assetnum)}, Site ID type: {type(site_id)}")

        # Get asset details using asset management service
        asset_details = asset_management_service.get_asset_details(assetnum, site_id)

        if not asset_details:
            logger.warning(f"⚠️ ASSET: No details returned for asset '{assetnum}' in site '{site_id}'")
            return jsonify({
                'success': False,
                'error': f'Asset {assetnum} not found in site {site_id}. Please check the asset number and site ID.'
            })

        logger.info(f"✅ ASSET: Successfully retrieved details for asset '{assetnum}'")

        return jsonify({
            'success': True,
            'asset': asset_details
        })

    except Exception as e:
        logger.error(f"Error getting asset details for {assetnum}: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/api/asset/debug-search', methods=['GET'])
def debug_asset_search():
    """Debug endpoint to check what assets are found by search."""
    try:
        # Check if user is logged in
        if not hasattr(token_manager, 'username') or not token_manager.username:
            return jsonify({'success': False, 'error': 'Not logged in'})

        # Get search parameters
        search_term = request.args.get('q', '').strip()
        site_id = request.args.get('siteid', '').strip()

        if not search_term:
            return jsonify({'success': False, 'error': 'Search term is required'})
        if not site_id:
            return jsonify({'success': False, 'error': 'Site ID is required'})

        logger.info(f"🔍 DEBUG: Asset search debug for '{search_term}' in site '{site_id}'")

        # Get debug info from asset management service
        debug_info = asset_management_service.debug_search_results(search_term, site_id)

        return jsonify({
            'success': True,
            'debug_info': debug_info
        })

    except Exception as e:
        logger.error(f"Error in asset search debug: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/api/asset/actions/<assetnum>', methods=['GET'])
def get_asset_actions(assetnum):
    """Get available actions for a specific asset."""
    try:
        # Check if user is logged in
        if not hasattr(token_manager, 'username') or not token_manager.username:
            return jsonify({'success': False, 'error': 'Not logged in'})

        # Get site ID parameter
        site_id = request.args.get('siteid', '').strip()
        if not site_id:
            return jsonify({
                'success': False,
                'error': 'Site ID is required'
            })

        logger.info(f"🔧 ASSET: Getting actions for asset '{assetnum}' in site '{site_id}'")

        # Get asset actions using asset management service
        actions = asset_management_service.get_asset_actions(assetnum, site_id)

        return jsonify({
            'success': True,
            'actions': actions
        })

    except Exception as e:
        logger.error(f"Error getting asset actions for {assetnum}: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/api/asset/cache/clear', methods=['POST'])
def clear_asset_cache():
    """Clear the asset management cache."""
    try:
        # Check if user is logged in
        if not hasattr(token_manager, 'username') or not token_manager.username:
            return jsonify({'success': False, 'error': 'Not logged in'})

        # Clear the cache
        asset_management_service.clear_cache()

        logger.info(f"🧹 ASSET: Cache cleared by user {token_manager.username}")

        return jsonify({
            'success': True,
            'message': 'Asset cache cleared successfully'
        })

    except Exception as e:
        logger.error(f"Error clearing asset cache: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/api/asset/related-records/<assetnum>', methods=['GET'])
def get_asset_related_records(assetnum):
    """Get related workorders and service requests for an asset."""
    try:
        # Check if user is logged in
        if not hasattr(token_manager, 'username') or not token_manager.username:
            return jsonify({'success': False, 'error': 'Not logged in'})

        # Get siteid from query parameters
        siteid = request.args.get('siteid')
        if not siteid:
            return jsonify({'success': False, 'error': 'siteid parameter is required'})

        # Get related records
        result = asset_related_records_service.get_related_records(assetnum, siteid)

        return jsonify(result)

    except Exception as e:
        logger.error(f"Error getting related records for asset {assetnum}: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/api/asset/cache-stats', methods=['GET'])
def get_asset_cache_stats():
    """Get asset related records cache statistics."""
    try:
        # Check if user is logged in
        if not hasattr(token_manager, 'username') or not token_manager.username:
            return jsonify({'success': False, 'error': 'Not logged in'})

        stats = asset_related_records_service.get_cache_stats()
        return jsonify({'success': True, 'stats': stats})

    except Exception as e:
        logger.error(f"Error getting asset cache stats: {str(e)}")
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/asset/export', methods=['GET'])
def export_asset_results():
    """Export asset search results to CSV."""
    try:
        # Check if user is logged in
        if not hasattr(token_manager, 'username') or not token_manager.username:
            return jsonify({'success': False, 'error': 'Not logged in'})

        # Get search parameters
        search_term = request.args.get('q', '').strip()
        site_id = request.args.get('siteid', '').strip()
        status_filter = request.args.get('status', '').strip()
        type_filter = request.args.get('type', '').strip()

        logger.info(f"📊 ASSET: Exporting assets for '{search_term}' in site '{site_id}'")

        # Get all assets (no pagination for export)
        assets, metadata = asset_management_service.search_assets(
            search_term=search_term,
            site_id=site_id,
            limit=1000,  # Large limit for export
            page=0,
            status_filter=status_filter
        )

        if not assets:
            return jsonify({
                'success': False,
                'error': 'No assets found to export'
            })

        # Create CSV content
        import csv
        import io

        output = io.StringIO()
        writer = csv.writer(output)

        # Write header
        writer.writerow([
            'Asset Number', 'Site ID', 'Description', 'Status', 'Location',
            'Location Description', 'Asset Tag', 'Serial Number', 'Model', 'Type',
            'Asset Type', 'Organization', 'Priority', 'Vendor', 'Manufacturer'
        ])

        # Write data
        for asset in assets:
            writer.writerow([
                asset.get('assetnum', ''),
                asset.get('siteid', ''),
                asset.get('description', ''),
                asset.get('status', ''),
                asset.get('location', ''),
                asset.get('location_description', ''),
                asset.get('assettag', ''),
                asset.get('serialnum', ''),
                asset.get('model', ''),
                asset.get('type', ''),
                asset.get('assettype', ''),
                asset.get('orgid', ''),
                asset.get('priority', ''),
                asset.get('vendor', ''),
                asset.get('manufacturer', '')
            ])

        # Create response
        from flask import Response
        output.seek(0)

        return Response(
            output.getvalue(),
            mimetype='text/csv',
            headers={
                'Content-Disposition': f'attachment; filename=assets_{datetime.datetime.now().strftime("%Y%m%d_%H%M%S")}.csv'
            }
        )

    except Exception as e:
        logger.error(f"Error exporting assets: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        })


# Labor Search API Endpoints
@app.route('/api/labor/search', methods=['GET'])
def search_labor_codes():
    """Search labor codes by labor code or description."""
    try:
        # Check if user is logged in
        if not hasattr(token_manager, 'username') or not token_manager.username:
            return jsonify({'success': False, 'error': 'Not logged in'})

        # Get search parameters
        search_term = request.args.get('search_term', '').strip()
        site_id = request.args.get('site_id', '').strip()
        limit = int(request.args.get('limit', 20))
        craft = request.args.get('craft', '').strip() or None
        skill_level = request.args.get('skill_level', '').strip() or None

        # Validate required parameters
        if not search_term:
            return jsonify({
                'success': False,
                'error': 'Search term is required'
            })

        if not site_id:
            return jsonify({
                'success': False,
                'error': 'Site ID is required'
            })

        logger.info(f"🔍 LABOR API: Searching for '{search_term}' in site {site_id}")

        # Perform labor search
        labor_codes, metadata = labor_search_service.search_labor(
            search_term, site_id, limit, craft, skill_level
        )

        return jsonify({
            'success': True,
            'labor_codes': labor_codes,
            'metadata': metadata,
            'search_term': search_term,
            'site_id': site_id
        })

    except Exception as e:
        logger.error(f"Error in labor search: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/api/labor/cache/clear', methods=['POST'])
def clear_labor_search_cache():
    """Clear the labor search cache."""
    try:
        # Check if user is logged in
        if not hasattr(token_manager, 'username') or not token_manager.username:
            return jsonify({'success': False, 'error': 'Not logged in'})

        result = labor_search_service.clear_cache()
        return jsonify(result)

    except Exception as e:
        logger.error(f"Error clearing labor cache: {str(e)}")
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/labor/cache/stats', methods=['GET'])
def get_labor_cache_stats():
    """Get labor search cache statistics."""
    try:
        # Check if user is logged in
        if not hasattr(token_manager, 'username') or not token_manager.username:
            return jsonify({'success': False, 'error': 'Not logged in'})

        stats = labor_search_service.get_cache_stats()
        return jsonify({'success': True, 'stats': stats})

    except Exception as e:
        logger.error(f"Error getting labor cache stats: {str(e)}")
        return jsonify({'success': False, 'error': str(e)})

# Labor Request API Endpoints
@app.route('/api/task/<task_wonum>/add-labor', methods=['POST'])
def add_labor_to_task(task_wonum):
    """Add labor to a specific task."""
    try:
        # Check if user is logged in
        if not hasattr(token_manager, 'username') or not token_manager.username:
            return jsonify({'success': False, 'error': 'Not logged in'})

        # Get request data
        data = request.get_json()
        if not data:
            return jsonify({'success': False, 'error': 'No data provided'})

        # Extract required parameters with proper null checks
        laborcode = (data.get('laborcode') or '').strip()
        regularhrs = data.get('regularhrs')  # Changed from laborhrs to regularhrs
        siteid = (data.get('siteid') or '').strip()
        taskid = data.get('taskid')
        parent_wonum = (data.get('parent_wonum') or '').strip()

        # Extract optional parameters with proper null checks
        craft = (data.get('craft') or '').strip() or None
        startdate = (data.get('startdate') or '').strip() or None
        starttime = (data.get('starttime') or '').strip() or None
        finishdate = (data.get('finishdate') or '').strip() or None
        finishtime = (data.get('finishtime') or '').strip() or None
        payrate = data.get('payrate') or None
        notes = (data.get('notes') or '').strip() or None
        transtype = (data.get('transtype') or '').strip() or None

        # Validate required parameters
        if not all([laborcode, regularhrs, siteid, taskid, parent_wonum]):
            missing = [param for param, value in [
                ('laborcode', laborcode), ('regularhrs', regularhrs), ('siteid', siteid),
                ('taskid', taskid), ('parent_wonum', parent_wonum)
            ] if not value]
            return jsonify({
                'success': False,
                'error': f'Missing required parameters: {missing}'
            })

        logger.info(f"🔧 LABOR API: Adding labor {laborcode} ({regularhrs}h) to task {task_wonum}")

        # Add labor to task
        result = labor_request_service.add_labor_request(
            wonum=parent_wonum,
            siteid=siteid,
            laborcode=laborcode,
            regularhrs=regularhrs,  # Changed from laborhrs to regularhrs
            taskid=taskid,
            craft=craft,
            startdate=startdate,
            starttime=starttime,
            finishdate=finishdate,
            finishtime=finishtime,
            payrate=payrate,
            notes=notes,
            task_wonum=task_wonum,
            transtype=transtype
        )

        return jsonify(result)

    except Exception as e:
        logger.error(f"Error adding labor to task {task_wonum}: {str(e)}")
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/labor/performance-stats', methods=['GET'])
def get_labor_performance_stats():
    """Get labor request performance statistics."""
    try:
        # Check if user is logged in
        if not hasattr(token_manager, 'username') or not token_manager.username:
            return jsonify({'success': False, 'error': 'Not logged in'})

        stats = labor_request_service.get_performance_stats()
        return jsonify({'success': True, 'stats': stats})

    except Exception as e:
        logger.error(f"Error getting labor performance stats: {str(e)}")
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/task/<task_wonum>/add-negative-labor', methods=['POST'])
def add_negative_labor_to_task(task_wonum):
    """Add negative labor hours to a specific task (for corrections)."""
    try:
        # Check if user is logged in
        if not hasattr(token_manager, 'username') or not token_manager.username:
            return jsonify({'success': False, 'error': 'Not logged in'})

        # Get request data
        data = request.get_json()
        if not data:
            return jsonify({'success': False, 'error': 'No data provided'})

        # Extract required parameters
        laborcode = (data.get('laborcode') or '').strip()
        negative_hours = data.get('negative_hours')  # Should be negative, e.g., -0.1
        siteid = (data.get('siteid') or '').strip()
        taskid = data.get('taskid')
        craft = (data.get('craft') or '').strip()
        parent_wonum = (data.get('parent_wonum') or '').strip()

        # Validate required parameters
        if not all([laborcode, siteid, taskid, parent_wonum]):
            missing = [param for param, value in [
                ('laborcode', laborcode), ('siteid', siteid), ('taskid', taskid), ('parent_wonum', parent_wonum)
            ] if not value]
            return jsonify({'success': False, 'error': f'Missing required parameters: {missing}'})

        if not negative_hours or negative_hours >= 0:
            return jsonify({'success': False, 'error': 'Negative hours must be less than 0'})

        logger.info(f"🔧 NEGATIVE LABOR: Adding {negative_hours} hours for {laborcode} to task {task_wonum}")

        # Call the labor deletion service to add negative hours
        result = labor_deletion_service.add_negative_labor_hours(
            task_wonum=task_wonum,
            parent_wonum=parent_wonum,
            laborcode=laborcode,
            negative_hours=negative_hours,
            taskid=taskid,
            siteid=siteid,
            craft=craft if craft else None
        )

        if result.get('success'):
            logger.info(f"✅ NEGATIVE LABOR: Successfully added {negative_hours} hours for {laborcode}")
            # Return JSON response for successful labor addition
            return jsonify(result)
        else:
            logger.error(f"❌ NEGATIVE LABOR: Failed to add negative hours: {result.get('error')}")
            return jsonify(result), 400

    except Exception as e:
        logger.error(f"Error adding negative labor to task {task_wonum}: {str(e)}")
        return jsonify({'success': False, 'error': str(e)})

def extract_labor_records_from_data(raw_labor_data, source_type):
    """Extract labor records from various data structures"""
    logger.info(f"👷 LABOR EXTRACT: Processing {source_type} data type: {type(raw_labor_data)}")

    if isinstance(raw_labor_data, list):
        # Multiple labor records returned as list
        logger.info(f"👷 LABOR EXTRACT: Found {len(raw_labor_data)} labor records (list)")
        return raw_labor_data
    elif isinstance(raw_labor_data, dict):
        # Check if this is a collection structure or single record
        if 'member' in raw_labor_data:
            # This is a collection structure with member array
            member_data = raw_labor_data['member']
            if isinstance(member_data, list):
                logger.info(f"👷 LABOR EXTRACT: Found {len(member_data)} labor records (dict with member list)")
                return member_data
            else:
                logger.info(f"👷 LABOR EXTRACT: Found 1 labor record (dict with member single)")
                return [member_data]
        elif 'rdfs:member' in raw_labor_data:
            # This is a collection structure with rdfs:member array
            member_data = raw_labor_data['rdfs:member']
            if isinstance(member_data, list):
                logger.info(f"👷 LABOR EXTRACT: Found {len(member_data)} labor records (dict with rdfs:member list)")
                return member_data
            else:
                logger.info(f"👷 LABOR EXTRACT: Found 1 labor record (dict with rdfs:member single)")
                return [member_data]
        else:
            # Single labor record returned as dict
            logger.info(f"👷 LABOR EXTRACT: Found 1 labor record (single dict)")
            return [raw_labor_data]
    else:
        logger.error(f"❌ LABOR EXTRACT: Unexpected data type: {type(raw_labor_data)}")
        return []

def fetch_labor_from_collection_ref(collection_ref_url):
    """
    Fetch labor records from a collection reference URL (same pattern as materials).

    Args:
        collection_ref_url: The collection reference URL to fetch from

    Returns:
        List of labor dictionaries
    """
    try:
        logger.info(f"👷 LABOR API: Fetching from collection ref: {collection_ref_url}")

        response = token_manager.session.get(
            collection_ref_url,
            timeout=(5.0, 30),
            headers={"Accept": "application/json"},
            allow_redirects=True
        )

        logger.info(f"👷 LABOR API: Collection ref response status: {response.status_code}")

        if response.status_code == 200:
            data = response.json()
            logger.info(f"👷 LABOR API: Collection ref data type: {type(data)}")
            logger.info(f"👷 LABOR API: Collection ref data keys: {list(data.keys()) if isinstance(data, dict) else 'Not a dict'}")

            # Extract labor records from the collection response
            if isinstance(data, dict):
                labor_records = data.get('member', data.get('rdfs:member', []))
                logger.info(f"👷 LABOR API: Found {len(labor_records)} labor records in collection ref")

                # Process each labor record to handle localref if needed
                processed_records = []
                for i, labor_data in enumerate(labor_records):
                    logger.info(f"👷 LABOR API: Raw labor {i+1} data: {labor_data}")
                    if isinstance(labor_data, dict):
                        logger.info(f"👷 LABOR API: Labor {i+1} keys: {list(labor_data.keys())}")

                        # Check if this is just a localref - if so, fetch the actual labor data
                        if 'localref' in labor_data and len(labor_data.keys()) == 1:
                            localref = labor_data['localref']
                            logger.info(f"👷 LABOR API: Labor {i+1} is a localref, fetching actual data from: {localref}")

                            # Fix hostname in localref if needed
                            base_url = getattr(token_manager, 'base_url', '')
                            if base_url and 'vectrus-mea.manage.v2x.maximotest.gov2x.com' in localref:
                                import re
                                hostname_match = re.search(r'https://([^/]+)', base_url)
                                if hostname_match:
                                    correct_hostname = hostname_match.group(1)
                                    localref = re.sub(r'https://[^/]+', f'https://{correct_hostname}', localref)
                                    logger.info(f"👷 LABOR API: Fixed localref URL: {localref}")

                            # Fetch the actual labor data
                            try:
                                localref_response = token_manager.session.get(
                                    localref,
                                    timeout=(5.0, 30),
                                    headers={"Accept": "application/json"},
                                    allow_redirects=True
                                )

                                if localref_response.status_code == 200:
                                    actual_labor_data = localref_response.json()
                                    logger.info(f"👷 LABOR API: Fetched actual labor data: {actual_labor_data}")
                                    labor_data = actual_labor_data
                                else:
                                    logger.warning(f"👷 LABOR API: Failed to fetch localref data, status: {localref_response.status_code}")
                                    continue
                            except Exception as e:
                                logger.warning(f"👷 LABOR API: Error fetching localref data: {str(e)}")
                                continue

                        processed_records.append(labor_data)

                return processed_records
            else:
                logger.warning(f"👷 LABOR API: Collection ref response is not a dict")
                return []
        else:
            logger.error(f"👷 LABOR API: Collection ref failed with status {response.status_code}")
            return []

    except Exception as e:
        logger.error(f"👷 LABOR API: Error fetching from collection ref: {str(e)}")
        return []

# Debug endpoint to investigate labtrans methods
@app.route('/api/debug/labtrans-methods/<task_wonum>')
def debug_labtrans_methods(task_wonum):
    """Debug endpoint to list all available labtrans methods and collections"""
    try:
        logger.info(f"🔍 DEBUG: Investigating labtrans methods for task {task_wonum}")

        # Check if user is logged in
        if not hasattr(token_manager, 'username') or not token_manager.username:
            return jsonify({'error': 'Authentication required'})

        # Build API URL
        base_url = getattr(token_manager, 'base_url', '')
        api_url = f"{base_url}/oslc/os/mxapiwodetail"

        # Query for the specific task work order to see ALL available fields and collections
        params = {
            "oslc.select": "*",
            "oslc.where": f'wonum="{task_wonum}"',
            "oslc.pageSize": "1",
            "lean": "1"
        }

        logger.info(f"🔍 DEBUG: Querying {api_url} for task {task_wonum}")
        logger.info(f"🔍 DEBUG: Params: {params}")

        response = token_manager.session.get(
            api_url,
            params=params,
            timeout=(5.0, 30),
            headers={"Accept": "application/json"},
            allow_redirects=True
        )

        logger.info(f"🔍 DEBUG: Response status: {response.status_code}")

        if response.status_code == 200:
            try:
                data = response.json()
                logger.info(f"🔍 DEBUG: Response data keys: {list(data.keys()) if data else 'None'}")
            except Exception as e:
                logger.error(f"🔍 DEBUG: Failed to parse JSON response: {e}")
                logger.error(f"🔍 DEBUG: Raw response text: {response.text[:1000]}")
                return jsonify({
                    'success': False,
                    'error': f'Failed to parse JSON: {e}',
                    'raw_response': response.text[:1000],
                    'response_headers': dict(response.headers)
                })

            if 'member' in data and data['member']:
                wo_record = data['member'][0]
                all_keys = list(wo_record.keys())

                # Find all labtrans-related keys
                labtrans_keys = [key for key in all_keys if 'labtrans' in key.lower()]
                collection_keys = [key for key in all_keys if 'collectionref' in key.lower()]

                logger.info(f"🔍 DEBUG: ALL KEYS ({len(all_keys)}): {all_keys}")
                logger.info(f"🔍 DEBUG: LABTRANS KEYS ({len(labtrans_keys)}): {labtrans_keys}")
                logger.info(f"🔍 DEBUG: COLLECTION KEYS ({len(collection_keys)}): {collection_keys}")

                # Check if labtrans field exists and what it contains
                if 'labtrans' in wo_record:
                    labtrans_data = wo_record['labtrans']
                    logger.info(f"🔍 DEBUG: labtrans field type: {type(labtrans_data)}")
                    logger.info(f"🔍 DEBUG: labtrans field content: {labtrans_data}")

                # Check labtrans collection reference
                if 'labtrans_collectionref' in wo_record:
                    collection_url = wo_record['labtrans_collectionref']
                    logger.info(f"🔍 DEBUG: labtrans_collectionref: {collection_url}")

                return jsonify({
                    'success': True,
                    'task_wonum': task_wonum,
                    'all_keys_count': len(all_keys),
                    'all_keys': all_keys,
                    'labtrans_keys': labtrans_keys,
                    'collection_keys': collection_keys,
                    'has_labtrans_field': 'labtrans' in wo_record,
                    'has_labtrans_collection': 'labtrans_collectionref' in wo_record,
                    'labtrans_collection_url': wo_record.get('labtrans_collectionref', 'Not found'),
                    'labtrans_data_type': str(type(wo_record.get('labtrans', 'Not found'))),
                    'labtrans_data': wo_record.get('labtrans', 'Not found')
                })
            else:
                return jsonify({'success': False, 'error': 'No work order data found'})
        else:
            return jsonify({'success': False, 'error': f'API request failed: {response.status_code}'})

    except Exception as e:
        logger.error(f"❌ DEBUG: Error investigating labtrans methods: {str(e)}")
        return jsonify({'success': False, 'error': str(e)})

# Task Labor Loading API Endpoint (Reverted to working original approach)
@app.route('/api/task/<task_wonum>/labor', methods=['GET'])
def get_task_labor(task_wonum):
    """Get labor assignments for a specific task."""
    try:
        # Check if user is logged in
        if not hasattr(token_manager, 'username') or not token_manager.username:
            return jsonify({'success': False, 'error': 'Not logged in'})

        # Get task status for logging purposes
        task_status = request.args.get('status', '')
        logger.info(f"👷 LABOR API: Loading labor for task {task_wonum} with status {task_status}")

        # Get work order's site ID (not user's site ID) - using TaskLaborService method
        workorder_site_id = task_labor_service.get_workorder_site_id(task_wonum)
        logger.info(f"👷 LABOR API: Using work order site ID: {workorder_site_id}")

        # Use MXAPIWODETAIL to get labor data using collection reference approach (same as materials)
        base_url = getattr(token_manager, 'base_url', '')
        api_url = f"{base_url}/oslc/os/mxapiwodetail"

        # First, get the work order to find the labtrans_collectionref
        oslc_filter = f'wonum="{task_wonum}"'
        if workorder_site_id and workorder_site_id != "UNKNOWN":
            oslc_filter += f' and siteid="{workorder_site_id}"'

        params = {
            "oslc.select": "wonum,siteid,labtrans_collectionref",  # Get the collection reference
            "oslc.where": oslc_filter,
            "oslc.pageSize": "1",
            "lean": "1"
        }

        logger.info(f"👷 LABOR API: Querying {api_url} for task {task_wonum}")
        logger.info(f"👷 LABOR API: Params: {params}")

        response = token_manager.session.get(
            api_url,
            params=params,
            timeout=(5.0, 30),
            headers={"Accept": "application/json"},
            allow_redirects=True
        )

        logger.info(f"👷 LABOR API: Response status: {response.status_code}")

        if response.status_code == 200:
            try:
                data = response.json()
                logger.info(f"👷 LABOR API: Response data keys: {list(data.keys()) if data else 'None'}")

                # Extract the labtrans_collectionref from the work order (same pattern as materials)
                labor_records = []

                if isinstance(data, dict):
                    work_orders = data.get('member', data.get('rdfs:member', []))
                    logger.info(f"👷 LABOR API: Found {len(work_orders)} work orders in response")

                    if work_orders and len(work_orders) > 0:
                        work_order = work_orders[0]
                        logger.info(f"👷 LABOR API: Work order wonum: {work_order.get('wonum', 'UNKNOWN')}")

                        # Get the labtrans collection reference
                        collection_ref = work_order.get('labtrans_collectionref')
                        if collection_ref:
                            logger.info(f"👷 LABOR API: Found labtrans_collectionref: {collection_ref}")

                            # Fix the hostname in the collection reference URL if needed
                            # Sometimes Maximo returns collection refs with different hostnames
                            base_url = getattr(token_manager, 'base_url', '')
                            if base_url and 'vectrus-mea.manage.v2x.maximotest.gov2x.com' in collection_ref:
                                # Extract the correct hostname from our base_url
                                import re
                                hostname_match = re.search(r'https://([^/]+)', base_url)
                                if hostname_match:
                                    correct_hostname = hostname_match.group(1)
                                    # Replace any hostname in the collection ref with the correct one
                                    collection_ref = re.sub(r'https://[^/]+', f'https://{correct_hostname}', collection_ref)
                                    logger.info(f"👷 LABOR API: Fixed collection ref URL: {collection_ref}")

                            # Fetch labor records from the collection reference
                            labor_records = fetch_labor_from_collection_ref(collection_ref)
                            logger.info(f"👷 LABOR API: Found {len(labor_records)} labor records from collection ref")

                        else:
                            logger.warning(f"👷 LABOR API: No labtrans_collectionref found in work order")
                    else:
                        logger.info(f"👷 LABOR API: No work order found for task {task_wonum}")

                logger.info(f"👷 LABOR API: Final extracted labor records count: {len(labor_records)}")

                # Process labor transaction records
                processed_labor = []
                for i, labor in enumerate(labor_records):
                    try:
                        logger.info(f"👷 LABOR API: Processing labor record {i+1}: {labor}")

                        # Use regularhrs as the main labor hours field
                        regular_hrs_raw = labor.get('regularhrs', 0)
                        premium_hrs_raw = labor.get('premiumpayhours', 0)

                        try:
                            regular_hrs = float(regular_hrs_raw or 0)
                        except (ValueError, TypeError) as e:
                            regular_hrs = 0.0

                        try:
                            premium_hrs = float(premium_hrs_raw or 0)
                        except (ValueError, TypeError) as e:
                            premium_hrs = 0.0

                        total_hrs = regular_hrs + premium_hrs

                        processed_record = {
                            'laborcode': labor.get('laborcode', ''),
                            'laborhrs': total_hrs,  # Total hours (regular + premium)
                            'craft': labor.get('craft', ''),
                            'startdate': labor.get('startdate', ''),
                            'finishdate': labor.get('finishdate', ''),
                            'transdate': labor.get('transdate', ''),
                            'regularhrs': regular_hrs,
                            'premiumpayhours': premium_hrs,
                            'transtype': labor.get('transtype', ''),
                            'labtransid': labor.get('labtransid', ''),
                            'taskid': labor.get('taskid', ''),
                            'description': f"Labor: {labor.get('laborcode', 'Unknown')}",
                            'status': 'ACTIVE',  # Default status
                            'rate_display': f"Regular: {regular_hrs}h, Premium: {premium_hrs}h, Total: {total_hrs}h"
                        }
                        processed_labor.append(processed_record)

                        logger.info(f"✅ LABOR API: Successfully processed labor record {i+1}: {labor.get('laborcode', 'Unknown')} - {total_hrs}h")

                    except Exception as e:
                        logger.error(f"❌ LABOR API: Error processing labor record {i+1}: {e}")
                        continue

                logger.info(f"✅ LABOR API: Successfully processed {len(processed_labor)} labor transaction records for task {task_wonum}")

                return jsonify({
                    'success': True,
                    'show_labor': True,
                    'labor': processed_labor,
                    'task_wonum': task_wonum,
                    'task_status': task_status,
                    'message': f'Found {len(processed_labor)} labor transaction records'
                })

            except json.JSONDecodeError as e:
                logger.error(f"❌ LABOR API: Failed to parse JSON response: {e}")
                return jsonify({
                    'success': False,
                    'error': 'Invalid JSON response from API',
                    'show_labor': False
                })
        else:
            logger.error(f"❌ LABOR API: API request failed with status {response.status_code}")
            logger.error(f"❌ LABOR API: Response: {response.text[:500]}")
            return jsonify({
                'success': False,
                'error': f'API request failed: HTTP {response.status_code}',
                'show_labor': False
            })

    except Exception as e:
        logger.error(f"❌ LABOR API: Error getting task labor for {task_wonum}: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e),
            'show_labor': False
        })

@app.route('/api/workorder/<wonum>/materials-availability', methods=['GET'])
def check_workorder_materials_availability(wonum):
    """Check if a work order has any planned materials across all its tasks."""
    try:
        # Check if user is logged in
        if not hasattr(token_manager, 'username') or not token_manager.username:
            return jsonify({'success': False, 'error': 'Not logged in'})

        # Get work order's site ID (not user's site ID)
        workorder_site_id = task_materials_service.get_workorder_site_id(wonum)

        logger.info(f"📦 WO MATERIALS API: Checking availability for WO {wonum}, work order site {workorder_site_id}")

        # Check materials availability using work order's site ID
        availability = task_materials_service.check_workorder_materials_availability(wonum, workorder_site_id)

        return jsonify({
            'success': True,
            'wonum': wonum,
            'site_id': workorder_site_id,
            'availability': availability
        })

    except Exception as e:
        logger.error(f"Error checking materials availability for WO {wonum}: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e),
            'wonum': wonum
        })

@app.route('/api/workorder/<wonum>/material-cost-availability', methods=['GET'])
def check_workorder_material_cost_availability(wonum):
    """Check material cost for a work order by summing linecost across all its tasks."""
    try:
        # Check if user is logged in
        if not hasattr(token_manager, 'username') or not token_manager.username:
            return jsonify({'success': False, 'error': 'Not logged in'})

        # Get work order's site ID (not user's site ID)
        workorder_site_id = task_materials_service.get_workorder_site_id(wonum)

        logger.info(f"💰 WO MATERIAL COST API: Checking material cost availability for WO {wonum}, work order site {workorder_site_id}")

        # Check material cost availability using work order's site ID
        availability = task_materials_service.check_workorder_material_cost_availability(wonum, workorder_site_id)

        return jsonify({
            'success': True,
            'wonum': wonum,
            'site_id': workorder_site_id,
            'availability': availability
        })

    except Exception as e:
        logger.error(f"Error checking material cost availability for WO {wonum}: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e),
            'wonum': wonum
        })

@app.route('/api/workorder/<wonum>/labor-hours-availability', methods=['GET'])
def check_workorder_labor_hours_availability(wonum):
    """Check if a work order has any labor hours across all its tasks."""
    try:
        # Check if user is logged in
        if not hasattr(token_manager, 'username') or not token_manager.username:
            return jsonify({'success': False, 'error': 'Not logged in'})

        # Get work order's site ID (not user's site ID)
        workorder_site_id = task_labor_service.get_workorder_site_id(wonum)

        logger.info(f"👷 WO LABOR API: Checking labor hours availability for WO {wonum}, work order site {workorder_site_id}")

        # Check labor hours availability using work order's site ID
        availability = task_labor_service.check_workorder_labor_hours_availability(wonum, workorder_site_id)

        return jsonify({
            'success': True,
            'wonum': wonum,
            'site_id': workorder_site_id,
            'availability': availability
        })

    except Exception as e:
        logger.error(f"Error checking labor hours availability for WO {wonum}: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e),
            'wonum': wonum
        })

@app.route('/api/workorder/<wonum>/labor-cost-availability', methods=['GET'])
def check_workorder_labor_cost_availability(wonum):
    """Check labor cost for a work order by summing PAYRATE * regularhrs across all its tasks."""
    try:
        # Check if user is logged in
        if not hasattr(token_manager, 'username') or not token_manager.username:
            return jsonify({'success': False, 'error': 'Not logged in'})

        # Get work order's site ID (not user's site ID)
        workorder_site_id = task_labor_service.get_workorder_site_id(wonum)

        logger.info(f"💰 WO LABOR COST API: Checking labor cost availability for WO {wonum}, work order site {workorder_site_id}")

        # Check labor cost availability using work order's site ID
        availability = task_labor_service.check_workorder_labor_cost_availability(wonum, workorder_site_id)

        return jsonify({
            'success': True,
            'wonum': wonum,
            'site_id': workorder_site_id,
            'availability': availability
        })

    except Exception as e:
        logger.error(f"Error checking labor cost availability for WO {wonum}: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e),
            'wonum': wonum
        })

@app.route('/api/workorder/<wonum>/task-details', methods=['GET'])
def get_workorder_task_details(wonum):
    """Get comprehensive task details including hours, labor cost, material count, and material cost for each task."""
    try:
        # Check if user is logged in
        if not hasattr(token_manager, 'username') or not token_manager.username:
            return jsonify({'success': False, 'error': 'Not logged in'})

        # Get work order's site ID (not user's site ID)
        workorder_site_id = task_labor_service.get_workorder_site_id(wonum)

        logger.info(f"📊 TASK DETAILS API: Getting task details for WO {wonum}, work order site {workorder_site_id}")

        # Get task details using work order's site ID
        details = task_labor_service.get_workorder_task_details(wonum, workorder_site_id)

        return jsonify({
            'success': True,
            'wonum': wonum,
            'site_id': workorder_site_id,
            'details': details
        })

    except Exception as e:
        logger.error(f"Error getting task details for WO {wonum}: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e),
            'wonum': wonum
        })

# Material Request API Endpoints
@app.route('/api/workorder/add-material-request', methods=['POST'])
def add_material_request():
    """Add a material request to a work order."""
    try:
        # Check if user is logged in
        if 'username' not in session:
            return jsonify({'success': False, 'error': 'Not authenticated'}), 401

        # Verify session is still valid
        if not enhanced_workorder_service.is_session_valid():
            return jsonify({'success': False, 'error': 'Session expired'}), 401

        # Get request data
        data = request.get_json()
        if not data:
            return jsonify({'success': False, 'error': 'No data provided'}), 400

        # Validate required fields
        required_fields = ['wonum', 'siteid', 'itemnum', 'quantity', 'requestby', 'taskid']
        for field in required_fields:
            if field not in data or data[field] is None or data[field] == '':
                return jsonify({'success': False, 'error': f'Missing required field: {field}'}), 400

        wonum = data['wonum']
        siteid = data['siteid']
        itemnum = data['itemnum']
        quantity = float(data['quantity'])

        # Handle location safely - can be None, empty string, or actual location
        location_raw = data.get('location')
        if location_raw and isinstance(location_raw, str):
            location = location_raw.strip() or None
        else:
            location = location_raw  # Keep as None if not provided

        directreq = data.get('directreq', True)

        # Handle notes safely
        notes_raw = data.get('notes')
        if notes_raw and isinstance(notes_raw, str):
            notes = notes_raw.strip() or None
        else:
            notes = None

        # Handle requestby safely
        requestby_raw = data.get('requestby')
        if requestby_raw and isinstance(requestby_raw, str):
            requestby = requestby_raw.strip()
        else:
            return jsonify({'success': False, 'error': 'requestby field is required and cannot be empty'}), 400

        # Handle taskid (MANDATORY field) - this should be the numeric task ID
        try:
            taskid = int(data['taskid'])
            logger.info(f"🔍 TASKID DEBUG: Received taskid={taskid} for work order {wonum}")
        except (ValueError, TypeError):
            return jsonify({'success': False, 'error': 'taskid must be a valid integer'}), 400

        # Handle task_wonum (optional field for validation)
        task_wonum = data.get('task_wonum')
        if task_wonum:
            logger.info(f"🔍 TASK WONUM DEBUG: Received task_wonum={task_wonum} for validation")

        # Validate quantity
        if quantity <= 0:
            return jsonify({'success': False, 'error': 'Quantity must be greater than 0'}), 400

        logger.info("🎯 MATERIAL REQUEST API - COMPLETE REQUEST DATA:")
        logger.info("="*60)
        logger.info(f"📦 FRONTEND REQUEST DATA:")
        logger.info(json.dumps(data, indent=2))
        logger.info("="*60)
        logger.info(f"🎯 PARSED VALUES:")
        logger.info(f"   WO Number: {wonum}")
        logger.info(f"   Site ID: {siteid}")
        logger.info(f"   Item Number: {itemnum}")
        logger.info(f"   Quantity: {quantity}")
        logger.info(f"   Task ID: {taskid}")
        logger.info(f"   Task WO Number: {task_wonum}")
        logger.info(f"   Location: {location}")
        logger.info(f"   Direct Request: {directreq}")
        logger.info(f"   Requested By: {requestby}")
        logger.info(f"   Notes: {notes}")
        logger.info("="*60)

        # Add the material request
        result = material_request_service.add_material_request(
            wonum=wonum,
            siteid=siteid,
            itemnum=itemnum,
            quantity=quantity,
            taskid=taskid,
            location=location,
            directreq=directreq,
            notes=notes,
            requestby=requestby,
            task_wonum=task_wonum
        )

        if result['success']:
            logger.info(f"✅ MATERIAL REQUEST API: Successfully added {itemnum} to WO {wonum}")
            return jsonify(result)
        else:
            logger.error(f"❌ MATERIAL REQUEST API: Failed to add {itemnum} to WO {wonum}: {result.get('error')}")
            return jsonify(result), 400

    except ValueError as e:
        logger.error(f"Material request validation error: {str(e)}")
        return jsonify({'success': False, 'error': f'Invalid data: {str(e)}'}), 400
    except Exception as e:
        logger.error(f"Error adding material request: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500

# Work Order Attachment API Endpoints
@app.route('/api/workorder/<wonum>/attachments', methods=['GET'])
def get_workorder_attachments(wonum):
    """Get all attachments for a work order"""
    try:
        # Check if user is logged in
        if not hasattr(token_manager, 'username') or not token_manager.username:
            return jsonify({'success': False, 'error': 'Not authenticated'}), 401

        logger.info(f"📎 ATTACHMENTS API: Getting attachments for work order {wonum}")

        result = mxapi_service.get_workorder_attachments(wonum)

        if result['success']:
            logger.info(f"✅ ATTACHMENTS API: Found {result['count']} attachments for {wonum}")
            return jsonify(result)
        else:
            logger.error(f"❌ ATTACHMENTS API: Failed to get attachments for {wonum}: {result.get('error')}")
            return jsonify(result), 400

    except Exception as e:
        logger.error(f"Error getting attachments for {wonum}: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/workorder/<wonum>/attachments', methods=['POST'])
def add_workorder_attachment(wonum):
    """Add an attachment to a work order"""
    try:
        # Check if user is logged in
        if not hasattr(token_manager, 'username') or not token_manager.username:
            return jsonify({'success': False, 'error': 'Not authenticated'}), 401

        logger.info(f"📎 ATTACHMENTS API: Adding attachment to work order {wonum}")

        # Check if file was uploaded
        if 'file' not in request.files:
            return jsonify({'success': False, 'error': 'No file uploaded'}), 400

        file = request.files['file']
        if file.filename == '':
            return jsonify({'success': False, 'error': 'No file selected'}), 400

        # Get additional form data
        description = request.form.get('description', file.filename)
        doctype = request.form.get('doctype', 'Attachments')  # Use 'Attachments' as per Maximo standard

        # Validate and normalize doctype to Maximo's primary categories
        primary_doctypes = ['Attachments', 'Diagrams', 'Images']
        if doctype not in primary_doctypes and doctype not in ['DRAWING', 'MANUAL', 'PHOTO', 'SPECIFICATION', 'REPORT', 'OTHER']:
            doctype = 'Attachments'  # Default to Attachments for unknown types

        # Validate file type (common Maximo supported formats)
        allowed_extensions = {
            'pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx',
            'txt', 'rtf', 'jpg', 'jpeg', 'png', 'gif', 'bmp',
            'tiff', 'zip', 'rar', '7z', 'mp4', 'avi', 'mov',
            'mp3', 'wav', 'csv', 'xml', 'json'
        }

        file_extension = file.filename.rsplit('.', 1)[1].lower() if '.' in file.filename else ''
        if file_extension not in allowed_extensions:
            return jsonify({
                'success': False,
                'error': f'File type .{file_extension} not supported. Allowed types: {", ".join(sorted(allowed_extensions))}'
            }), 400

        # Check file size (limit to 50MB)
        file.seek(0, 2)  # Seek to end
        file_size = file.tell()
        file.seek(0)  # Reset to beginning

        max_size = 50 * 1024 * 1024  # 50MB
        if file_size > max_size:
            return jsonify({
                'success': False,
                'error': f'File size ({file_size / 1024 / 1024:.1f}MB) exceeds maximum allowed size (50MB)'
            }), 400

        # Prepare file data
        file_data = {
            'filename': file.filename,
            'content': file.read(),
            'size': file_size,
            'content_type': file.content_type or 'application/octet-stream'
        }

        # Log upload payload details
        logger.info(f"📎 ATTACHMENTS API: Upload payload - filename: {file.filename}")
        logger.info(f"📎 ATTACHMENTS API: Upload payload - size: {file_size} bytes")
        logger.info(f"📎 ATTACHMENTS API: Upload payload - content_type: {file.content_type}")
        logger.info(f"📎 ATTACHMENTS API: Upload payload - description: {description}")
        logger.info(f"📎 ATTACHMENTS API: Upload payload - doctype: {doctype}")
        logger.info(f"📎 ATTACHMENTS API: Upload payload - extension: {file_extension}")

        result = mxapi_service.add_workorder_attachment(wonum, file_data, description, doctype)

        if result['success']:
            logger.info(f"✅ ATTACHMENTS API: Successfully added attachment {file.filename} to {wonum}")
            return jsonify(result)
        else:
            logger.error(f"❌ ATTACHMENTS API: Failed to add attachment to {wonum}: {result.get('error')}")
            return jsonify(result), 400

    except Exception as e:
        logger.error(f"Error adding attachment to {wonum}: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/workorder/<wonum>/attachments/<docinfoid>', methods=['DELETE'])
def delete_workorder_attachment(wonum, docinfoid):
    """Delete an attachment from a work order"""
    try:
        # Check if user is logged in
        if not hasattr(token_manager, 'username') or not token_manager.username:
            return jsonify({'success': False, 'error': 'Not authenticated'}), 401

        logger.info(f"📎 ATTACHMENTS API: Deleting attachment {docinfoid} from work order {wonum}")

        result = mxapi_service.delete_workorder_attachment(wonum, docinfoid)

        if result['success']:
            logger.info(f"✅ ATTACHMENTS API: Successfully deleted attachment {docinfoid} from {wonum}")
            return jsonify(result)
        else:
            logger.error(f"❌ ATTACHMENTS API: Failed to delete attachment {docinfoid} from {wonum}: {result.get('error')}")
            return jsonify(result), 400

    except Exception as e:
        logger.error(f"Error deleting attachment {docinfoid} from {wonum}: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/workorder/<wonum>/attachments/<docinfoid>/download', methods=['GET'])
def download_workorder_attachment(wonum, docinfoid):
    """Download an attachment from a work order"""
    try:
        # Check if user is logged in
        if not hasattr(token_manager, 'username') or not token_manager.username:
            return jsonify({'success': False, 'error': 'Not authenticated'}), 401

        logger.info(f"📎 ATTACHMENTS API: Downloading attachment {docinfoid} from work order {wonum}")

        # Use the service method to download the attachment
        download_result = mxapi_service.download_workorder_attachment(wonum, docinfoid)

        if download_result['success']:
            # Return the file as a download
            from flask import Response

            filename = download_result['filename']
            content = download_result['content']
            content_type = download_result['content_type']

            logger.info(f"✅ ATTACHMENTS API: Successfully downloaded {filename} ({len(content)} bytes)")

            # Create response with file content
            response = Response(
                content,
                mimetype=content_type,
                headers={
                    'Content-Disposition': f'attachment; filename="{filename}"',
                    'Content-Length': str(len(content))
                }
            )

            return response
        else:
            logger.error(f"❌ ATTACHMENTS API: Download failed: {download_result.get('error')}")
            return jsonify(download_result), 400

    except Exception as e:
        logger.error(f"Error downloading attachment {docinfoid} from {wonum}: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/workorder/<wonum>/attachments/<docinfoid>/view', methods=['GET'])
def view_workorder_attachment(wonum, docinfoid):
    """View an attachment from a work order (for supported file types)"""
    try:
        # Check if user is logged in
        if not hasattr(token_manager, 'username') or not token_manager.username:
            return jsonify({'success': False, 'error': 'Not authenticated'}), 401

        logger.info(f"📎 ATTACHMENTS API: Viewing attachment {docinfoid} from work order {wonum}")

        # Use the service method to download the attachment
        download_result = mxapi_service.download_workorder_attachment(wonum, docinfoid)

        if download_result['success']:
            filename = download_result['filename']
            content = download_result['content']
            content_type = download_result['content_type']

            # Determine if file can be viewed inline
            viewable_types = {
                'application/pdf': 'pdf',
                'text/plain': 'text',
                'text/html': 'html',
                'text/css': 'css',
                'text/javascript': 'javascript',
                'application/json': 'json',
                'application/xml': 'xml',
                'text/xml': 'xml',
                'image/jpeg': 'image',
                'image/jpg': 'image',
                'image/png': 'image',
                'image/gif': 'image',
                'image/bmp': 'image',
                'image/svg+xml': 'image'
            }

            # Check file extension as fallback
            if content_type not in viewable_types:
                ext = filename.lower().split('.')[-1] if '.' in filename else ''
                ext_mapping = {
                    'pdf': 'application/pdf',
                    'txt': 'text/plain',
                    'json': 'application/json',
                    'xml': 'application/xml',
                    'html': 'text/html',
                    'css': 'text/css',
                    'js': 'text/javascript',
                    'jpg': 'image/jpeg',
                    'jpeg': 'image/jpeg',
                    'png': 'image/png',
                    'gif': 'image/gif',
                    'bmp': 'image/bmp',
                    'svg': 'image/svg+xml'
                }
                content_type = ext_mapping.get(ext, content_type)

            if content_type in viewable_types:
                logger.info(f"✅ ATTACHMENTS API: Viewing {filename} inline ({content_type})")

                # Return file for inline viewing
                from flask import Response
                response = Response(
                    content,
                    mimetype=content_type,
                    headers={
                        'Content-Disposition': f'inline; filename="{filename}"',
                        'Content-Length': str(len(content))
                    }
                )
                return response
            else:
                logger.warning(f"📎 ATTACHMENTS API: File type {content_type} not viewable inline")
                return jsonify({
                    'success': False,
                    'error': f'File type {content_type} cannot be viewed inline. Use download instead.',
                    'filename': filename,
                    'content_type': content_type,
                    'downloadable': True
                }), 400
        else:
            logger.error(f"❌ ATTACHMENTS API: View failed: {download_result.get('error')}")
            return jsonify(download_result), 400

    except Exception as e:
        logger.error(f"Error viewing attachment {docinfoid} from {wonum}: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500

# QR Code Generation API Endpoints
@app.route('/api/inventory/generate-qr', methods=['POST'])
def generate_inventory_qr_code():
    """Generate QR code for inventory record"""
    try:
        # Check if user is logged in
        if not hasattr(token_manager, 'username') or not token_manager.username:
            return jsonify({'success': False, 'error': 'Not authenticated'}), 401

        # Get request data
        data = request.get_json()
        if not data:
            return jsonify({'success': False, 'error': 'No data provided'}), 400

        # Extract inventory data and optional balance record
        inventory_data = data.get('inventory_data', data)  # Support both new and old format
        balance_record = data.get('balance_record')  # Optional balance record for balance-specific QR

        logger.info(f"🔍 QR CODE API: Received inventory data keys: {list(inventory_data.keys())}")
        if balance_record:
            logger.info(f"🔍 QR CODE API: Generating balance-specific QR code for item {inventory_data.get('itemnum', 'UNKNOWN')}, balance ID {balance_record.get('invbalancesid')}")
        else:
            logger.info(f"🔍 QR CODE API: Generating inventory-level QR code for item {inventory_data.get('itemnum', 'UNKNOWN')}")

        # Import QR code service
        from backend.services.qr_code_service import QRCodeService
        qr_service = QRCodeService()

        # Generate QR code (inventory-level or balance-specific)
        result = qr_service.generate_inventory_qr_code(inventory_data, balance_record)

        if result['success']:
            qr_type = result['qr_data'].get('qr_type', 'inventory_level')
            if qr_type == 'balance_specific':
                bin_info = result['qr_data'].get('binnum', 'NO_BIN')
                lot_info = result['qr_data'].get('lotnum', 'NO_LOT')
                balance_info = result['qr_data'].get('curbal', 0)
                logger.info(f"✅ QR CODE API: Generated balance-specific QR code for bin {bin_info}, lot {lot_info}, balance {balance_info}")
            else:
                logger.info(f"✅ QR CODE API: Generated inventory-level QR code for item {inventory_data.get('itemnum', 'UNKNOWN')}")
            return jsonify(result)
        else:
            logger.error(f"❌ QR CODE API: Failed to generate QR code: {result.get('error')}")
            return jsonify(result), 400

    except Exception as e:
        logger.error(f"Error generating QR code: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/inventory/generate-qr-label', methods=['POST'])
def generate_inventory_qr_label():
    """Generate printable QR label for inventory record"""
    try:
        # Check if user is logged in
        if not hasattr(token_manager, 'username') or not token_manager.username:
            return jsonify({'success': False, 'error': 'Not authenticated'}), 401

        # Get inventory data from request
        inventory_data = request.get_json()
        if not inventory_data:
            return jsonify({'success': False, 'error': 'No inventory data provided'}), 400

        logger.info(f"🖨️ QR LABEL API: Received inventory data keys: {list(inventory_data.keys())}")
        logger.info(f"🖨️ QR LABEL API: Full inventory data: {inventory_data}")
        logger.info(f"🖨️ QR LABEL API: Generating printable label for item {inventory_data.get('itemnum', 'UNKNOWN')}")

        # Import QR code service
        from backend.services.qr_code_service import QRCodeService
        qr_service = QRCodeService()

        # Generate printable label
        result = qr_service.generate_printable_qr_label(inventory_data)

        if result['success']:
            logger.info(f"✅ QR LABEL API: Generated printable label for item {inventory_data.get('itemnum', 'UNKNOWN')}")
            return jsonify(result)
        else:
            logger.error(f"❌ QR LABEL API: Failed to generate label: {result.get('error')}")
            return jsonify(result), 400

    except Exception as e:
        logger.error(f"Error generating QR label: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/inventory/decode-qr', methods=['POST'])
def decode_inventory_qr_code():
    """Decode QR code content to inventory data"""
    try:
        # Check if user is logged in
        if not hasattr(token_manager, 'username') or not token_manager.username:
            return jsonify({'success': False, 'error': 'Not authenticated'}), 401

        # Get QR code content from request
        request_data = request.get_json()
        if not request_data or 'qr_content' not in request_data:
            return jsonify({'success': False, 'error': 'No QR code content provided'}), 400

        qr_content = request_data['qr_content']
        logger.info(f"🔍 QR DECODE API: Decoding QR code content")

        # Import QR code service
        from backend.services.qr_code_service import QRCodeService
        qr_service = QRCodeService()

        # Decode QR code
        result = qr_service.decode_qr_data(qr_content)

        if result['success']:
            logger.info(f"✅ QR DECODE API: Decoded QR code for item {result['inventory_data'].get('itemnum', 'UNKNOWN')}")
            return jsonify(result)
        else:
            logger.error(f"❌ QR DECODE API: Failed to decode QR code: {result.get('error')}")
            return jsonify(result), 400

    except Exception as e:
        logger.error(f"Error decoding QR code: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500

# Asset QR Code Generation API Endpoints
@app.route('/api/asset/generate-qr', methods=['POST'])
def generate_asset_qr_code():
    """Generate QR code for asset record"""
    try:
        # Check if user is logged in
        if not hasattr(token_manager, 'username') or not token_manager.username:
            return jsonify({'success': False, 'error': 'Not authenticated'}), 401

        # Get request data
        data = request.get_json()
        if not data:
            return jsonify({'success': False, 'error': 'No data provided'}), 400

        # Extract asset data
        asset_data = data.get('asset_data', data)  # Support both new and old format

        logger.info(f"🔍 ASSET QR CODE API: Received asset data keys: {list(asset_data.keys())}")
        logger.info(f"🔍 ASSET QR CODE API: Generating QR code for asset {asset_data.get('assetnum', 'UNKNOWN')}")

        # Import QR code service
        from backend.services.qr_code_service import QRCodeService
        qr_service = QRCodeService()

        # Generate QR code
        result = qr_service.generate_asset_qr_code(asset_data)

        if result['success']:
            logger.info(f"✅ ASSET QR CODE API: Generated QR code for asset {asset_data.get('assetnum', 'UNKNOWN')}")
            return jsonify(result)
        else:
            logger.error(f"❌ ASSET QR CODE API: Failed to generate QR code: {result.get('error')}")
            return jsonify(result), 400

    except Exception as e:
        logger.error(f"Error generating asset QR code: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/asset/decode-qr', methods=['POST'])
def decode_asset_qr_code():
    """Decode QR code content to asset data"""
    try:
        # Check if user is logged in
        if not hasattr(token_manager, 'username') or not token_manager.username:
            return jsonify({'success': False, 'error': 'Not authenticated'}), 401

        # Get QR code content from request
        request_data = request.get_json()
        if not request_data or 'qr_content' not in request_data:
            return jsonify({'success': False, 'error': 'No QR code content provided'}), 400

        qr_content = request_data['qr_content']
        logger.info(f"🔍 ASSET QR DECODE API: Decoding QR code content")

        # Import QR code service
        from backend.services.qr_code_service import QRCodeService
        qr_service = QRCodeService()

        # Decode QR code
        result = qr_service.decode_asset_qr_data(qr_content)

        if result['success']:
            logger.info(f"✅ ASSET QR DECODE API: Decoded QR code for asset {result['asset_data'].get('assetnum', 'UNKNOWN')}")
            return jsonify(result)
        else:
            logger.error(f"❌ ASSET QR DECODE API: Failed to decode QR code: {result.get('error')}")
            return jsonify(result), 400

    except Exception as e:
        logger.error(f"Error decoding asset QR code: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500

# Inventory Operations API Endpoints
@app.route('/api/inventory/cycle-count', methods=['POST'])
def submit_cycle_count():
    """Submit cycle count for inventory item"""
    try:
        # Check if user is logged in
        if not hasattr(token_manager, 'username') or not token_manager.username:
            return jsonify({'success': False, 'error': 'Not authenticated'}), 401

        # Get cycle count data from request
        count_data = request.get_json()
        if not count_data:
            return jsonify({'success': False, 'error': 'No count data provided'}), 400

        # Validate required fields
        required_fields = ['inventory_data', 'physical_count', 'system_count']
        for field in required_fields:
            if field not in count_data:
                return jsonify({'success': False, 'error': f'Missing required field: {field}'}), 400

        inventory_data = count_data['inventory_data']
        physical_count = float(count_data['physical_count'])
        system_count = float(count_data['system_count'])
        variance = physical_count - system_count
        notes = count_data.get('notes', '')

        logger.info(f"📊 CYCLE COUNT API: Processing count for item {inventory_data.get('itemnum', 'UNKNOWN')}")
        logger.info(f"📊 CYCLE COUNT API: Physical: {physical_count}, System: {system_count}, Variance: {variance}")

        # If there's a variance, submit as an inventory adjustment
        if variance != 0:
            # Import and use the inventory adjustment service
            from backend.services.inventory_adjustment_service import InventoryAdjustmentService
            adjustment_service = InventoryAdjustmentService(token_manager)

            # Determine adjustment type based on variance
            adjustment_type = 'POSITIVE' if variance > 0 else 'NEGATIVE'
            adjustment_quantity = abs(variance)

            # Submit the adjustment to mxapiinventory
            result = adjustment_service.submit_inventory_adjustment(
                inventory_data,
                {
                    'adjustment_type': adjustment_type,
                    'quantity': adjustment_quantity,
                    'reason_code': 'CYCLE_COUNT',
                    'notes': f"Cycle count adjustment. Physical: {physical_count}, System: {system_count}. {notes}".strip()
                }
            )

            if result['success']:
                logger.info(f"✅ CYCLE COUNT API: Successfully submitted cycle count adjustment for item {inventory_data.get('itemnum', 'UNKNOWN')}")
                # Add cycle count specific metadata
                result.update({
                    'operation': 'cycle_count',
                    'itemnum': inventory_data.get('itemnum'),
                    'physical_count': physical_count,
                    'system_count': system_count,
                    'variance': variance,
                    'notes': notes,
                    'submitted_by': token_manager.username,
                    'message': f'Cycle count submitted successfully. Variance: {variance:+.2f} adjusted in Maximo'
                })
            else:
                logger.error(f"❌ CYCLE COUNT API: Failed to submit cycle count adjustment for item {inventory_data.get('itemnum', 'UNKNOWN')}: {result.get('error')}")
        else:
            # No variance, just log the count
            result = {
                'success': True,
                'operation': 'cycle_count',
                'itemnum': inventory_data.get('itemnum'),
                'physical_count': physical_count,
                'system_count': system_count,
                'variance': variance,
                'notes': notes,
                'timestamp': datetime.datetime.now().isoformat(),
                'submitted_by': token_manager.username,
                'message': 'Cycle count completed. No variance detected - no adjustment needed.'
            }
            logger.info(f"✅ CYCLE COUNT API: Count completed for item {inventory_data.get('itemnum', 'UNKNOWN')} - no variance")

        return jsonify(result)

    except ValueError as e:
        logger.error(f"Cycle count validation error: {str(e)}")
        return jsonify({'success': False, 'error': f'Invalid data: {str(e)}'}), 400
    except Exception as e:
        logger.error(f"Error submitting cycle count: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/inventory/adjustment', methods=['POST'])
def submit_inventory_adjustment():
    """Submit inventory adjustment"""
    try:
        # Check if user is logged in
        if not hasattr(token_manager, 'username') or not token_manager.username:
            return jsonify({'success': False, 'error': 'Not authenticated'}), 401

        # Get adjustment data from request
        adjustment_data = request.get_json()
        if not adjustment_data:
            return jsonify({'success': False, 'error': 'No adjustment data provided'}), 400

        # Validate required fields - handle both adjustment methods
        required_fields = ['inventory_data', 'reason_code']
        for field in required_fields:
            if field not in adjustment_data:
                return jsonify({'success': False, 'error': f'Missing required field: {field}'}), 400

        inventory_data = adjustment_data['inventory_data']
        reason_code = adjustment_data['reason_code']
        notes = adjustment_data.get('notes', '')
        adjustment_method = adjustment_data.get('adjustment_method', 'CURRENT_BALANCE')

        # Handle different adjustment methods
        if adjustment_method == 'PHYSICAL_COUNT':
            # Physical Count Adjustment
            if 'physical_count' not in adjustment_data:
                return jsonify({'success': False, 'error': 'Missing required field: physical_count'}), 400

            physical_count = float(adjustment_data['physical_count'])
            adjustment_type = 'PHYSICAL_COUNT'

            logger.info(f"📝 ADJUSTMENT API: Processing physical count adjustment for item {inventory_data.get('itemnum', 'UNKNOWN')}")
            logger.info(f"📝 ADJUSTMENT API: Physical count: {physical_count}, Reason: {reason_code}")

            # Prepare data for service
            service_data = {
                'adjustment_method': 'PHYSICAL_COUNT',
                'adjustment_type': 'PHYSICAL_COUNT',
                'physical_count': physical_count,
                'reason_code': reason_code,
                'notes': notes,
                'reconciled': adjustment_data.get('reconciled', True)
            }

        else:
            # Current Balance Adjustment
            if 'adjustment_type' not in adjustment_data or 'quantity' not in adjustment_data:
                return jsonify({'success': False, 'error': 'Missing required fields: adjustment_type and quantity'}), 400

            adjustment_type = adjustment_data['adjustment_type']
            quantity = float(adjustment_data['quantity'])

            logger.info(f"📝 ADJUSTMENT API: Processing {adjustment_type} adjustment for item {inventory_data.get('itemnum', 'UNKNOWN')}")
            logger.info(f"📝 ADJUSTMENT API: Quantity: {quantity}, Reason: {reason_code}")

            # Prepare data for service
            service_data = {
                'adjustment_method': 'CURRENT_BALANCE',
                'adjustment_type': adjustment_type,
                'quantity': quantity,
                'reason_code': reason_code,
                'notes': notes,
                'reconciled': adjustment_data.get('reconciled', True)
            }

        # Import and use the inventory adjustment service
        from backend.services.inventory_adjustment_service import InventoryAdjustmentService
        adjustment_service = InventoryAdjustmentService(token_manager)

        # Validate adjustment data
        validation_result = adjustment_service.validate_adjustment_data(service_data)

        if not validation_result['valid']:
            return jsonify({
                'success': False,
                'error': 'Validation failed: ' + ', '.join(validation_result['errors'])
            }), 400

        # Submit the adjustment to mxapiinventory
        result = adjustment_service.submit_inventory_adjustment(inventory_data, service_data)

        if result['success']:
            logger.info(f"✅ ADJUSTMENT API: Successfully submitted adjustment for item {inventory_data.get('itemnum', 'UNKNOWN')}")
            # Add additional metadata for frontend
            result_metadata = {
                'operation': 'inventory_adjustment',
                'itemnum': inventory_data.get('itemnum'),
                'adjustment_type': adjustment_type,
                'reason_code': reason_code,
                'notes': notes,
                'submitted_by': token_manager.username
            }

            # Add quantity or physical_count based on adjustment method
            if adjustment_method == 'PHYSICAL_COUNT':
                result_metadata['physical_count'] = physical_count
            else:
                result_metadata['quantity'] = quantity

            result.update(result_metadata)
        else:
            logger.error(f"❌ ADJUSTMENT API: Failed to submit adjustment for item {inventory_data.get('itemnum', 'UNKNOWN')}: {result.get('error')}")

        return jsonify(result)

    except ValueError as e:
        logger.error(f"Adjustment validation error: {str(e)}")
        return jsonify({'success': False, 'error': f'Invalid data: {str(e)}'}), 400
    except Exception as e:
        logger.error(f"Error submitting adjustment: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/inventory/physical-count-adjustment', methods=['POST'])
def submit_physical_count_adjustment():
    """Submit physical count adjustment with specific payload structure"""
    try:
        # Check if user is logged in
        if not hasattr(token_manager, 'username') or not token_manager.username:
            return jsonify({'success': False, 'error': 'Not authenticated'}), 401

        # Get adjustment data from request (already in the correct payload format)
        payload = request.get_json()
        if not payload:
            return jsonify({'success': False, 'error': 'No adjustment data provided'}), 400

        # Validate payload structure
        if not isinstance(payload, list) or len(payload) == 0:
            return jsonify({'success': False, 'error': 'Invalid payload structure - expected array'}), 400

        adjustment_record = payload[0]
        required_fields = ['_action', 'itemnum', 'itemsetid', 'siteid', 'location', 'invbalances']
        for field in required_fields:
            if field not in adjustment_record:
                return jsonify({'success': False, 'error': f'Missing required field: {field}'}), 400

        # Validate invbalances structure
        if not isinstance(adjustment_record['invbalances'], list) or len(adjustment_record['invbalances']) == 0:
            return jsonify({'success': False, 'error': 'Invalid invbalances structure'}), 400

        balance_record = adjustment_record['invbalances'][0]
        balance_required_fields = ['physcnt', 'physcntdate']
        for field in balance_required_fields:
            if field not in balance_record:
                return jsonify({'success': False, 'error': f'Missing required balance field: {field}'}), 400

        logger.info(f"📝 PHYSICAL COUNT API: Processing adjustment for item {adjustment_record.get('itemnum', 'UNKNOWN')}")
        logger.info(f"📝 PHYSICAL COUNT API: Physical count: {balance_record.get('physcnt')}")

        # Import and use the inventory adjustment service
        from backend.services.inventory_adjustment_service import InventoryAdjustmentService
        adjustment_service = InventoryAdjustmentService(token_manager)

        # Submit directly to Maximo using the existing service's _submit_to_maximo method
        result = adjustment_service._submit_to_maximo(payload)

        if result['success']:
            logger.info(f"✅ PHYSICAL COUNT API: Successfully submitted for item {adjustment_record.get('itemnum', 'UNKNOWN')}")
            # Add additional metadata for frontend
            result.update({
                'operation': 'physical_count_adjustment',
                'itemnum': adjustment_record.get('itemnum'),
                'physical_count': balance_record.get('physcnt'),
                'memo': balance_record.get('memo', ''),
                'submitted_by': token_manager.username,
                'message': 'Physical count adjustment submitted successfully'
            })
        else:
            logger.error(f"❌ PHYSICAL COUNT API: Failed for item {adjustment_record.get('itemnum', 'UNKNOWN')}: {result.get('error')}")

        return jsonify(result)

    except ValueError as e:
        logger.error(f"Physical count validation error: {str(e)}")
        return jsonify({'success': False, 'error': f'Invalid data: {str(e)}'}), 400
    except Exception as e:
        logger.error(f"Error submitting physical count adjustment: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/inventory/current-balance-adjustment', methods=['POST'])
def submit_current_balance_adjustment():
    """Submit current balance adjustment with specific payload structure"""
    try:
        # Check if user is logged in
        if not hasattr(token_manager, 'username') or not token_manager.username:
            return jsonify({'success': False, 'error': 'Not authenticated'}), 401

        # Get adjustment data from request (already in the correct payload format)
        payload = request.get_json()
        if not payload:
            return jsonify({'success': False, 'error': 'No adjustment data provided'}), 400

        # Validate payload structure
        if not isinstance(payload, list) or len(payload) == 0:
            return jsonify({'success': False, 'error': 'Invalid payload structure - expected array'}), 400

        adjustment_record = payload[0]
        required_fields = ['_action', 'itemnum', 'itemsetid', 'siteid', 'location', 'invbalances']
        for field in required_fields:
            if field not in adjustment_record:
                return jsonify({'success': False, 'error': f'Missing required field: {field}'}), 400

        # Validate invbalances structure
        if not isinstance(adjustment_record['invbalances'], list) or len(adjustment_record['invbalances']) == 0:
            return jsonify({'success': False, 'error': 'Invalid invbalances structure'}), 400

        balance_record = adjustment_record['invbalances'][0]
        balance_required_fields = ['curbal']
        for field in balance_required_fields:
            if field not in balance_record:
                return jsonify({'success': False, 'error': f'Missing required balance field: {field}'}), 400

        logger.info(f"📝 CURRENT BALANCE API: Processing adjustment for item {adjustment_record.get('itemnum', 'UNKNOWN')}")
        logger.info(f"📝 CURRENT BALANCE API: New balance: {balance_record.get('curbal')}")

        # Import and use the inventory adjustment service
        from backend.services.inventory_adjustment_service import InventoryAdjustmentService
        adjustment_service = InventoryAdjustmentService(token_manager)

        # Submit directly to Maximo using the existing service's _submit_to_maximo method
        result = adjustment_service._submit_to_maximo(payload)

        if result['success']:
            logger.info(f"✅ CURRENT BALANCE API: Successfully submitted for item {adjustment_record.get('itemnum', 'UNKNOWN')}")
            # Add additional metadata for frontend
            result.update({
                'operation': 'current_balance_adjustment',
                'itemnum': adjustment_record.get('itemnum'),
                'new_balance': balance_record.get('curbal'),
                'memo': balance_record.get('memo', ''),
                'submitted_by': token_manager.username,
                'message': 'Current balance adjustment submitted successfully'
            })
        else:
            logger.error(f"❌ CURRENT BALANCE API: Failed for item {adjustment_record.get('itemnum', 'UNKNOWN')}: {result.get('error')}")

        return jsonify(result)

    except ValueError as e:
        logger.error(f"Current balance validation error: {str(e)}")
        return jsonify({'success': False, 'error': f'Invalid data: {str(e)}'}), 400
    except Exception as e:
        logger.error(f"Error submitting current balance adjustment: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/inventory/avgcost-adjustment', methods=['POST'])
def submit_avgcost_adjustment():
    """Submit average cost adjustment with specific payload structure"""
    try:
        # Check if user is logged in
        if not hasattr(token_manager, 'username') or not token_manager.username:
            return jsonify({'success': False, 'error': 'Not authenticated'}), 401

        # Get adjustment data from request (already in the correct payload format)
        payload = request.get_json()
        if not payload:
            return jsonify({'success': False, 'error': 'No adjustment data provided'}), 400

        # Validate payload structure
        if not isinstance(payload, list) or len(payload) == 0:
            return jsonify({'success': False, 'error': 'Invalid payload structure - expected array'}), 400

        adjustment_record = payload[0]
        required_fields = ['_action', 'itemnum', 'itemsetid', 'siteid', 'location', 'invcost']
        for field in required_fields:
            if field not in adjustment_record:
                return jsonify({'success': False, 'error': f'Missing required field: {field}'}), 400

        # Validate invcost structure
        if not isinstance(adjustment_record['invcost'], list) or len(adjustment_record['invcost']) == 0:
            return jsonify({'success': False, 'error': 'Invalid invcost structure'}), 400

        cost_record = adjustment_record['invcost'][0]
        cost_required_fields = ['avgcost']
        for field in cost_required_fields:
            if field not in cost_record:
                return jsonify({'success': False, 'error': f'Missing required cost field: {field}'}), 400

        logger.info(f"📝 AVGCOST API: Processing adjustment for item {adjustment_record.get('itemnum', 'UNKNOWN')}")
        logger.info(f"📝 AVGCOST API: New average cost: {cost_record.get('avgcost')}")

        # Import and use the inventory adjustment service
        from backend.services.inventory_adjustment_service import InventoryAdjustmentService
        adjustment_service = InventoryAdjustmentService(token_manager)

        # Submit directly to Maximo using the existing service's _submit_to_maximo method
        result = adjustment_service._submit_to_maximo(payload)

        if result['success']:
            logger.info(f"✅ AVGCOST API: Successfully submitted for item {adjustment_record.get('itemnum', 'UNKNOWN')}")
            # Add additional metadata for frontend
            result.update({
                'operation': 'avgcost_adjustment',
                'itemnum': adjustment_record.get('itemnum'),
                'new_avgcost': cost_record.get('avgcost'),
                'conditioncode': cost_record.get('conditioncode', ''),
                'submitted_by': token_manager.username,
                'message': 'Average cost adjustment submitted successfully'
            })
        else:
            logger.error(f"❌ AVGCOST API: Failed for item {adjustment_record.get('itemnum', 'UNKNOWN')}: {result.get('error')}")

        return jsonify(result)

    except ValueError as e:
        logger.error(f"Average cost validation error: {str(e)}")
        return jsonify({'success': False, 'error': f'Invalid data: {str(e)}'}), 400
    except Exception as e:
        logger.error(f"Error submitting average cost adjustment: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/inventory/stdcost-adjustment', methods=['POST'])
def submit_stdcost_adjustment():
    """Submit standard cost adjustment with specific payload structure"""
    try:
        # Check if user is logged in
        if not hasattr(token_manager, 'username') or not token_manager.username:
            return jsonify({'success': False, 'error': 'Not authenticated'}), 401

        # Get adjustment data from request (already in the correct payload format)
        payload = request.get_json()
        if not payload:
            return jsonify({'success': False, 'error': 'No adjustment data provided'}), 400

        # Validate payload structure
        if not isinstance(payload, list) or len(payload) == 0:
            return jsonify({'success': False, 'error': 'Invalid payload structure - expected array'}), 400

        adjustment_record = payload[0]
        required_fields = ['_action', 'itemnum', 'itemsetid', 'siteid', 'location', 'invcost']
        for field in required_fields:
            if field not in adjustment_record:
                return jsonify({'success': False, 'error': f'Missing required field: {field}'}), 400

        # Validate invcost structure
        if not isinstance(adjustment_record['invcost'], list) or len(adjustment_record['invcost']) == 0:
            return jsonify({'success': False, 'error': 'Invalid invcost structure'}), 400

        cost_record = adjustment_record['invcost'][0]
        cost_required_fields = ['stdcost']
        for field in cost_required_fields:
            if field not in cost_record:
                return jsonify({'success': False, 'error': f'Missing required cost field: {field}'}), 400

        logger.info(f"📝 STDCOST API: Processing adjustment for item {adjustment_record.get('itemnum', 'UNKNOWN')}")
        logger.info(f"📝 STDCOST API: New standard cost: {cost_record.get('stdcost')}")

        # Import and use the inventory adjustment service
        from backend.services.inventory_adjustment_service import InventoryAdjustmentService
        adjustment_service = InventoryAdjustmentService(token_manager)

        # Submit directly to Maximo using the existing service's _submit_to_maximo method
        result = adjustment_service._submit_to_maximo(payload)

        if result['success']:
            logger.info(f"✅ STDCOST API: Successfully submitted for item {adjustment_record.get('itemnum', 'UNKNOWN')}")
            # Add additional metadata for frontend
            result.update({
                'operation': 'stdcost_adjustment',
                'itemnum': adjustment_record.get('itemnum'),
                'new_stdcost': cost_record.get('stdcost'),
                'conditioncode': cost_record.get('conditioncode', ''),
                'submitted_by': token_manager.username,
                'message': 'Standard cost adjustment submitted successfully'
            })
        else:
            logger.error(f"❌ STDCOST API: Failed for item {adjustment_record.get('itemnum', 'UNKNOWN')}: {result.get('error')}")

        return jsonify(result)

    except ValueError as e:
        logger.error(f"Standard cost validation error: {str(e)}")
        return jsonify({'success': False, 'error': f'Invalid data: {str(e)}'}), 400
    except Exception as e:
        logger.error(f"Error submitting standard cost adjustment: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/inventory/no-balance-physical-count', methods=['POST'])
def submit_no_balance_physical_count():
    """Submit physical count adjustment for items with no existing invbalances"""
    try:
        # Check if user is logged in
        if not hasattr(token_manager, 'username') or not token_manager.username:
            return jsonify({'success': False, 'error': 'Not authenticated'}), 401

        # Get adjustment data from request (already in the correct payload format)
        payload = request.get_json()
        if not payload:
            return jsonify({'success': False, 'error': 'No adjustment data provided'}), 400

        # Validate payload structure
        if not isinstance(payload, list) or len(payload) == 0:
            return jsonify({'success': False, 'error': 'Invalid payload structure - expected array'}), 400

        adjustment_record = payload[0]
        required_fields = ['_action', 'itemnum', 'itemsetid', 'siteid', 'location', 'invbalances']
        for field in required_fields:
            if field not in adjustment_record:
                return jsonify({'success': False, 'error': f'Missing required field: {field}'}), 400

        # Validate invbalances structure
        if not isinstance(adjustment_record['invbalances'], list) or len(adjustment_record['invbalances']) == 0:
            return jsonify({'success': False, 'error': 'Invalid invbalances structure'}), 400

        balance_record = adjustment_record['invbalances'][0]
        balance_required_fields = ['physcnt', 'physcntdate']
        for field in balance_required_fields:
            if field not in balance_record:
                return jsonify({'success': False, 'error': f'Missing required balance field: {field}'}), 400

        logger.info(f"📝 NO-BALANCE PHYSICAL COUNT API: Processing adjustment for item {adjustment_record.get('itemnum', 'UNKNOWN')}")
        logger.info(f"📝 NO-BALANCE PHYSICAL COUNT API: Physical count: {balance_record.get('physcnt')}")

        # Import and use the inventory adjustment service
        from backend.services.inventory_adjustment_service import InventoryAdjustmentService
        adjustment_service = InventoryAdjustmentService(token_manager)

        # Submit directly to Maximo using the existing service's _submit_to_maximo method
        result = adjustment_service._submit_to_maximo(payload)

        if result['success']:
            logger.info(f"✅ NO-BALANCE PHYSICAL COUNT API: Successfully submitted for item {adjustment_record.get('itemnum', 'UNKNOWN')}")
            # Add additional metadata for frontend
            result.update({
                'operation': 'no_balance_physical_count',
                'itemnum': adjustment_record.get('itemnum'),
                'physical_count': balance_record.get('physcnt'),
                'location': adjustment_record.get('location'),
                'binnum': balance_record.get('binnum', ''),
                'memo': balance_record.get('memo', ''),
                'submitted_by': token_manager.username,
                'message': 'Physical count adjustment submitted successfully (new inventory record created)'
            })
        else:
            logger.error(f"❌ NO-BALANCE PHYSICAL COUNT API: Failed for item {adjustment_record.get('itemnum', 'UNKNOWN')}: {result.get('error')}")

        return jsonify(result)

    except ValueError as e:
        logger.error(f"No-balance physical count validation error: {str(e)}")
        return jsonify({'success': False, 'error': f'Invalid data: {str(e)}'}), 400
    except Exception as e:
        logger.error(f"Error submitting no-balance physical count adjustment: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/inventory/no-balance-current-balance', methods=['POST'])
def submit_no_balance_current_balance():
    """Submit current balance adjustment for items with no existing invbalances"""
    try:
        # Check if user is logged in
        if not hasattr(token_manager, 'username') or not token_manager.username:
            return jsonify({'success': False, 'error': 'Not authenticated'}), 401

        # Get adjustment data from request (already in the correct payload format)
        payload = request.get_json()
        if not payload:
            return jsonify({'success': False, 'error': 'No adjustment data provided'}), 400

        # Validate payload structure
        if not isinstance(payload, list) or len(payload) == 0:
            return jsonify({'success': False, 'error': 'Invalid payload structure - expected array'}), 400

        adjustment_record = payload[0]
        required_fields = ['_action', 'itemnum', 'itemsetid', 'siteid', 'location', 'invbalances']
        for field in required_fields:
            if field not in adjustment_record:
                return jsonify({'success': False, 'error': f'Missing required field: {field}'}), 400

        # Validate invbalances structure
        if not isinstance(adjustment_record['invbalances'], list) or len(adjustment_record['invbalances']) == 0:
            return jsonify({'success': False, 'error': 'Invalid invbalances structure'}), 400

        balance_record = adjustment_record['invbalances'][0]
        balance_required_fields = ['curbal']
        for field in balance_required_fields:
            if field not in balance_record:
                return jsonify({'success': False, 'error': f'Missing required balance field: {field}'}), 400

        logger.info(f"📝 NO-BALANCE CURRENT BALANCE API: Processing adjustment for item {adjustment_record.get('itemnum', 'UNKNOWN')}")
        logger.info(f"📝 NO-BALANCE CURRENT BALANCE API: Current balance: {balance_record.get('curbal')}")

        # Import and use the inventory adjustment service
        from backend.services.inventory_adjustment_service import InventoryAdjustmentService
        adjustment_service = InventoryAdjustmentService(token_manager)

        # Submit directly to Maximo using the existing service's _submit_to_maximo method
        result = adjustment_service._submit_to_maximo(payload)

        if result['success']:
            logger.info(f"✅ NO-BALANCE CURRENT BALANCE API: Successfully submitted for item {adjustment_record.get('itemnum', 'UNKNOWN')}")
            # Add additional metadata for frontend
            result.update({
                'operation': 'no_balance_current_balance',
                'itemnum': adjustment_record.get('itemnum'),
                'current_balance': balance_record.get('curbal'),
                'location': adjustment_record.get('location'),
                'binnum': balance_record.get('binnum', ''),
                'memo': balance_record.get('memo', ''),
                'submitted_by': token_manager.username,
                'message': 'Current balance adjustment submitted successfully (new inventory record created)'
            })
        else:
            logger.error(f"❌ NO-BALANCE CURRENT BALANCE API: Failed for item {adjustment_record.get('itemnum', 'UNKNOWN')}: {result.get('error')}")

        return jsonify(result)

    except ValueError as e:
        logger.error(f"No-balance current balance validation error: {str(e)}")
        return jsonify({'success': False, 'error': f'Invalid data: {str(e)}'}), 400
    except Exception as e:
        logger.error(f"Error submitting no-balance current balance adjustment: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/inventory/transfer', methods=['POST'])
def submit_inventory_transfer():
    """Submit inventory transfer"""
    try:
        # Check if user is logged in
        if not hasattr(token_manager, 'username') or not token_manager.username:
            return jsonify({'success': False, 'error': 'Not authenticated'}), 401

        # Get transfer data from request
        transfer_data = request.get_json()
        if not transfer_data:
            return jsonify({'success': False, 'error': 'No transfer data provided'}), 400

        # Validate required fields
        required_fields = ['inventory_data', 'quantity', 'to_location']
        for field in required_fields:
            if field not in transfer_data:
                return jsonify({'success': False, 'error': f'Missing required field: {field}'}), 400

        inventory_data = transfer_data['inventory_data']
        quantity = float(transfer_data['quantity'])
        to_location = transfer_data['to_location']
        to_bin = transfer_data.get('to_bin', '')
        notes = transfer_data.get('notes', '')

        # Validate quantity
        current_balance = float(inventory_data.get('currentbalance', 0))
        if quantity > current_balance:
            return jsonify({
                'success': False,
                'error': f'Transfer quantity ({quantity}) exceeds available balance ({current_balance})'
            }), 400

        logger.info(f"🔄 TRANSFER API: Processing transfer for item {inventory_data.get('itemnum', 'UNKNOWN')}")
        logger.info(f"🔄 TRANSFER API: Quantity: {quantity}, From: {inventory_data.get('storeloc')}, To: {to_location}")

        # In a real implementation, this would:
        # 1. Create MATRECTRANS records for both ISSUE (from) and RECEIPT (to)
        # 2. Update source INVENTORY/INVBALANCES records (decrease)
        # 3. Update/create destination INVENTORY/INVBALANCES records (increase)
        # 4. Handle bin-to-bin transfers within same location
        # 5. Create audit trail records

        # For now, simulate successful submission
        result = {
            'success': True,
            'operation': 'inventory_transfer',
            'itemnum': inventory_data.get('itemnum'),
            'quantity': quantity,
            'from_location': inventory_data.get('storeloc'),
            'from_bin': inventory_data.get('binnum'),
            'to_location': to_location,
            'to_bin': to_bin,
            'notes': notes,
            'timestamp': datetime.datetime.now().isoformat(),
            'submitted_by': token_manager.username,
            'message': f'Transfer of {quantity:.2f} units from {inventory_data.get("storeloc")} to {to_location} submitted successfully'
        }

        logger.info(f"✅ TRANSFER API: Transfer submitted for item {inventory_data.get('itemnum', 'UNKNOWN')}")
        return jsonify(result)

    except ValueError as e:
        logger.error(f"Transfer validation error: {str(e)}")
        return jsonify({'success': False, 'error': f'Invalid data: {str(e)}'}), 400
    except Exception as e:
        logger.error(f"Error submitting transfer: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/inventory/transfer-current-item', methods=['POST'])
def submit_transfer_current_item():
    """Submit transfer current item using MXAPIINVENTORY TransferCurrentItem action"""
    try:
        # Check if user is logged in
        if not hasattr(token_manager, 'username') or not token_manager.username:
            return jsonify({'success': False, 'error': 'Not authenticated'}), 401

        # Get transfer data from request
        transfer_data = request.get_json()
        if not transfer_data:
            return jsonify({'success': False, 'error': 'No transfer data provided'}), 400

        logger.info(f"🔄 TRANSFER CURRENT ITEM: Processing transfer for item {transfer_data.get('itemnum', 'UNKNOWN')}")
        logger.debug(f"🔄 TRANSFER CURRENT ITEM: Transfer data: {json.dumps(transfer_data, indent=2)}")

        # Initialize the inventory transfer service
        from backend.services.inventory_transfer_service import InventoryTransferService
        transfer_service = InventoryTransferService(token_manager)

        # Submit the transfer using the service
        result = transfer_service.submit_transfer_current_item(transfer_data)

        if result['success']:
            logger.info(f"✅ TRANSFER CURRENT ITEM: Successfully submitted for item {transfer_data.get('itemnum')}")
            return jsonify(result)
        else:
            logger.error(f"❌ TRANSFER CURRENT ITEM: Failed for item {transfer_data.get('itemnum')}: {result.get('error')}")
            return jsonify(result), 400

    except Exception as e:
        logger.error(f"❌ TRANSFER CURRENT ITEM: Exception occurred: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/inventory/transfer-same-site', methods=['POST'])
def submit_same_site_transfer():
    """Submit same-site transfer using source site context with conversion factor"""
    try:
        # Check if user is logged in
        if not hasattr(token_manager, 'username') or not token_manager.username:
            return jsonify({'success': False, 'error': 'Not authenticated'}), 401

        # Get transfer data from request
        transfer_data = request.get_json()
        if not transfer_data:
            return jsonify({'success': False, 'error': 'No transfer data provided'}), 400

        logger.info(f"🔄 SAME SITE TRANSFER: Processing for item {transfer_data.get('itemnum', 'UNKNOWN')}")

        # Initialize the inventory transfer service
        from backend.services.inventory_transfer_service import InventoryTransferService
        transfer_service = InventoryTransferService(token_manager)

        # Submit the same-site transfer
        result = transfer_service.submit_same_site_transfer(transfer_data)

        if result['success']:
            logger.info(f"✅ SAME SITE TRANSFER: Success for item {transfer_data.get('itemnum')}")
            return jsonify(result)
        else:
            logger.error(f"❌ SAME SITE TRANSFER: Failed for item {transfer_data.get('itemnum')}: {result.get('error')}")
            return jsonify(result), 400

    except Exception as e:
        logger.error(f"❌ SAME SITE TRANSFER: Exception occurred: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/inventory/transfer-cross-site', methods=['POST'])
def submit_cross_site_transfer():
    """Submit cross-site transfer using destination site context with toissueunit"""
    try:
        # Check if user is logged in
        if not hasattr(token_manager, 'username') or not token_manager.username:
            return jsonify({'success': False, 'error': 'Not authenticated'}), 401

        # Get transfer data from request
        transfer_data = request.get_json()
        if not transfer_data:
            return jsonify({'success': False, 'error': 'No transfer data provided'}), 400

        logger.info(f"🔄 CROSS SITE TRANSFER: Processing for item {transfer_data.get('itemnum', 'UNKNOWN')}")

        # Initialize the inventory transfer service
        from backend.services.inventory_transfer_service import InventoryTransferService
        transfer_service = InventoryTransferService(token_manager)

        # Submit the cross-site transfer
        result = transfer_service.submit_cross_site_transfer(transfer_data)

        if result['success']:
            logger.info(f"✅ CROSS SITE TRANSFER: Success for item {transfer_data.get('itemnum')}")
            return jsonify(result)
        else:
            logger.error(f"❌ CROSS SITE TRANSFER: Failed for item {transfer_data.get('itemnum')}: {result.get('error')}")
            return jsonify(result), 400

    except Exception as e:
        logger.error(f"❌ CROSS SITE TRANSFER: Exception occurred: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500


@app.route('/api/inventory/transfer-current-item-destination-context', methods=['POST'])
def submit_transfer_current_item_destination_context():
    """Submit transfer current item using DESTINATION SITE CONTEXT approach"""
    try:
        # Check if user is logged in
        if not hasattr(token_manager, 'username') or not token_manager.username:
            return jsonify({'success': False, 'error': 'Not authenticated'}), 401

        # Get transfer data from request
        transfer_data = request.get_json()
        if not transfer_data:
            return jsonify({'success': False, 'error': 'No transfer data provided'}), 400

        logger.info(f"🔄 DESTINATION CONTEXT TRANSFER: Received request for item {transfer_data.get('itemnum', 'UNKNOWN')}")

        # Initialize the destination context transfer service
        from backend.services.destination_context_transfer_service import DestinationContextTransferService
        transfer_service = DestinationContextTransferService(token_manager)

        # Submit the transfer using destination context
        result = transfer_service.submit_transfer_with_destination_context(transfer_data)

        if result['success']:
            logger.info(f"✅ DESTINATION CONTEXT TRANSFER: Success for item {transfer_data.get('itemnum')}")
        else:
            logger.error(f"❌ DESTINATION CONTEXT TRANSFER: Failed for item {transfer_data.get('itemnum')}: {result.get('error')}")

        return jsonify(result)

    except Exception as e:
        logger.error(f"❌ DESTINATION CONTEXT TRANSFER: Exception occurred: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/inventory/transfer-current-item/storerooms', methods=['GET'])
def get_transfer_storerooms():
    """Get available storerooms for transfer destination"""
    try:
        # Check if user is logged in
        if not hasattr(token_manager, 'username') or not token_manager.username:
            return jsonify({'success': False, 'error': 'Not authenticated'}), 401

        # Get site ID from query parameters
        siteid = request.args.get('siteid')
        if not siteid:
            return jsonify({'success': False, 'error': 'Site ID is required'}), 400

        logger.info(f"🏢 TRANSFER STOREROOMS: Getting storerooms for site {siteid}")

        # Initialize the inventory transfer service
        from backend.services.inventory_transfer_service import InventoryTransferService
        transfer_service = InventoryTransferService(token_manager)

        # Get available storerooms
        storerooms = transfer_service.get_available_storerooms(siteid)

        logger.info(f"✅ TRANSFER STOREROOMS: Found {len(storerooms)} storerooms for site {siteid}")
        return jsonify({
            'success': True,
            'storerooms': storerooms,
            'siteid': siteid
        })

    except Exception as e:
        logger.error(f"❌ TRANSFER STOREROOMS: Exception occurred: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/inventory/transfer-current-item/bins-lots-conditions', methods=['GET'])
def get_transfer_bins_lots_conditions():
    """Get available bins, lots, and condition codes for transfer"""
    try:
        # Check if user is logged in
        if not hasattr(token_manager, 'username') or not token_manager.username:
            return jsonify({'success': False, 'error': 'Not authenticated'}), 401

        # Get parameters from query
        siteid = request.args.get('siteid')
        location = request.args.get('location')

        if not siteid or not location:
            return jsonify({'success': False, 'error': 'Site ID and location are required'}), 400

        logger.info(f"📦 TRANSFER BINS/LOTS/CONDITIONS: Getting data for site {siteid}, location {location}")

        # Initialize the inventory transfer service
        from backend.services.inventory_transfer_service import InventoryTransferService
        transfer_service = InventoryTransferService(token_manager)

        # Get available bins, lots, and conditions
        data = transfer_service.get_available_bins_lots_conditions(siteid, location)

        logger.info(f"✅ TRANSFER BINS/LOTS/CONDITIONS: Found {len(data['bins'])} bins, {len(data['lots'])} lots, {len(data['conditions'])} conditions")
        return jsonify({
            'success': True,
            'data': data,
            'siteid': siteid,
            'location': location
        })

    except Exception as e:
        logger.error(f"❌ TRANSFER BINS/LOTS/CONDITIONS: Exception occurred: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/inventory/transfer-current-item/issue-units', methods=['GET'])
def get_transfer_issue_units():
    """Get available issue units for transfer"""
    try:
        # Check if user is logged in
        if not hasattr(token_manager, 'username') or not token_manager.username:
            return jsonify({'success': False, 'error': 'Not authenticated'}), 401

        # Get item number from query parameters
        itemnum = request.args.get('itemnum')
        if not itemnum:
            return jsonify({'success': False, 'error': 'Item number is required'}), 400

        logger.info(f"🔧 TRANSFER ISSUE UNITS: Getting issue units for item {itemnum}")

        # Initialize the inventory transfer service
        from backend.services.inventory_transfer_service import InventoryTransferService
        transfer_service = InventoryTransferService(token_manager)

        # Get available issue units
        units = transfer_service.get_available_issue_units(itemnum)

        logger.info(f"✅ TRANSFER ISSUE UNITS: Found {len(units)} issue units for item {itemnum}")
        return jsonify({
            'success': True,
            'units': units,
            'itemnum': itemnum
        })

    except Exception as e:
        logger.error(f"❌ TRANSFER ISSUE UNITS: Exception occurred: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500


# ============================================================================
# ISSUE CURRENT ITEM API ENDPOINTS
# ============================================================================

@app.route('/api/inventory/issue-current-item', methods=['POST'])
def submit_issue_current_item():
    """Submit issue current item using MXAPIINVENTORY endpoint"""
    try:
        # Check if user is logged in
        if not hasattr(token_manager, 'username') or not token_manager.username:
            return jsonify({'success': False, 'error': 'Not authenticated'}), 401

        # Get issue data from request
        issue_data = request.get_json()
        if not issue_data:
            return jsonify({'success': False, 'error': 'No issue data provided'}), 400

        logger.info(f"🔄 ISSUE CURRENT ITEM: Processing issue for item {issue_data.get('itemnum', 'UNKNOWN')}")
        logger.debug(f"🔄 ISSUE CURRENT ITEM: Issue data: {json.dumps(issue_data, indent=2)}")

        # Initialize the inventory issue service
        from backend.services.inventory_issue_service import InventoryIssueService
        issue_service = InventoryIssueService(token_manager)

        # Submit the issue using the service
        result = issue_service.submit_issue_current_item(issue_data)

        if result['success']:
            logger.info(f"✅ ISSUE CURRENT ITEM: Successfully submitted for item {issue_data.get('itemnum', 'UNKNOWN')}")
        else:
            logger.error(f"❌ ISSUE CURRENT ITEM: Failed for item {issue_data.get('itemnum', 'UNKNOWN')}: {result.get('error')}")

        return jsonify(result)

    except Exception as e:
        logger.error(f"❌ ISSUE CURRENT ITEM: Exception occurred: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/inventory/issue-current-item/work-orders', methods=['GET'])
def get_issue_work_orders():
    """Get work orders for issue transactions using MXAPIWODETAIL with configurable status filtering"""
    try:
        # Check if user is logged in
        if not hasattr(token_manager, 'username') or not token_manager.username:
            return jsonify({'success': False, 'error': 'Not authenticated'}), 401

        # Get parameters from query
        siteid = request.args.get('siteid')
        search_term = request.args.get('search', '')

        if not siteid:
            return jsonify({'success': False, 'error': 'Site ID is required'}), 400

        # Get configured work order statuses from issue material config
        issue_material_config = load_issue_material_config()
        configured_wo_statuses = issue_material_config.get('work_order_statuses', ['WMATL', 'PISSUE'])

        logger.info(f"🔧 ISSUE WORK ORDERS: Getting work orders for site {siteid}, configured WO statuses: {configured_wo_statuses}, search: '{search_term}'")

        # Call MXAPIWODETAIL directly with proper filtering
        base_url = getattr(token_manager, 'base_url', '')
        api_url = f"{base_url}/oslc/os/mxapiwodetail"

        # Build where clause: siteid, istask=0, historyflag=0, and status filtering
        where_conditions = [
            f'siteid="{siteid}"',
            'istask=0',
            'historyflag=0',
            'woclass="WORKORDER"'
        ]

        # Add status filter using configured WO statuses from Issue Material Config
        if configured_wo_statuses:
            status_list = '","'.join(configured_wo_statuses)
            where_conditions.append(f'status in ["{status_list}"]')
            logger.info(f"🔧 ISSUE WORK ORDERS: Using configured WO statuses: {configured_wo_statuses}")

        # Add search filter if provided
        if search_term and search_term.strip():
            search_term = search_term.strip()
            search_conditions = [
                f'wonum="{search_term}"',
                f'wonum like "%{search_term}%"',
                f'description like "%{search_term}%"'
            ]
            where_conditions.append(f'({" or ".join(search_conditions)})')

        params = {
            "oslc.select": "wonum,description,status,siteid,assetnum,location,worktype",
            "oslc.where": " and ".join(where_conditions),
            "oslc.pageSize": "100",
            "lean": "1"
        }

        logger.info(f"🔧 ISSUE WORK ORDERS: Filter: {params['oslc.where']}")

        response = token_manager.session.get(
            api_url,
            params=params,
            timeout=(5.0, 15),
            headers={"Accept": "application/json"}
        )

        if response.status_code == 200:
            try:
                data = response.json()
                work_orders_raw = data.get('member', [])

                # Format work orders for display
                formatted_work_orders = []
                for wo in work_orders_raw:
                    wonum = wo.get('wonum', '')
                    description = wo.get('description', 'No description')[:50]
                    status = wo.get('status', '')
                    wo_siteid = wo.get('siteid', '')

                    formatted_wo = {
                        'wonum': wonum,
                        'description': description,
                        'status': status,
                        'siteid': wo_siteid,
                        'assetnum': wo.get('assetnum', ''),
                        'location': wo.get('location', ''),
                        'worktype': wo.get('worktype', ''),
                        'display_text': f"{wonum} - {description} ({status}) [{wo_siteid}]"
                    }
                    formatted_work_orders.append(formatted_wo)

                logger.info(f"✅ ISSUE WORK ORDERS: Found {len(formatted_work_orders)} work orders for site {siteid}")
                return jsonify({
                    'success': True,
                    'work_orders': formatted_work_orders,
                    'siteid': siteid
                })

            except ValueError as e:
                logger.error(f"❌ ISSUE WORK ORDERS: Failed to parse JSON: {e}")
                return jsonify({'success': True, 'work_orders': []})

        else:
            logger.error(f"❌ ISSUE WORK ORDERS: Failed to get work orders: {response.status_code}")
            return jsonify({'success': True, 'work_orders': []})

    except Exception as e:
        logger.error(f"❌ ISSUE WORK ORDERS: Exception occurred: {str(e)}")
        return jsonify({'success': True, 'work_orders': []})

@app.route('/api/inventory/issue-current-item/work-order-tasks', methods=['GET'])
def get_issue_work_order_tasks():
    """Get tasks for a specific work order using MXAPIWODETAIL with parent filtering and search functionality"""
    try:
        # Check if user is logged in
        if not hasattr(token_manager, 'username') or not token_manager.username:
            return jsonify({'success': False, 'error': 'Not authenticated'}), 401

        # Get parameters from query
        wonum = request.args.get('wonum')
        siteid = request.args.get('siteid')
        search_term = request.args.get('search', '')

        if not wonum or not siteid:
            return jsonify({'success': False, 'error': 'Work order number and site ID are required'}), 400

        # Get configured task statuses from issue material config
        issue_material_config = load_issue_material_config()
        configured_statuses = issue_material_config.get('task_statuses', ['APPR', 'INPRG', 'WMATL', 'PISSUE'])

        logger.info(f"🔧 ISSUE WO TASKS: Getting tasks for work order {wonum}, configured statuses: {configured_statuses}, search: '{search_term}'")

        # Call MXAPIWODETAIL directly with proper filtering for tasks
        base_url = getattr(token_manager, 'base_url', '')
        api_url = f"{base_url}/oslc/os/mxapiwodetail"

        # Build where clause: parent=wonum, istask=1, historyflag=0, and status filtering
        where_conditions = [
            f'parent="{wonum}"',
            'istask=1',
            'historyflag=0',
            f'siteid="{siteid}"'
        ]

        # Add status filter using configured task statuses from Issue Material Config
        if configured_statuses:
            status_list = '","'.join(configured_statuses)
            where_conditions.append(f'status in ["{status_list}"]')

        # Add search filter if provided
        if search_term and search_term.strip():
            search_term = search_term.strip()
            search_conditions = [
                f'wonum="{search_term}"',
                f'wonum like "%{search_term}%"',
                f'description like "%{search_term}%"'
            ]
            where_conditions.append(f'({" or ".join(search_conditions)})')

        params = {
            "oslc.select": "wonum,taskid,description,status,siteid,parent,istask",
            "oslc.where": " and ".join(where_conditions),
            "oslc.pageSize": "50",
            "lean": "1"
        }

        logger.info(f"🔧 ISSUE WO TASKS: Filter: {params['oslc.where']}")

        response = token_manager.session.get(
            api_url,
            params=params,
            timeout=(5.0, 15),
            headers={"Accept": "application/json"}
        )

        if response.status_code == 200:
            try:
                data = response.json()
                tasks_raw = data.get('member', [])

                # Format tasks for display
                formatted_tasks = []
                for task in tasks_raw:
                    task_wonum = task.get('wonum', '')
                    task_id = task.get('taskid', '')
                    description = task.get('description', 'No description')[:50]
                    status = task.get('status', '')
                    task_siteid = task.get('siteid', '')

                    formatted_task = {
                        'wonum': task_wonum,
                        'taskid': task_id,
                        'description': description,
                        'status': status,
                        'siteid': task_siteid,
                        'parent': task.get('parent', ''),
                        'display_text': f"{task_wonum} (Task: {task_id}) - {description} ({status}) [{task_siteid}]"
                    }
                    formatted_tasks.append(formatted_task)

                logger.info(f"✅ ISSUE WO TASKS: Found {len(formatted_tasks)} tasks for work order {wonum}")
                return jsonify({
                    'success': True,
                    'tasks': formatted_tasks,
                    'wonum': wonum
                })

            except ValueError as e:
                logger.error(f"❌ ISSUE WO TASKS: Failed to parse JSON: {e}")
                return jsonify({'success': True, 'tasks': []})

        else:
            logger.error(f"❌ ISSUE WO TASKS: API returned status {response.status_code}")
            return jsonify({'success': True, 'tasks': []})

    except Exception as e:
        logger.error(f"❌ ISSUE WO TASKS: Exception occurred: {str(e)}")
        # Return empty array on error (like other endpoints do)
        return jsonify({
            'success': True,
            'tasks': []
        })

@app.route('/api/inventory/issue-current-item/assets', methods=['GET'])
def get_issue_assets():
    """Get assets for issue transactions"""
    try:
        # Check if user is logged in
        if not hasattr(token_manager, 'username') or not token_manager.username:
            return jsonify({'success': False, 'error': 'Not authenticated'}), 401

        # Get parameters from query
        siteid = request.args.get('siteid')
        search_term = request.args.get('search', '')

        if not siteid:
            return jsonify({'success': False, 'error': 'Site ID is required'}), 400

        logger.info(f"🔧 ISSUE ASSETS: Getting assets for site {siteid}, search: '{search_term}'")

        # Initialize the inventory issue service
        from backend.services.inventory_issue_service import InventoryIssueService
        issue_service = InventoryIssueService(token_manager)

        # Get assets
        assets = issue_service.get_assets(siteid, search_term)

        logger.info(f"✅ ISSUE ASSETS: Found {len(assets)} assets for site {siteid}")
        return jsonify({
            'success': True,
            'assets': assets,
            'siteid': siteid
        })

    except Exception as e:
        logger.error(f"❌ ISSUE ASSETS: Exception occurred: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/inventory/issue-current-item/gl-accounts', methods=['GET'])
def get_issue_gl_accounts():
    """Get GL accounts for issue transactions"""
    try:
        # Check if user is logged in
        if not hasattr(token_manager, 'username') or not token_manager.username:
            return jsonify({'success': False, 'error': 'Not authenticated'}), 401

        # Get account type filter
        account_type = request.args.get('type', '')

        logger.info(f"🔧 ISSUE GL ACCOUNTS: Getting GL accounts")

        # Initialize the inventory issue service
        from backend.services.inventory_issue_service import InventoryIssueService
        issue_service = InventoryIssueService(token_manager)

        # Get GL accounts
        accounts = issue_service.get_gl_accounts(account_type)

        logger.info(f"✅ ISSUE GL ACCOUNTS: Found {len(accounts)} GL accounts")
        return jsonify({
            'success': True,
            'accounts': accounts
        })

    except Exception as e:
        logger.error(f"❌ ISSUE GL ACCOUNTS: Exception occurred: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/inventory/issue-current-item/persons', methods=['GET'])
def get_issue_persons():
    """Get persons for issue to lookup"""
    try:
        # Check if user is logged in
        if not hasattr(token_manager, 'username') or not token_manager.username:
            return jsonify({'success': False, 'error': 'Not authenticated'}), 401

        # Get search term
        search_term = request.args.get('search', '')

        logger.info(f"🔧 ISSUE PERSONS: Getting persons, search: '{search_term}'")

        # Initialize the inventory issue service
        from backend.services.inventory_issue_service import InventoryIssueService
        issue_service = InventoryIssueService(token_manager)

        # Get persons
        persons = issue_service.get_persons(search_term)

        logger.info(f"✅ ISSUE PERSONS: Found {len(persons)} persons")
        return jsonify({
            'success': True,
            'persons': persons
        })

    except Exception as e:
        logger.error(f"❌ ISSUE PERSONS: Exception occurred: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/inventory/issue-current-item/locations', methods=['GET'])
def get_issue_locations():
    """Get locations for issue transactions"""
    try:
        # Check if user is logged in
        if not hasattr(token_manager, 'username') or not token_manager.username:
            return jsonify({'success': False, 'error': 'Not authenticated'}), 401

        # Get parameters
        siteid = request.args.get('siteid')
        search_term = request.args.get('search', '')

        if not siteid:
            return jsonify({'success': False, 'error': 'Site ID is required'}), 400

        logger.info(f"🔧 ISSUE LOCATIONS: Getting locations for site {siteid}, search: '{search_term}'")

        # Initialize the inventory issue service
        from backend.services.inventory_issue_service import InventoryIssueService
        issue_service = InventoryIssueService(token_manager)

        # Get locations
        locations = issue_service.get_locations(siteid, search_term)

        logger.info(f"✅ ISSUE LOCATIONS: Found {len(locations)} locations for site {siteid}")
        return jsonify({
            'success': True,
            'locations': locations,
            'siteid': siteid
        })

    except Exception as e:
        logger.error(f"❌ ISSUE LOCATIONS: Exception occurred: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/inventory/issue-current-item/requisitions', methods=['GET'])
def get_issue_requisitions():
    """Get requisitions for issue transactions"""
    try:
        # Check if user is logged in
        if not hasattr(token_manager, 'username') or not token_manager.username:
            return jsonify({'success': False, 'error': 'Not authenticated'}), 401

        # Get parameters
        siteid = request.args.get('siteid')
        search_term = request.args.get('search', '')

        if not siteid:
            return jsonify({'success': False, 'error': 'Site ID is required'}), 400

        logger.info(f"🔧 ISSUE REQUISITIONS: Getting requisitions for site {siteid}, search: '{search_term}'")

        # Initialize the inventory issue service
        from backend.services.inventory_issue_service import InventoryIssueService
        issue_service = InventoryIssueService(token_manager)

        # Get requisitions
        requisitions = issue_service.get_requisitions(siteid, search_term)

        logger.info(f"✅ ISSUE REQUISITIONS: Found {len(requisitions)} requisitions for site {siteid}")
        return jsonify({
            'success': True,
            'requisitions': requisitions,
            'siteid': siteid
        })

    except Exception as e:
        logger.error(f"❌ ISSUE REQUISITIONS: Exception occurred: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/inventory/issue-current-item/sites', methods=['GET'])
def get_issue_sites():
    """Get available sites for issue transactions"""
    try:
        # Check if user is logged in
        if not hasattr(token_manager, 'username') or not token_manager.username:
            return jsonify({'success': False, 'error': 'Not authenticated'}), 401

        logger.info(f"🔧 ISSUE SITES: Getting available sites")

        # Initialize the inventory issue service
        from backend.services.inventory_issue_service import InventoryIssueService
        issue_service = InventoryIssueService(token_manager)

        # Get sites
        sites = issue_service.get_available_sites()

        logger.info(f"✅ ISSUE SITES: Found {len(sites)} sites")
        return jsonify({
            'success': True,
            'sites': sites
        })

    except Exception as e:
        logger.error(f"❌ ISSUE SITES: Exception occurred: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/inventory/issue-current-item/inventory-balances', methods=['GET'])
def get_issue_inventory_balances():
    """Get inventory balances for bins, lots, and condition codes"""
    try:
        # Check if user is logged in
        if not hasattr(token_manager, 'username') or not token_manager.username:
            return jsonify({'success': False, 'error': 'Not authenticated'}), 401

        # Get parameters
        itemnum = request.args.get('itemnum')
        siteid = request.args.get('siteid')
        location = request.args.get('location')

        if not itemnum or not siteid or not location:
            return jsonify({'success': False, 'error': 'Item number, site ID, and location are required'}), 400

        logger.info(f"🔧 ISSUE INVENTORY BALANCES: Getting balances for {itemnum} at {siteid}/{location}")

        # Initialize the inventory issue service
        from backend.services.inventory_issue_service import InventoryIssueService
        issue_service = InventoryIssueService(token_manager)

        # Get inventory balances
        balances = issue_service.get_inventory_balances(itemnum, siteid, location)

        logger.info(f"✅ ISSUE INVENTORY BALANCES: Found {len(balances['bins'])} bins, {len(balances['lots'])} lots, {len(balances['conditions'])} conditions")
        return jsonify({
            'success': True,
            'balances': balances,
            'itemnum': itemnum,
            'siteid': siteid,
            'location': location
        })

    except Exception as e:
        logger.error(f"❌ ISSUE INVENTORY BALANCES: Exception occurred: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/inventory/issue-current-item/issue-units', methods=['GET'])
def get_issue_units():
    """Get available issue units for an item"""
    try:
        # Check if user is logged in
        if not hasattr(token_manager, 'username') or not token_manager.username:
            return jsonify({'success': False, 'error': 'Not authenticated'}), 401

        # Get item number from query parameters
        itemnum = request.args.get('itemnum')
        if not itemnum:
            return jsonify({'success': False, 'error': 'Item number is required'}), 400

        logger.info(f"🔧 ISSUE UNITS: Getting issue units for item {itemnum}")

        # Initialize the inventory issue service
        from backend.services.inventory_issue_service import InventoryIssueService
        issue_service = InventoryIssueService(token_manager)

        # Get available issue units
        units = issue_service.get_issue_units(itemnum)

        logger.info(f"✅ ISSUE UNITS: Found {len(units)} issue units for item {itemnum}")
        return jsonify({
            'success': True,
            'units': units,
            'itemnum': itemnum
        })

    except Exception as e:
        logger.error(f"❌ ISSUE UNITS: Exception occurred: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/inventory/test-mxapi', methods=['POST'])
def test_mxapiinventory_endpoint():
    """Test endpoint for mxapiinventory integration"""
    try:
        # Check if user is logged in
        if not hasattr(token_manager, 'username') or not token_manager.username:
            return jsonify({'success': False, 'error': 'Not authenticated'}), 401

        # Get test data from request or use defaults
        request_data = request.get_json() or {}

        # Use the exact test data from your example
        test_inventory_data = request_data.get('inventory_data', {
            'itemnum': '5975-60-V00-0529',
            'siteid': 'LCVKNT',
            'storeloc': 'RIP001',
            'binnum': '28-800-0004',
            'currentbalance': 30,
            'conditioncode': 'A1',
            'issueunit': 'RO',
            'lotnum': '',
            'controlacc': '',
            'shrinkageacc': ''
        })

        test_adjustment_data = request_data.get('adjustment_data', {
            'adjustment_type': 'POSITIVE',
            'quantity': 0,  # Test with 0 to match your physcnt example
            'reason_code': 'TEST_QR_SCANNER',
            'notes': 'Test adjustment from QR scanner integration'
        })

        logger.info(f"🧪 MXAPI TEST: Testing mxapiinventory integration for item {test_inventory_data.get('itemnum')}")

        # Import and use the inventory adjustment service
        from backend.services.inventory_adjustment_service import InventoryAdjustmentService
        adjustment_service = InventoryAdjustmentService(token_manager)

        # Test payload building
        try:
            payload = adjustment_service._build_adjustment_payload(test_inventory_data, test_adjustment_data)
            logger.info(f"🧪 MXAPI TEST: Payload built successfully")
        except Exception as e:
            return jsonify({
                'success': False,
                'error': f'Failed to build payload: {str(e)}',
                'test_stage': 'payload_building'
            }), 400

        # Test the full submission
        result = adjustment_service.submit_inventory_adjustment(test_inventory_data, test_adjustment_data)

        # Add test metadata
        result.update({
            'test_endpoint': True,
            'test_data_used': {
                'inventory_data': test_inventory_data,
                'adjustment_data': test_adjustment_data
            },
            'payload_generated': payload
        })

        if result['success']:
            logger.info(f"✅ MXAPI TEST: Integration test successful for item {test_inventory_data.get('itemnum')}")
        else:
            logger.error(f"❌ MXAPI TEST: Integration test failed for item {test_inventory_data.get('itemnum')}: {result.get('error')}")

        return jsonify(result)

    except Exception as e:
        logger.error(f"Error in mxapiinventory test: {str(e)}")
        return jsonify({'success': False, 'error': str(e), 'test_stage': 'general_error'}), 500

# Payload test page
@app.route('/payload-test')
def payload_test():
    """Test page to show complete payload structure"""
    return render_template('payload_test.html')

if __name__ == '__main__':
    app.run(debug=True, port=5010)
