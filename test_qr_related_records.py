#!/usr/bin/env python3
"""
Test script for QR Scanner Related Records API

This script tests the new related records functionality to ensure it's working correctly.

Author: Augment Agent
Date: 2025-01-28
"""

import requests
import json
import sys

def test_related_records_api():
    """Test the QR scanner related records API."""
    
    base_url = "http://localhost:5000"
    
    # Test data - use a known asset
    test_data = {
        "assetnum": "PUMP001",  # Replace with actual asset number
        "siteid": "BEDFORD"     # Replace with actual site ID
    }
    
    print("🧪 Testing QR Scanner Related Records API")
    print("=" * 60)
    print(f"Asset: {test_data['assetnum']}")
    print(f"Site: {test_data['siteid']}")
    print()
    
    try:
        print("📡 Sending API request...")
        response = requests.post(
            f"{base_url}/api/qr-scanner/related-records",
            json=test_data,
            timeout=30
        )
        
        print(f"Response Status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            
            if result.get('success'):
                print("✅ API Request Successful!")
                print()
                
                # Display summary
                summary = result.get('summary', {})
                print("📊 SUMMARY:")
                print(f"   • Total Records: {summary.get('total_records', 0)}")
                print(f"   • Open Work Orders: {summary.get('open_work_orders', 0)}")
                print(f"   • Open Service Requests: {summary.get('open_service_requests', 0)}")
                print(f"   • Completed Work Orders: {summary.get('completed_work_orders', 0)}")
                print(f"   • PM Schedules: {summary.get('pm_schedules', 0)}")
                print(f"   • Overdue PMs: {summary.get('overdue_pms', 0)}")
                print(f"   • Health Score: {summary.get('health_score', 0)}")
                print()
                
                # Display records breakdown
                records = result.get('records', {})
                print("📋 RECORDS BREAKDOWN:")
                
                work_orders = records.get('work_orders', [])
                print(f"   • Work Orders: {len(work_orders)}")
                if work_orders:
                    open_wos = [wo for wo in work_orders if wo.get('is_open', False)]
                    print(f"     - Open: {len(open_wos)}")
                    print(f"     - Closed: {len(work_orders) - len(open_wos)}")
                    
                    # Show first few work orders
                    print("     - Recent Work Orders:")
                    for wo in work_orders[:3]:
                        status = wo.get('status', 'N/A')
                        is_open = wo.get('is_open', False)
                        print(f"       * {wo.get('wonum', 'N/A')}: {status} ({'OPEN' if is_open else 'CLOSED'})")
                
                service_requests = records.get('service_requests', [])
                print(f"   • Service Requests: {len(service_requests)}")
                
                asset_history = records.get('asset_history', [])
                print(f"   • Asset History: {len(asset_history)}")
                
                pm_schedules = records.get('pm_schedules', [])
                print(f"   • PM Schedules: {len(pm_schedules)}")
                print()
                
                # Display analytics
                analytics = result.get('analytics', {})
                performance = analytics.get('performance_metrics', {})
                print("📈 ANALYTICS:")
                print(f"   • Health Score: {performance.get('health_score', 0)}")
                print(f"   • Reliability Score: {performance.get('reliability_score', 0)}")
                print(f"   • Availability Score: {performance.get('availability_score', 0)}")
                
                recommendations = analytics.get('recommendations', [])
                if recommendations:
                    print("   • Recommendations:")
                    for rec in recommendations:
                        print(f"     - {rec}")
                print()
                
                # Performance info
                duration = result.get('fetch_duration_ms', 0)
                print(f"⚡ Performance: {duration}ms")
                
                print()
                print("✅ ALL TESTS PASSED!")
                return True
                
            else:
                print(f"❌ API Error: {result.get('error')}")
                return False
                
        elif response.status_code == 403:
            print("❌ Authentication Error: Please log in to the application first")
            print("   1. Open http://localhost:5000 in your browser")
            print("   2. Log in with your credentials")
            print("   3. Run this test again")
            return False
            
        else:
            print(f"❌ HTTP Error: {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ Connection Error: Make sure the Flask app is running on localhost:5000")
        return False
        
    except Exception as e:
        print(f"❌ Test Error: {str(e)}")
        return False

def test_with_different_assets():
    """Test with multiple assets to verify functionality."""
    
    test_assets = [
        {"assetnum": "PUMP001", "siteid": "BEDFORD"},
        {"assetnum": "MOTOR001", "siteid": "BEDFORD"},
        {"assetnum": "VALVE001", "siteid": "BEDFORD"},
    ]
    
    print("\n🧪 Testing Multiple Assets")
    print("=" * 60)
    
    for i, asset in enumerate(test_assets, 1):
        print(f"\n{i}. Testing Asset: {asset['assetnum']}")
        print("-" * 40)
        
        try:
            response = requests.post(
                "http://localhost:5000/api/qr-scanner/related-records",
                json=asset,
                timeout=10
            )
            
            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    summary = result.get('summary', {})
                    print(f"   ✅ Success - {summary.get('total_records', 0)} total records")
                    print(f"      Open WOs: {summary.get('open_work_orders', 0)}")
                else:
                    print(f"   ❌ API Error: {result.get('error')}")
            else:
                print(f"   ❌ HTTP {response.status_code}")
                
        except Exception as e:
            print(f"   ❌ Error: {str(e)}")

if __name__ == "__main__":
    print("🚀 QR Scanner Related Records API Test")
    print("=" * 60)
    
    # Run main test
    success = test_related_records_api()
    
    if success:
        # Run additional tests
        test_with_different_assets()
        
        print("\n" + "=" * 60)
        print("🎉 All tests completed! The QR Scanner Related Records API is working.")
        print("\nNext steps:")
        print("1. Open http://localhost:5000/asset-qr-scanner")
        print("2. Scan an asset QR code")
        print("3. Verify the beautiful grid layout appears")
        print("4. Check that the Asset Overview shows correct numbers")
    else:
        print("\n" + "=" * 60)
        print("❌ Tests failed. Please check the issues above.")
        sys.exit(1)
