#!/usr/bin/env python3
"""
QR Scanner Related Records Service

This service provides comprehensive related records data for scanned assets including:
- Work orders (open, closed, historical)
- Service requests
- Asset history and maintenance records
- Preventive maintenance schedules
- Performance analytics and trends

Author: Augment Agent
Date: 2025-01-28
"""

import logging
import time
import json
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
from collections import defaultdict

logger = logging.getLogger(__name__)

class QRRelatedRecordsService:
    """Service for fetching comprehensive related records for QR scanned assets."""
    
    def __init__(self, token_manager):
        """
        Initialize the QR Related Records Service.
        
        Args:
            token_manager: Token manager instance for Maximo API authentication
        """
        self.token_manager = token_manager
        self.logger = logger
        
        # Cache for performance optimization
        self.cache = {}
        self.cache_ttl = 300  # 5 minutes cache TTL
        
    async def get_comprehensive_related_records(self, assetnum: str, siteid: str) -> Dict[str, Any]:
        """
        Get comprehensive related records for an asset including analytics and trends.
        
        Args:
            assetnum: Asset number
            siteid: Site ID
            
        Returns:
            Dict containing all related records and analytics
        """
        start_time = time.time()
        cache_key = f"{assetnum}_{siteid}_comprehensive"
        
        # Check cache first
        if self._is_cache_valid(cache_key):
            self.logger.info(f"📋 QR RECORDS: Returning cached data for {assetnum}")
            return self.cache[cache_key]['data']
        
        try:
            self.logger.info(f"📋 QR RECORDS: Fetching comprehensive records for asset {assetnum} in site {siteid}")
            
            # Fetch all related records concurrently
            records_data = await self._fetch_all_related_records(assetnum, siteid)
            
            # Generate analytics and insights
            analytics = self._generate_analytics(records_data)
            
            # Prepare comprehensive response
            comprehensive_data = {
                'success': True,
                'asset_info': {
                    'assetnum': assetnum,
                    'siteid': siteid,
                    'fetch_timestamp': datetime.now().isoformat()
                },
                'records': records_data,
                'analytics': analytics,
                'summary': self._generate_summary(records_data, analytics),
                'fetch_duration_ms': int((time.time() - start_time) * 1000)
            }
            
            # Cache the result
            self._cache_data(cache_key, comprehensive_data)
            
            self.logger.info(f"✅ QR RECORDS: Successfully fetched comprehensive data for {assetnum}")
            return comprehensive_data
            
        except Exception as e:
            self.logger.error(f"❌ QR RECORDS: Error fetching comprehensive records: {e}")
            return {
                'success': False,
                'error': str(e),
                'asset_info': {'assetnum': assetnum, 'siteid': siteid}
            }
    
    async def _fetch_all_related_records(self, assetnum: str, siteid: str) -> Dict[str, Any]:
        """Fetch all types of related records for the asset."""
        records = {
            'work_orders': await self._fetch_work_orders(assetnum, siteid),
            'service_requests': await self._fetch_service_requests(assetnum, siteid),
            'asset_history': await self._fetch_asset_history(assetnum, siteid),
            'pm_schedules': await self._fetch_pm_schedules(assetnum, siteid),
            'recent_activities': await self._fetch_recent_activities(assetnum, siteid)
        }
        
        return records
    
    async def _fetch_work_orders(self, assetnum: str, siteid: str) -> List[Dict[str, Any]]:
        """Fetch work orders for the asset."""
        try:
            base_url = getattr(self.token_manager, 'base_url', '')
            api_url = f"{base_url}/oslc/os/mxapiwodetail"

            # Fetch ALL work orders for the asset (both open and closed)
            params = {
                "oslc.select": "wonum,description,status,priority,worktype,reportdate,schedstart,targcompdate,actstart,actfinish,assetnum,siteid,istask,historyflag,wopriority,reportedby",
                "oslc.where": f'assetnum="{assetnum}" and siteid="{siteid}" and istask=0',
                "oslc.orderBy": "-reportdate",
                "oslc.pageSize": "200",  # Increased page size
                "lean": "1"
            }

            self.logger.info(f"🔍 QR RECORDS: Fetching work orders for {assetnum} in {siteid}")
            self.logger.info(f"🔍 QR RECORDS: Query URL: {api_url}")
            self.logger.info(f"🔍 QR RECORDS: Query params: {params}")

            response = self.token_manager.session.get(
                api_url,
                params=params,
                timeout=(5.0, 30),
                headers={"Accept": "application/json"}
            )

            self.logger.info(f"🔍 QR RECORDS: Response status: {response.status_code}")

            if response.status_code == 200:
                data = response.json()
                work_orders = data.get('member', [])

                self.logger.info(f"🔍 QR RECORDS: Raw work orders found: {len(work_orders)}")

                # Process and categorize work orders
                processed_wos = []
                open_count = 0
                closed_count = 0

                for wo in work_orders:
                    processed_wo = self._process_work_order(wo)
                    processed_wos.append(processed_wo)

                    # Count open vs closed
                    if processed_wo.get('is_open', False):
                        open_count += 1
                    else:
                        closed_count += 1

                self.logger.info(f"✅ QR RECORDS: Processed {len(processed_wos)} work orders for {assetnum}")
                self.logger.info(f"✅ QR RECORDS: Open: {open_count}, Closed: {closed_count}")

                return processed_wos
            else:
                self.logger.warning(f"⚠️ QR RECORDS: Failed to fetch work orders: {response.status_code}")
                self.logger.warning(f"⚠️ QR RECORDS: Response text: {response.text}")
                return []

        except Exception as e:
            self.logger.error(f"❌ QR RECORDS: Error fetching work orders: {e}")
            return []
    
    async def _fetch_service_requests(self, assetnum: str, siteid: str) -> List[Dict[str, Any]]:
        """Fetch service requests for the asset."""
        try:
            base_url = getattr(self.token_manager, 'base_url', '')
            api_url = f"{base_url}/oslc/os/mxapisr"
            
            params = {
                "oslc.select": "ticketid,description,status,priority,reportdate,reportedby,assetnum,siteid,affectedperson",
                "oslc.where": f'assetnum="{assetnum}" and siteid="{siteid}"',
                "oslc.orderBy": "-reportdate",
                "oslc.pageSize": "50",
                "lean": "1"
            }
            
            response = self.token_manager.session.get(
                api_url,
                params=params,
                timeout=(5.0, 30),
                headers={"Accept": "application/json"}
            )
            
            if response.status_code == 200:
                data = response.json()
                service_requests = data.get('member', [])
                
                processed_srs = []
                for sr in service_requests:
                    processed_sr = self._process_service_request(sr)
                    processed_srs.append(processed_sr)
                
                self.logger.info(f"✅ QR RECORDS: Found {len(processed_srs)} service requests for {assetnum}")
                return processed_srs
            else:
                self.logger.warning(f"⚠️ QR RECORDS: Failed to fetch service requests: {response.status_code}")
                return []
                
        except Exception as e:
            self.logger.error(f"❌ QR RECORDS: Error fetching service requests: {e}")
            return []
    
    async def _fetch_asset_history(self, assetnum: str, siteid: str) -> List[Dict[str, Any]]:
        """Fetch asset history and maintenance records."""
        try:
            # Fetch completed work orders as history
            base_url = getattr(self.token_manager, 'base_url', '')
            api_url = f"{base_url}/oslc/os/mxapiwodetail"
            
            params = {
                "oslc.select": "wonum,description,status,worktype,reportdate,actfinish,assetnum,siteid,actlabcost,actmatcost,acttoolcost",
                "oslc.where": f'assetnum="{assetnum}" and siteid="{siteid}" and historyflag=1 and istask=0',
                "oslc.orderBy": "-actfinish",
                "oslc.pageSize": "50",
                "lean": "1"
            }
            
            response = self.token_manager.session.get(
                api_url,
                params=params,
                timeout=(5.0, 30),
                headers={"Accept": "application/json"}
            )
            
            if response.status_code == 200:
                data = response.json()
                history_records = data.get('member', [])
                
                processed_history = []
                for record in history_records:
                    processed_record = self._process_history_record(record)
                    processed_history.append(processed_record)
                
                self.logger.info(f"✅ QR RECORDS: Found {len(processed_history)} history records for {assetnum}")
                return processed_history
            else:
                self.logger.warning(f"⚠️ QR RECORDS: Failed to fetch asset history: {response.status_code}")
                return []
                
        except Exception as e:
            self.logger.error(f"❌ QR RECORDS: Error fetching asset history: {e}")
            return []
    
    async def _fetch_pm_schedules(self, assetnum: str, siteid: str) -> List[Dict[str, Any]]:
        """Fetch preventive maintenance schedules."""
        try:
            base_url = getattr(self.token_manager, 'base_url', '')
            api_url = f"{base_url}/oslc/os/mxapipm"
            
            params = {
                "oslc.select": "pmnum,description,status,frequency,frequnit,nextdate,assetnum,siteid,route,jpnum",
                "oslc.where": f'assetnum="{assetnum}" and siteid="{siteid}"',
                "oslc.orderBy": "nextdate",
                "oslc.pageSize": "20",
                "lean": "1"
            }
            
            response = self.token_manager.session.get(
                api_url,
                params=params,
                timeout=(5.0, 30),
                headers={"Accept": "application/json"}
            )
            
            if response.status_code == 200:
                data = response.json()
                pm_schedules = data.get('member', [])
                
                processed_pms = []
                for pm in pm_schedules:
                    processed_pm = self._process_pm_schedule(pm)
                    processed_pms.append(processed_pm)
                
                self.logger.info(f"✅ QR RECORDS: Found {len(processed_pms)} PM schedules for {assetnum}")
                return processed_pms
            else:
                self.logger.warning(f"⚠️ QR RECORDS: Failed to fetch PM schedules: {response.status_code}")
                return []
                
        except Exception as e:
            self.logger.error(f"❌ QR RECORDS: Error fetching PM schedules: {e}")
            return []
    
    async def _fetch_recent_activities(self, assetnum: str, siteid: str) -> List[Dict[str, Any]]:
        """Fetch recent activities and changes for the asset."""
        # This would typically fetch from audit logs or activity tables
        # For now, we'll return recent work orders and service requests
        try:
            recent_activities = []
            
            # Get recent work orders (last 30 days)
            cutoff_date = (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d')
            
            base_url = getattr(self.token_manager, 'base_url', '')
            api_url = f"{base_url}/oslc/os/mxapiwodetail"
            
            params = {
                "oslc.select": "wonum,description,status,reportdate,reportedby,assetnum,siteid",
                "oslc.where": f'assetnum="{assetnum}" and siteid="{siteid}" and reportdate>="{cutoff_date}"',
                "oslc.orderBy": "-reportdate",
                "oslc.pageSize": "20",
                "lean": "1"
            }
            
            response = self.token_manager.session.get(
                api_url,
                params=params,
                timeout=(5.0, 30),
                headers={"Accept": "application/json"}
            )
            
            if response.status_code == 200:
                data = response.json()
                recent_wos = data.get('member', [])
                
                for wo in recent_wos:
                    activity = {
                        'type': 'work_order',
                        'id': wo.get('wonum'),
                        'description': wo.get('description', ''),
                        'date': wo.get('reportdate'),
                        'user': wo.get('reportedby', ''),
                        'status': wo.get('status', '')
                    }
                    recent_activities.append(activity)
                
                self.logger.info(f"✅ QR RECORDS: Found {len(recent_activities)} recent activities for {assetnum}")
                return recent_activities
            else:
                self.logger.warning(f"⚠️ QR RECORDS: Failed to fetch recent activities: {response.status_code}")
                return []
                
        except Exception as e:
            self.logger.error(f"❌ QR RECORDS: Error fetching recent activities: {e}")
            return []

    def _process_work_order(self, wo: Dict[str, Any]) -> Dict[str, Any]:
        """Process and enhance work order data."""
        status = wo.get('status', '').upper()

        # More comprehensive open status check
        open_statuses = ['WAPPR', 'INPRG', 'WSCH', 'WMATL', 'APPR', 'WPCOND', 'WPLAN', 'WSTART']
        closed_statuses = ['COMP', 'CLOSE', 'CLOSED', 'CAN', 'CANCELLED']

        is_open = status in open_statuses or (status not in closed_statuses and not wo.get('actfinish'))

        processed = {
            'wonum': wo.get('wonum', ''),
            'description': wo.get('description', ''),
            'status': status,
            'priority': wo.get('priority', ''),
            'worktype': wo.get('worktype', ''),
            'reportdate': wo.get('reportdate', ''),
            'schedstart': wo.get('schedstart', ''),
            'targcompdate': wo.get('targcompdate', ''),
            'actstart': wo.get('actstart', ''),
            'actfinish': wo.get('actfinish', ''),
            'reportedby': wo.get('reportedby', ''),
            'historyflag': wo.get('historyflag', 0),
            'is_open': is_open,
            'days_open': self._calculate_days_open(wo.get('reportdate'), wo.get('actfinish')),
            'urgency_level': self._calculate_urgency(wo.get('priority', ''), wo.get('reportdate')),
            'record_type': 'work_order'
        }

        # Debug logging for work order processing
        self.logger.debug(f"🔍 QR RECORDS: WO {wo.get('wonum')} - Status: {status}, Open: {is_open}")

        return processed

    def _process_service_request(self, sr: Dict[str, Any]) -> Dict[str, Any]:
        """Process and enhance service request data."""
        processed = {
            'ticketid': sr.get('ticketid', ''),
            'description': sr.get('description', ''),
            'status': sr.get('status', ''),
            'priority': sr.get('priority', ''),
            'reportdate': sr.get('reportdate', ''),
            'reportedby': sr.get('reportedby', ''),
            'affectedperson': sr.get('affectedperson', ''),
            'is_open': sr.get('status', '') not in ['CLOSED', 'RESOLVED'],
            'days_open': self._calculate_days_open(sr.get('reportdate')),
            'record_type': 'service_request'
        }

        return processed

    def _process_history_record(self, record: Dict[str, Any]) -> Dict[str, Any]:
        """Process and enhance history record data."""
        processed = {
            'wonum': record.get('wonum', ''),
            'description': record.get('description', ''),
            'status': record.get('status', ''),
            'worktype': record.get('worktype', ''),
            'reportdate': record.get('reportdate', ''),
            'actfinish': record.get('actfinish', ''),
            'total_cost': self._calculate_total_cost(record),
            'duration_days': self._calculate_duration(record.get('reportdate'), record.get('actfinish')),
            'record_type': 'history'
        }

        return processed

    def _process_pm_schedule(self, pm: Dict[str, Any]) -> Dict[str, Any]:
        """Process and enhance PM schedule data."""
        processed = {
            'pmnum': pm.get('pmnum', ''),
            'description': pm.get('description', ''),
            'status': pm.get('status', ''),
            'frequency': pm.get('frequency', ''),
            'frequnit': pm.get('frequnit', ''),
            'nextdate': pm.get('nextdate', ''),
            'route': pm.get('route', ''),
            'jpnum': pm.get('jpnum', ''),
            'is_overdue': self._is_pm_overdue(pm.get('nextdate')),
            'days_until_due': self._calculate_days_until_due(pm.get('nextdate')),
            'record_type': 'pm_schedule'
        }

        return processed

    def _generate_analytics(self, records_data: Dict[str, Any]) -> Dict[str, Any]:
        """Generate analytics and insights from the records data."""
        analytics = {
            'summary_stats': self._calculate_summary_stats(records_data),
            'trends': self._analyze_trends(records_data),
            'patterns': self._identify_patterns(records_data),
            'performance_metrics': self._calculate_performance_metrics(records_data),
            'recommendations': self._generate_recommendations(records_data)
        }

        return analytics

    def _generate_summary(self, records_data: Dict[str, Any], analytics: Dict[str, Any]) -> Dict[str, Any]:
        """Generate a summary of the asset's related records."""
        work_orders = records_data.get('work_orders', [])
        service_requests = records_data.get('service_requests', [])
        history = records_data.get('asset_history', [])
        pm_schedules = records_data.get('pm_schedules', [])

        open_wos = [wo for wo in work_orders if wo.get('is_open', False)]
        open_srs = [sr for sr in service_requests if sr.get('is_open', False)]
        overdue_pms = [pm for pm in pm_schedules if pm.get('is_overdue', False)]

        summary = {
            'total_records': len(work_orders) + len(service_requests) + len(history) + len(pm_schedules),
            'open_work_orders': len(open_wos),
            'open_service_requests': len(open_srs),
            'completed_work_orders': len(history),
            'pm_schedules': len(pm_schedules),
            'overdue_pms': len(overdue_pms),
            'health_score': analytics.get('performance_metrics', {}).get('health_score', 0),
            'last_maintenance': self._get_last_maintenance_date(history),
            'next_pm_due': self._get_next_pm_date(pm_schedules)
        }

        return summary

    def _calculate_days_open(self, start_date: str, end_date: str = None) -> int:
        """Calculate number of days a record has been open."""
        if not start_date:
            return 0

        try:
            start = datetime.fromisoformat(start_date.replace('Z', '+00:00'))
            end = datetime.now() if not end_date else datetime.fromisoformat(end_date.replace('Z', '+00:00'))
            return (end - start.replace(tzinfo=None)).days
        except:
            return 0

    def _calculate_urgency(self, priority: str, report_date: str) -> str:
        """Calculate urgency level based on priority and age."""
        days_open = self._calculate_days_open(report_date)

        if priority in ['1', '2'] or days_open > 30:
            return 'HIGH'
        elif priority == '3' or days_open > 14:
            return 'MEDIUM'
        else:
            return 'LOW'

    def _calculate_total_cost(self, record: Dict[str, Any]) -> float:
        """Calculate total cost from labor, material, and tool costs."""
        labor_cost = float(record.get('actlabcost', 0) or 0)
        material_cost = float(record.get('actmatcost', 0) or 0)
        tool_cost = float(record.get('acttoolcost', 0) or 0)

        return labor_cost + material_cost + tool_cost

    def _calculate_duration(self, start_date: str, end_date: str) -> int:
        """Calculate duration between two dates."""
        if not start_date or not end_date:
            return 0

        try:
            start = datetime.fromisoformat(start_date.replace('Z', '+00:00'))
            end = datetime.fromisoformat(end_date.replace('Z', '+00:00'))
            return (end - start).days
        except:
            return 0

    def _is_pm_overdue(self, next_date: str) -> bool:
        """Check if PM schedule is overdue."""
        if not next_date:
            return False

        try:
            next_due = datetime.fromisoformat(next_date.replace('Z', '+00:00'))
            return next_due.replace(tzinfo=None) < datetime.now()
        except:
            return False

    def _calculate_days_until_due(self, next_date: str) -> int:
        """Calculate days until PM is due."""
        if not next_date:
            return 999

        try:
            next_due = datetime.fromisoformat(next_date.replace('Z', '+00:00'))
            return (next_due.replace(tzinfo=None) - datetime.now()).days
        except:
            return 999

    def _calculate_summary_stats(self, records_data: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate summary statistics."""
        work_orders = records_data.get('work_orders', [])

        stats = {
            'total_work_orders': len(work_orders),
            'open_work_orders': len([wo for wo in work_orders if wo.get('is_open', False)]),
            'avg_resolution_time': self._calculate_avg_resolution_time(work_orders),
            'most_common_work_type': self._get_most_common_work_type(work_orders)
        }

        return stats

    def _analyze_trends(self, records_data: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze trends in the data."""
        work_orders = records_data.get('work_orders', [])

        # Analyze work order trends over time
        monthly_counts = defaultdict(int)
        for wo in work_orders:
            if wo.get('reportdate'):
                try:
                    date = datetime.fromisoformat(wo['reportdate'].replace('Z', '+00:00'))
                    month_key = date.strftime('%Y-%m')
                    monthly_counts[month_key] += 1
                except:
                    continue

        # Simple trend analysis
        recent_months = sorted(monthly_counts.keys())[-3:]  # Last 3 months
        if len(recent_months) >= 2:
            trend = 'increasing' if monthly_counts[recent_months[-1]] > monthly_counts[recent_months[0]] else 'decreasing'
        else:
            trend = 'stable'

        return {
            'work_order_trend': trend,
            'monthly_counts': dict(monthly_counts),
            'cost_trend': 'stable',  # Placeholder
            'frequency_trend': 'normal'  # Placeholder
        }

    def _identify_patterns(self, records_data: Dict[str, Any]) -> Dict[str, Any]:
        """Identify patterns in maintenance data."""
        work_orders = records_data.get('work_orders', [])

        # Identify recurring issues by description keywords
        description_words = defaultdict(int)
        for wo in work_orders:
            description = wo.get('description', '').lower()
            words = description.split()
            for word in words:
                if len(word) > 3:  # Only consider words longer than 3 characters
                    description_words[word] += 1

        # Find most common words (potential recurring issues)
        common_words = sorted(description_words.items(), key=lambda x: x[1], reverse=True)[:5]
        recurring_issues = [word for word, count in common_words if count > 1]

        return {
            'recurring_issues': recurring_issues,
            'seasonal_patterns': [],  # Placeholder
            'failure_modes': []  # Placeholder
        }

    def _calculate_performance_metrics(self, records_data: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate asset performance metrics."""
        work_orders = records_data.get('work_orders', [])
        history = records_data.get('asset_history', [])
        pm_schedules = records_data.get('pm_schedules', [])

        # Calculate health score based on various factors
        open_count = len([wo for wo in work_orders if wo.get('is_open', False)])
        total_count = len(work_orders) + len(history)
        overdue_pms = len([pm for pm in pm_schedules if pm.get('is_overdue', False)])

        # Health score calculation (0-100)
        health_score = 100
        health_score -= min(open_count * 10, 50)  # Reduce for open work orders
        health_score -= min(overdue_pms * 15, 30)  # Reduce for overdue PMs
        health_score = max(health_score, 0)

        # Reliability score based on work order frequency
        reliability_score = max(0, 100 - (total_count * 2))

        # Availability score (placeholder - would need downtime data)
        availability_score = 95 - (open_count * 5)

        return {
            'health_score': health_score,
            'reliability_score': max(reliability_score, 0),
            'availability_score': max(availability_score, 0)
        }

    def _generate_recommendations(self, records_data: Dict[str, Any]) -> List[str]:
        """Generate maintenance recommendations."""
        recommendations = []

        work_orders = records_data.get('work_orders', [])
        pm_schedules = records_data.get('pm_schedules', [])

        open_wos = [wo for wo in work_orders if wo.get('is_open', False)]
        overdue_pms = [pm for pm in pm_schedules if pm.get('is_overdue', False)]
        high_priority_wos = [wo for wo in open_wos if wo.get('priority') in ['1', '2']]

        if len(open_wos) > 5:
            recommendations.append("High number of open work orders - consider prioritizing maintenance resources")

        if high_priority_wos:
            recommendations.append(f"{len(high_priority_wos)} high priority work order(s) require immediate attention")

        if overdue_pms:
            recommendations.append(f"{len(overdue_pms)} PM schedule(s) overdue - schedule preventive maintenance")

        if len(open_wos) == 0 and len(overdue_pms) == 0:
            recommendations.append("Asset maintenance is up to date - continue regular monitoring")

        return recommendations

    def _calculate_avg_resolution_time(self, work_orders: List[Dict[str, Any]]) -> float:
        """Calculate average resolution time for completed work orders."""
        completed_wos = [wo for wo in work_orders if wo.get('actfinish') and wo.get('reportdate')]

        if not completed_wos:
            return 0.0

        total_days = 0
        for wo in completed_wos:
            duration = self._calculate_duration(wo.get('reportdate'), wo.get('actfinish'))
            total_days += duration

        return total_days / len(completed_wos) if completed_wos else 0.0

    def _get_most_common_work_type(self, work_orders: List[Dict[str, Any]]) -> str:
        """Get the most common work type."""
        work_types = [wo.get('worktype', '') for wo in work_orders if wo.get('worktype')]

        if not work_types:
            return 'N/A'

        from collections import Counter
        return Counter(work_types).most_common(1)[0][0]

    def _get_last_maintenance_date(self, history: List[Dict[str, Any]]) -> str:
        """Get the date of last maintenance."""
        if not history:
            return 'N/A'

        # Find the most recent completion date
        completion_dates = [h.get('actfinish') for h in history if h.get('actfinish')]

        if not completion_dates:
            return 'N/A'

        return max(completion_dates)

    def _get_next_pm_date(self, pm_schedules: List[Dict[str, Any]]) -> str:
        """Get the next PM due date."""
        if not pm_schedules:
            return 'N/A'

        # Find the earliest next date
        next_dates = [pm.get('nextdate') for pm in pm_schedules if pm.get('nextdate')]

        if not next_dates:
            return 'N/A'

        return min(next_dates)

    def _is_cache_valid(self, cache_key: str) -> bool:
        """Check if cached data is still valid."""
        if cache_key not in self.cache:
            return False

        cache_time = self.cache[cache_key]['timestamp']
        return (time.time() - cache_time) < self.cache_ttl

    def _cache_data(self, cache_key: str, data: Dict[str, Any]) -> None:
        """Cache data with timestamp."""
        self.cache[cache_key] = {
            'data': data,
            'timestamp': time.time()
        }
