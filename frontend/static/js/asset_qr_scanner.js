/**
 * Asset QR Code Scanner JavaScript
 * Handles QR code scanning and asset operations
 */

console.log('🔍 ASSET QR SCANNER: Loading asset QR scanner JavaScript...');

class AssetQRScanner {
    constructor() {
        console.log('🔍 ASSET QR SCANNER: Creating AssetQRScanner instance');
        this.currentAssetData = null;
        this.selectedOperation = null;
        this.videoElement = null;
        this.canvasElement = null;
        this.canvasContext = null;
        this.cameraActive = false;
        this.currentCameraId = null;
        this.availableCameras = [];
        this.scanningInterval = null;
        this.currentStream = null;
        this.scanCount = 0;
        this.lastScanTime = 0;
        this.init();
    }

    init() {
        console.log('🔍 ASSET QR SCANNER: Initializing asset QR scanner');

        // Set up event listeners
        this.setupEventListeners();

        // Wait for QR scanner library to load
        this.waitForQRLibrary();

        // Focus on QR input
        setTimeout(() => {
            const input = document.getElementById('qrCodeInput');
            if (input) {
                input.focus();
                console.log('✅ ASSET QR SCANNER: Input field focused');
            } else {
                console.error('❌ ASSET QR SCANNER: Input field not found');
            }
        }, 100);
    }

    waitForQRLibrary() {
        console.log('🔄 ASSET QR SCANNER: Starting to wait for jsQR library...');
        
        // Check if jsQR library is loaded
        let attempts = 0;
        const maxAttempts = 40; // 20 seconds max wait

        const checkLibrary = () => {
            attempts++;
            console.log(`🔍 ASSET QR SCANNER: Check attempt ${attempts}/${maxAttempts} - typeof jsQR =`, typeof jsQR);

            if (typeof jsQR !== 'undefined') {
                console.log('✅ ASSET QR SCANNER: jsQR library loaded successfully');
                this.initializeCameraElements();
                // Enable camera button
                const cameraBtn = document.getElementById('cameraToggleBtn');
                if (cameraBtn) {
                    cameraBtn.disabled = false;
                    cameraBtn.innerHTML = '<i class="fas fa-camera me-1"></i>Start Camera Scanner';
                    cameraBtn.classList.remove('btn-outline-secondary', 'btn-outline-warning');
                    cameraBtn.classList.add('btn-outline-primary');
                }

                // Hide force enable button since library loaded successfully
                const forceBtn = document.getElementById('forceEnableBtn');
                if (forceBtn) {
                    forceBtn.style.display = 'none';
                }

            } else if (attempts < maxAttempts) {
                console.log(`⏳ ASSET QR SCANNER: Waiting for jsQR library... (${attempts}/${maxAttempts})`);
                setTimeout(checkLibrary, 500);
            } else {
                console.error('❌ ASSET QR SCANNER: jsQR library failed to load after 20 seconds');
                
                // Enable button anyway for manual testing
                const cameraBtn = document.getElementById('cameraToggleBtn');
                const forceBtn = document.getElementById('forceEnableBtn');
                if (cameraBtn) {
                    cameraBtn.disabled = false;
                    cameraBtn.innerHTML = '<i class="fas fa-exclamation-triangle me-1"></i>Library Failed - Try Anyway';
                    cameraBtn.classList.remove('btn-outline-primary');
                    cameraBtn.classList.add('btn-outline-warning');
                }
                if (forceBtn) {
                    forceBtn.style.display = 'inline-block';
                }

                // Show error message to user
                this.showError('QR scanning library failed to load. Please refresh the page.');
            }
        };

        // Also listen for the jsQRLoaded event
        window.addEventListener('jsQRLoaded', () => {
            console.log('✅ ASSET QR SCANNER: jsQR library loaded via event');
            if (typeof jsQR !== 'undefined') {
                this.initializeCameraElements();
            }
        });

        // Start checking
        checkLibrary();
    }

    setupEventListeners() {
        // Enter key processing
        document.getElementById('qrCodeInput').addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.processQRCode();
            }
        });

        // Auto-process when QR data is pasted
        document.getElementById('qrCodeInput').addEventListener('paste', () => {
            setTimeout(() => {
                this.processQRCode();
            }, 100);
        });
    }

    async processQRCode() {
        console.log('🔍 ASSET QR SCANNER: processQRCode called');

        const qrInput = document.getElementById('qrCodeInput');
        if (!qrInput) {
            console.error('❌ ASSET QR SCANNER: QR input field not found');
            alert('QR input field not found');
            return;
        }

        const qrContent = qrInput.value.trim();
        console.log('🔍 ASSET QR SCANNER: QR content length:', qrContent.length);

        if (!qrContent) {
            console.log('❌ ASSET QR SCANNER: No QR content provided');
            alert('Please enter QR code content');
            return;
        }

        console.log('🔍 ASSET QR SCANNER: Processing QR content:', qrContent.substring(0, 100) + '...');
        this.showLoading('Processing asset QR code...');

        try {
            console.log('🔍 ASSET QR SCANNER: Calling decode API...');

            // Decode asset QR code
            const response = await fetch('/api/asset/decode-qr', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ qr_content: qrContent })
            });

            console.log('🔍 ASSET QR SCANNER: API response status:', response.status);
            const result = await response.json();
            console.log('🔍 ASSET QR SCANNER: API result:', result);

            if (result.success) {
                console.log('✅ ASSET QR SCANNER: QR decoded successfully');
                this.currentAssetData = result.asset_data;
                this.displayAssetInfo(this.currentAssetData);
                this.showAssetOperations();
                this.hideLoading();
            } else {
                console.error('❌ ASSET QR SCANNER: Decode failed:', result.error);
                this.hideLoading();
                this.showError(`Failed to decode asset QR code: ${result.error}`);
            }

        } catch (error) {
            console.error('❌ ASSET QR SCANNER: Exception:', error);
            this.hideLoading();
            this.showError('Error processing QR code: ' + error.message);
        }
    }

    displayAssetInfo(assetData) {
        console.log('🎨 QR SCANNER: Displaying asset info with magical layout for:', assetData.assetnum);

        // Store asset data for related records
        this.currentAssetData = assetData;

        const infoSection = document.getElementById('assetInfoSection');
        const infoCard = document.getElementById('assetInfoCard');

        // Format the asset information with beautiful grid layout
        const generatedDate = assetData.generated_at ?
            new Date(assetData.generated_at).toLocaleString() : 'Unknown';

        const assetInfoHTML = `
            <div class="qr-related-records">
                <!-- Asset Header -->
                <div class="records-card analytics full-width animate-in mb-4" style="animation-delay: 0.1s">
                    <div class="records-card-header">
                        <h5 class="records-card-title">
                            <span><i class="fas fa-cogs records-card-icon"></i>Asset: ${assetData.assetnum || 'Unknown'}</span>
                            <span class="badge bg-primary">${assetData.status || 'N/A'}</span>
                        </h5>
                    </div>
                    <div class="records-card-content">
                        <div class="row">
                            <div class="col-md-6">
                                <p class="mb-2"><strong>Description:</strong> ${assetData.description || 'N/A'}</p>
                                <p class="mb-2"><strong>Site:</strong> ${assetData.siteid || 'N/A'}</p>
                                <p class="mb-2"><strong>Location:</strong> ${assetData.location || 'N/A'}</p>
                                <p class="mb-2"><strong>Asset Tag:</strong> ${assetData.assettag || 'N/A'}</p>
                            </div>
                            <div class="col-md-6">
                                <p class="mb-2"><strong>Model:</strong> ${assetData.model || 'N/A'}</p>
                                <p class="mb-2"><strong>Type:</strong> ${assetData.assettype || 'N/A'}</p>
                                <p class="mb-2"><strong>Manufacturer:</strong> ${assetData.manufacturer || 'N/A'}</p>
                                <p class="mb-2"><strong>Serial Number:</strong> ${assetData.serialnum || 'N/A'}</p>
                            </div>
                        </div>
                        <div class="row mt-2">
                            <div class="col-12">
                                <small class="text-muted">
                                    <i class="fas fa-qrcode me-1"></i>QR Code Generated: ${generatedDate}
                                </small>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Asset Overview Loading Placeholder -->
                <div class="records-card analytics full-width animate-in" style="animation-delay: 0.2s" id="assetOverviewCard">
                    <div class="records-card-header" data-toggle="collapse">
                        <h5 class="records-card-title">
                            <span><i class="fas fa-chart-line records-card-icon"></i>Asset Overview</span>
                            <span class="collapse-indicator">▼</span>
                        </h5>
                    </div>
                    <div class="records-card-content" id="assetOverviewContent">
                        <div class="row">
                            <div class="col-md-3 col-6 mb-3">
                                <div class="text-center">
                                    <div class="skeleton skeleton-text short"></div>
                                    <small class="text-muted">Health Score</small>
                                </div>
                            </div>
                            <div class="col-md-3 col-6 mb-3">
                                <div class="text-center">
                                    <div class="skeleton skeleton-text short"></div>
                                    <small class="text-muted">Open Work Orders</small>
                                </div>
                            </div>
                            <div class="col-md-3 col-6 mb-3">
                                <div class="text-center">
                                    <div class="skeleton skeleton-text short"></div>
                                    <small class="text-muted">Open Service Requests</small>
                                </div>
                            </div>
                            <div class="col-md-3 col-6 mb-3">
                                <div class="text-center">
                                    <div class="skeleton skeleton-text short"></div>
                                    <small class="text-muted">Overdue PMs</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Related Records Grid Container -->
                <div class="records-grid" id="relatedRecordsGrid" style="display: grid !important; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)) !important; gap: 1.5rem !important; width: 100% !important;">
                    <!-- Work Orders Card -->
                    <div class="records-card work-orders animate-in" style="animation-delay: 0.3s" id="workOrdersCard">
                        <div class="records-card-header" data-toggle="collapse">
                            <h5 class="records-card-title">
                                <span><i class="fas fa-wrench records-card-icon"></i>Work Orders</span>
                                <span class="records-card-count skeleton" style="width: 30px; height: 24px;"></span>
                                <span class="collapse-indicator">▼</span>
                            </h5>
                        </div>
                        <div class="records-card-content">
                            <div class="record-item">
                                <div class="skeleton skeleton-text medium"></div>
                                <div class="skeleton skeleton-text long"></div>
                                <div class="skeleton skeleton-text short"></div>
                            </div>
                        </div>
                    </div>

                    <!-- Service Requests Card -->
                    <div class="records-card service-requests animate-in" style="animation-delay: 0.4s" id="serviceRequestsCard">
                        <div class="records-card-header" data-toggle="collapse">
                            <h5 class="records-card-title">
                                <span><i class="fas fa-ticket-alt records-card-icon"></i>Service Requests</span>
                                <span class="records-card-count skeleton" style="width: 30px; height: 24px;"></span>
                                <span class="collapse-indicator">▼</span>
                            </h5>
                        </div>
                        <div class="records-card-content">
                            <div class="record-item">
                                <div class="skeleton skeleton-text medium"></div>
                                <div class="skeleton skeleton-text long"></div>
                                <div class="skeleton skeleton-text short"></div>
                            </div>
                        </div>
                    </div>

                    <!-- Asset History Card -->
                    <div class="records-card asset-history animate-in" style="animation-delay: 0.5s" id="assetHistoryCard">
                        <div class="records-card-header" data-toggle="collapse">
                            <h5 class="records-card-title">
                                <span><i class="fas fa-history records-card-icon"></i>Maintenance History</span>
                                <span class="records-card-count skeleton" style="width: 30px; height: 24px;"></span>
                                <span class="collapse-indicator">▼</span>
                            </h5>
                        </div>
                        <div class="records-card-content">
                            <div class="record-item">
                                <div class="skeleton skeleton-text medium"></div>
                                <div class="skeleton skeleton-text long"></div>
                                <div class="skeleton skeleton-text short"></div>
                            </div>
                        </div>
                    </div>

                    <!-- PM Schedules Card -->
                    <div class="records-card pm-schedules animate-in" style="animation-delay: 0.6s" id="pmSchedulesCard">
                        <div class="records-card-header" data-toggle="collapse">
                            <h5 class="records-card-title">
                                <span><i class="fas fa-calendar-check records-card-icon"></i>PM Schedules</span>
                                <span class="records-card-count skeleton" style="width: 30px; height: 24px;"></span>
                                <span class="collapse-indicator">▼</span>
                            </h5>
                        </div>
                        <div class="records-card-content">
                            <div class="record-item">
                                <div class="skeleton skeleton-text medium"></div>
                                <div class="skeleton skeleton-text long"></div>
                                <div class="skeleton skeleton-text short"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;

        infoCard.innerHTML = assetInfoHTML;
        infoSection.style.display = 'block';

        // Force grid layout and add event listeners
        setTimeout(() => {
            const grid = document.getElementById('relatedRecordsGrid');
            if (grid) {
                grid.style.display = 'grid';
                grid.style.gridTemplateColumns = 'repeat(auto-fit, minmax(300px, 1fr))';
                grid.style.gap = '1.5rem';
                grid.style.width = '100%';
                console.log('🎨 QR SCANNER: Grid layout forced via JavaScript');
            }

            // Add event listeners for collapse functionality
            this.addCollapseEventListeners();

            // Add event listeners for action buttons
            this.addActionEventListeners();
        }, 100);

        // Load related records immediately
        this.loadRelatedRecords(assetData);
    }

    showAssetOperations() {
        const operationsSection = document.getElementById('assetOperations');
        operationsSection.style.display = 'block';
    }

    // Handle successful work order/service request creation
    handleOperationSuccess(operationType, recordNumber) {
        const resultsSection = document.getElementById('operationResults');
        const resultsContent = document.getElementById('resultsContent');

        let message = '';
        let icon = '';

        if (operationType === 'workorder') {
            message = `Work Order ${recordNumber} created successfully for asset ${this.currentAssetData.assetnum}`;
            icon = 'fas fa-wrench';
        } else if (operationType === 'servicerequest') {
            message = `Service Request ${recordNumber} created successfully for asset ${this.currentAssetData.assetnum}`;
            icon = 'fas fa-headset';
        }

        resultsContent.innerHTML = `
            <div class="alert alert-success">
                <i class="${icon} me-2"></i>${message}
                <div class="mt-2">
                    <button class="btn btn-sm btn-primary me-2" data-action="view-record" data-type="${operationType}" data-number="${recordNumber}">
                        <i class="fas fa-eye me-1"></i>View Record
                    </button>
                    <button class="btn btn-sm btn-secondary" data-action="clear-scanner">
                        <i class="fas fa-plus me-1"></i>Scan Another Asset
                    </button>
                </div>
            </div>
        `;

        resultsSection.style.display = 'block';
        this.showSuccess(message);
    }

    showLoading(message = 'Loading...') {
        const overlay = document.getElementById('loadingOverlay');
        const text = overlay.querySelector('.loading-text');
        text.textContent = message;
        overlay.style.display = 'flex';
    }

    hideLoading() {
        const overlay = document.getElementById('loadingOverlay');
        overlay.style.display = 'none';
    }

    showError(message) {
        alert('Error: ' + message);
        console.error('❌ ASSET QR SCANNER:', message);
    }

    showSuccess(message) {
        // Create a success toast or alert
        const alertDiv = document.createElement('div');
        alertDiv.className = 'alert alert-success alert-dismissible fade show position-fixed';
        alertDiv.style.top = '20px';
        alertDiv.style.right = '20px';
        alertDiv.style.zIndex = '9999';
        alertDiv.innerHTML = `
            <i class="fas fa-check-circle me-2"></i>${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        document.body.appendChild(alertDiv);

        // Auto-remove after 5 seconds
        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.parentNode.removeChild(alertDiv);
            }
        }, 5000);
    }

    // Camera-related methods
    initializeCameraElements() {
        this.videoElement = document.getElementById('qrVideo');
        this.canvasElement = document.getElementById('qrCanvas');
        if (this.canvasElement) {
            this.canvasContext = this.canvasElement.getContext('2d');
        }
        console.log('✅ ASSET QR SCANNER: Camera elements initialized');
    }

    async toggleCamera() {
        if (this.cameraActive) {
            this.stopCamera();
        } else {
            await this.startCamera();
        }
    }

    async startCamera() {
        try {
            console.log('📷 Starting camera...');

            const constraints = {
                video: {
                    facingMode: 'environment', // Prefer back camera
                    width: { ideal: 640 },
                    height: { ideal: 480 }
                }
            };

            this.currentStream = await navigator.mediaDevices.getUserMedia(constraints);
            this.videoElement.srcObject = this.currentStream;

            // Show camera container
            document.getElementById('cameraContainer').style.display = 'block';

            // Update button
            const btn = document.getElementById('cameraToggleBtn');
            btn.innerHTML = '<i class="fas fa-stop me-1"></i>Stop Camera';
            btn.classList.remove('btn-outline-primary');
            btn.classList.add('btn-outline-danger');

            // Show switch camera button
            document.getElementById('cameraSwitchBtn').style.display = 'inline-block';

            this.cameraActive = true;

            // Start scanning
            this.startScanning();

            console.log('✅ Camera started successfully');

        } catch (error) {
            console.error('❌ Camera error:', error);
            this.showError('Camera access failed: ' + error.message);
        }
    }

    stopCamera() {
        console.log('📷 Stopping camera...');

        if (this.currentStream) {
            this.currentStream.getTracks().forEach(track => track.stop());
            this.currentStream = null;
        }

        if (this.scanningInterval) {
            clearInterval(this.scanningInterval);
            this.scanningInterval = null;
        }

        // Hide camera container
        document.getElementById('cameraContainer').style.display = 'none';

        // Update button
        const btn = document.getElementById('cameraToggleBtn');
        btn.innerHTML = '<i class="fas fa-camera me-1"></i>Start Camera';
        btn.classList.remove('btn-outline-danger');
        btn.classList.add('btn-outline-primary');

        // Hide switch camera button
        document.getElementById('cameraSwitchBtn').style.display = 'none';

        this.cameraActive = false;

        console.log('✅ Camera stopped');
    }

    startScanning() {
        if (!this.videoElement || !this.canvasElement || typeof jsQR === 'undefined') {
            console.log('❌ Cannot start scanning - missing elements or jsQR');
            return;
        }

        this.scanningInterval = setInterval(() => {
            this.scanForQRCode();
        }, 500); // Scan every 500ms
    }

    scanForQRCode() {
        if (!this.videoElement.videoWidth || !this.videoElement.videoHeight) {
            return;
        }

        // Set canvas size to video size
        this.canvasElement.width = this.videoElement.videoWidth;
        this.canvasElement.height = this.videoElement.videoHeight;

        // Draw video frame to canvas
        this.canvasContext.drawImage(this.videoElement, 0, 0);

        // Get image data
        const imageData = this.canvasContext.getImageData(0, 0, this.canvasElement.width, this.canvasElement.height);

        // Try to decode QR code
        const code = jsQR(imageData.data, imageData.width, imageData.height);

        if (code) {
            console.log('✅ QR code detected:', code.data);

            // Put the QR content in the input field
            document.getElementById('qrCodeInput').value = code.data;

            // Stop camera
            this.stopCamera();

            // Process the QR code
            this.processQRCode();
        }
    }

    async switchCamera() {
        if (!this.cameraActive) {
            return;
        }

        try {
            // Stop current camera
            this.stopCamera();

            // Try to start with different facing mode
            const constraints = {
                video: {
                    facingMode: this.currentCameraId === 'environment' ? 'user' : 'environment',
                    width: { ideal: 640 },
                    height: { ideal: 480 }
                }
            };

            await this.startCamera();

        } catch (error) {
            console.error('❌ Camera switch error:', error);
            this.showError('Failed to switch camera: ' + error.message);
        }
    }

    forceEnableCamera() {
        // Force enable camera button
        const btn = document.getElementById('cameraToggleBtn');
        btn.disabled = false;
        btn.innerHTML = '<i class="fas fa-camera me-1"></i>Start Camera (Forced)';
        btn.classList.remove('btn-outline-warning');
        btn.classList.add('btn-outline-primary');
    }

    captureManually() {
        if (this.cameraActive && this.videoElement) {
            this.scanForQRCode();
        }
    }

    // Work Order Creation Methods
    async showCreateWorkOrderModal(assetData) {
        console.log('🔧 QR SCANNER: Showing work order creation modal for asset:', assetData.assetnum);

        const modalBody = document.getElementById('createWorkOrderModalBody');
        modalBody.innerHTML = this.createWorkOrderForm(assetData);

        const modal = new bootstrap.Modal(document.getElementById('createWorkOrderModal'));
        modal.show();

        // Populate the Created By field with current user's personid
        try {
            const response = await fetch('/api/current-user');
            const userInfo = await response.json();
            if (userInfo.success) {
                const createdByField = document.getElementById('woCreatedBy');
                if (createdByField) {
                    createdByField.value = userInfo.personid || userInfo.username;
                    createdByField.placeholder = `${userInfo.displayname || userInfo.username} (${userInfo.personid || userInfo.username})`;
                }
            }
        } catch (error) {
            console.warn('Could not fetch user info for Created By field:', error);
        }

        // Add form submission handler
        const form = document.getElementById('createWorkOrderForm');
        if (form) {
            form.addEventListener('submit', (e) => {
                e.preventDefault();
                this.submitCreateWorkOrder(assetData);
            });
        }
    }

    createWorkOrderForm(assetData) {
        return `
            <form id="createWorkOrderForm">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    Creating work order for scanned asset: <strong>${assetData.assetnum}</strong> (${assetData.siteid})
                </div>

                <div class="row">
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label for="woAssetNum" class="form-label">Asset Number</label>
                            <input type="text" class="form-control" id="woAssetNum" value="${assetData.assetnum}" readonly>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label for="woSiteId" class="form-label">Site ID</label>
                            <input type="text" class="form-control" id="woSiteId" value="${assetData.siteid}" readonly>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label for="woOrgId" class="form-label">Organization</label>
                            <input type="text" class="form-control" id="woOrgId" value="${assetData.orgid || 'VECTRUS'}" readonly>
                        </div>
                    </div>
                </div>

                <div class="mb-3">
                    <label for="woDescription" class="form-label">Work Order Description *</label>
                    <input type="text" class="form-control" id="woDescription" placeholder="Brief description of the work to be performed" required>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="woWorkType" class="form-label">Work Type</label>
                            <select class="form-select" id="woWorkType">
                                <option value="">Select work type</option>
                                <option value="CM">Corrective Maintenance</option>
                                <option value="PM">Preventive Maintenance</option>
                                <option value="EM">Emergency Maintenance</option>
                                <option value="IN">Inspection</option>
                                <option value="CAL">Calibration</option>
                                <option value="REP">Repair</option>
                                <option value="INST">Installation</option>
                                <option value="MOD">Modification</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="woPriority" class="form-label">Priority</label>
                            <select class="form-select" id="woPriority">
                                <option value="1">1 - Emergency</option>
                                <option value="2">2 - High</option>
                                <option value="3" selected>3 - Normal</option>
                                <option value="4">4 - Low</option>
                                <option value="5">5 - Planning</option>
                            </select>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="woTargetStartDate" class="form-label">Target Start Date</label>
                            <input type="datetime-local" class="form-control" id="woTargetStartDate">
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="woCreatedBy" class="form-label">Created By</label>
                            <input type="text" class="form-control" id="woCreatedBy" placeholder="Your name or ID">
                        </div>
                    </div>
                </div>

                <div class="mb-3">
                    <label for="woLongDescription" class="form-label">Detailed Description</label>
                    <textarea class="form-control" id="woLongDescription" rows="4" placeholder="Provide detailed information about the work to be performed, including any specific requirements, safety considerations, or special instructions..."></textarea>
                </div>

                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-warning">
                        <i class="fas fa-wrench me-1"></i>Create Work Order
                    </button>
                </div>
            </form>
        `;
    }

    async submitCreateWorkOrder(assetData) {
        try {
            console.log('🔧 QR SCANNER: Submitting work order creation...');

            // Get form data
            const formData = {
                assetnum: document.getElementById('woAssetNum').value,
                siteid: document.getElementById('woSiteId').value,
                orgid: document.getElementById('woOrgId').value,
                description: document.getElementById('woDescription').value,
                worktype: document.getElementById('woWorkType').value,
                priority: document.getElementById('woPriority').value,
                longdescription: document.getElementById('woLongDescription').value,
                targstartdate: document.getElementById('woTargetStartDate').value,
                createdby: document.getElementById('woCreatedBy').value
            };

            // Validate required fields
            if (!formData.description) {
                this.showError('Description is required');
                return;
            }

            // Show loading state for AI analysis
            const submitBtn = document.querySelector('#createWorkOrderForm button[type="submit"]');
            const originalText = submitBtn.innerHTML;
            submitBtn.innerHTML = '<i class="fas fa-brain fa-spin me-1"></i>AI Analysis...';
            submitBtn.disabled = true;

            try {
                // Step 1: Perform AI Analysis
                console.log('🤖 QR SCANNER: Starting AI analysis...');
                const analysisResult = await this.performAIAnalysis(assetData, formData);

                if (analysisResult.success) {
                    console.log('✅ QR SCANNER: AI analysis successful');

                    // Don't close the work order creation modal yet - keep it open until final success
                    // The modal will be closed after the user proceeds with creation and gets success response

                    // Show AI insights modal
                    this.showAIInsightsModal(analysisResult, assetData, formData);
                } else {
                    // If AI analysis fails, proceed with normal creation
                    console.warn('⚠️ QR SCANNER: AI analysis failed, proceeding with normal creation:', analysisResult.error);
                    this.showSuccess('AI analysis unavailable. Proceeding with normal creation.');

                    // Change button text to normal creation
                    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Creating...';

                    // Proceed with direct creation - but keep modal open until success
                    await this.createWorkOrderWithoutAI(formData);

                    // Modal will be closed by createWorkOrderWithoutAI after success
                }
            } catch (analysisError) {
                console.error('❌ QR SCANNER: AI analysis error:', analysisError);
                this.showError('AI analysis failed. Proceeding with normal creation.');

                // Change button text to normal creation
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Creating...';

                // Proceed with direct creation - but keep modal open until success
                await this.createWorkOrderWithoutAI(formData);

                // Modal will be closed by createWorkOrderWithoutAI after success
            }

        } catch (error) {
            console.error('❌ QR SCANNER: Work order submission error:', error);
            this.showError('Failed to create work order: ' + error.message);

            // Reset button
            const submitBtn = document.querySelector('#createWorkOrderForm button[type="submit"]');
            if (submitBtn) {
                submitBtn.innerHTML = '<i class="fas fa-wrench me-1"></i>Create Work Order';
                submitBtn.disabled = false;
            }
        }
    }

    async performAIAnalysis(assetData, formData) {
        try {
            console.log('🤖 QR SCANNER: Starting AI analysis for work order creation...');

            const response = await fetch('/api/ai-analysis/analyze-workorder', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    asset_data: {
                        assetnum: assetData.assetnum,
                        siteid: assetData.siteid,
                        description: assetData.description || ''
                    },
                    user_description: formData.description + (formData.longdescription ? ' ' + formData.longdescription : ''),
                    form_data: formData
                })
            });

            if (!response.ok) {
                throw new Error(`AI analysis request failed: ${response.status}`);
            }

            const result = await response.json();
            console.log('🤖 QR SCANNER: AI analysis completed:', result);

            return result;

        } catch (error) {
            console.error('❌ QR SCANNER: AI analysis error:', error);
            return {
                success: false,
                error: error.message
            };
        }
    }

    async createWorkOrderWithoutAI(formData) {
        try {
            console.log('🔧 QR SCANNER: Creating work order without AI...');

            this.showSuccess('Creating work order...');

            const response = await fetch('/api/asset/create-workorder', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(formData)
            });

            console.log('🔧 QR SCANNER: API response status:', response.status);
            const result = await response.json();
            console.log('🔧 QR SCANNER: API response data:', result);

            if (result.success) {
                console.log('🎉 QR SCANNER: Work order created successfully!');
                console.log('🎉 QR SCANNER: Work order number:', result.wonum);

                // NOW close the work order creation modal AFTER successful API response
                const modalElement = document.getElementById('createWorkOrderModal');
                if (modalElement) {
                    const modal = bootstrap.Modal.getInstance(modalElement) || new bootstrap.Modal(modalElement);
                    modal.hide();
                    console.log('✅ QR SCANNER: Work order modal closed AFTER successful API response');

                    // Ensure modal is fully closed
                    setTimeout(() => {
                        modalElement.style.display = 'none';
                        modalElement.classList.remove('show');
                        document.body.classList.remove('modal-open');
                        const backdrop = document.querySelector('.modal-backdrop');
                        if (backdrop) backdrop.remove();
                    }, 300);
                }

                // Show the beautiful work order creation success display
                this.showWorkOrderCreationSuccess(result);

                // Update the results section
                this.handleOperationSuccess('workorder', result.wonum);

            } else {
                console.error('❌ QR SCANNER: Work order creation failed:', result.error);
                this.showError('Failed to create work order: ' + result.error);

                // Reset button on failure and keep modal open for retry
                const submitBtn = document.querySelector('#createWorkOrderForm button[type="submit"]');
                if (submitBtn) {
                    submitBtn.innerHTML = '<i class="fas fa-wrench me-1"></i>Create Work Order';
                    submitBtn.disabled = false;
                }

                console.log('⚠️ QR SCANNER: Work order modal staying open for retry after failure');
            }

        } catch (error) {
            console.error('❌ QR SCANNER: Work order creation error:', error);
            this.showError('Error creating work order: ' + error.message);

            // Reset button on error and keep modal open for retry
            const submitBtn = document.querySelector('#createWorkOrderForm button[type="submit"]');
            if (submitBtn) {
                submitBtn.innerHTML = '<i class="fas fa-wrench me-1"></i>Create Work Order';
                submitBtn.disabled = false;
            }

            console.log('⚠️ QR SCANNER: Work order modal staying open for retry after error');
        }
    }

    showAIInsightsModal(analysisResult, assetData, formData) {
        console.log('🤖 QR SCANNER: Showing AI insights modal');

        const modalBody = document.getElementById('aiInsightsModalBody');
        modalBody.innerHTML = this.createAIInsightsModalHTML(analysisResult, assetData, formData);

        const modal = new bootstrap.Modal(document.getElementById('aiInsightsModal'));
        modal.show();

        // Set up event listeners
        this.setupAIInsightsEventListeners(analysisResult, assetData, formData);
    }

    createAIInsightsModalHTML(analysisResult, assetData, formData) {
        const kpis = analysisResult.kpis || {};
        const insights = analysisResult.insights || {};
        const duplicationAnalysis = analysisResult.duplication_analysis || {};
        const recommendations = analysisResult.recommendations || [];
        const existingWorkorders = analysisResult.existing_workorders || [];

        // Debug logging to see what data we're getting
        console.log('🔍 QR SCANNER: AI Analysis Result Structure:');
        console.log('🔍 QR SCANNER: - Full Analysis Result:', analysisResult);
        console.log('🔍 QR SCANNER: - KPIs:', kpis);
        console.log('🔍 QR SCANNER: - Insights:', insights);
        console.log('🔍 QR SCANNER: - Duplication Analysis:', duplicationAnalysis);
        console.log('🔍 QR SCANNER: - Existing Work Orders Count:', existingWorkorders.length);
        console.log('🔍 QR SCANNER: - Recommendations:', recommendations);

        // Validate critical data
        if (!kpis.total_open_workorders && existingWorkorders.length > 0) {
            console.warn('⚠️ QR SCANNER: KPIs missing total_open_workorders but we have existing work orders');
        }
        if (!insights.recent_activity) {
            console.warn('⚠️ QR SCANNER: Missing recent_activity insights');
        }
        if (!duplicationAnalysis.high_risk && !duplicationAnalysis.medium_risk) {
            console.warn('⚠️ QR SCANNER: No duplication analysis risk data');
        }

        return `
            <div class="ai-insights-container">
                <!-- AI Analysis Summary -->
                <div class="alert alert-info mb-4">
                    <div class="d-flex align-items-center">
                        <i class="fas fa-brain fa-2x me-3 text-primary"></i>
                        <div>
                            <h6 class="mb-1">AI Analysis Complete</h6>
                            <p class="mb-0">Our AI has analyzed your work order request and found ${existingWorkorders.length} similar work orders for this asset.</p>
                        </div>
                    </div>
                </div>

                <!-- Key Performance Indicators -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <i class="fas fa-tools fa-2x text-warning mb-2"></i>
                                <h5 class="card-title">${kpis.total_open_workorders !== undefined ? kpis.total_open_workorders : existingWorkorders.length}</h5>
                                <p class="card-text small">Total Work Orders</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <i class="fas fa-clock fa-2x text-info mb-2"></i>
                                <h5 class="card-title">${insights.recent_activity?.avg_days_open !== undefined ? insights.recent_activity.avg_days_open : 'N/A'}</h5>
                                <p class="card-text small">Avg Days Open</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <i class="fas fa-exclamation-triangle fa-2x text-danger mb-2"></i>
                                <h5 class="card-title">${kpis.potential_duplicates !== undefined ? kpis.potential_duplicates : 0}</h5>
                                <p class="card-text small">Potential Duplicates</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <i class="fas fa-percentage fa-2x text-info mb-2"></i>
                                <h5 class="card-title">${kpis.highest_similarity_score !== undefined ? Math.round(kpis.highest_similarity_score * 100) : 0}%</h5>
                                <p class="card-text small">Highest Similarity</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Intended Work Order -->
                <div class="row mb-4">
                    <div class="col-12">
                        <h6><i class="fas fa-plus-circle me-2"></i>Your New Work Order</h6>
                        <div class="card border-primary">
                            <div class="card-body">
                                <h6 class="card-title">New Work Order</h6>
                                <p class="card-text"><strong>Description:</strong> ${formData.description || 'No description provided'}</p>
                                ${formData.longdescription ? `<p class="card-text"><strong>Long Description:</strong> ${formData.longdescription}</p>` : ''}
                                <div class="d-flex gap-2 flex-wrap">
                                    ${formData.worktype ? `<span class="badge bg-primary">${formData.worktype}</span>` : ''}
                                    ${formData.priority ? `<span class="badge bg-info">Priority ${formData.priority}</span>` : ''}
                                    <span class="badge bg-secondary">Asset: ${assetData.assetnum}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Similar Work Orders -->
                ${existingWorkorders.length > 0 ? `
                    <div class="row mb-4">
                        <div class="col-12">
                            <h6><i class="fas fa-history me-2"></i>Similar Work Orders Found</h6>
                            <div class="table-responsive">
                                <table class="table table-sm">
                                    <thead>
                                        <tr>
                                            <th>Work Order</th>
                                            <th>Description</th>
                                            <th>Status</th>
                                            <th>Date</th>
                                            <th>Priority</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        ${existingWorkorders.slice(0, 5).map(wo => {
                                            // Format the date
                                            let formattedDate = 'N/A';
                                            if (wo.reportdate) {
                                                try {
                                                    const date = new Date(wo.reportdate);
                                                    formattedDate = date.toLocaleDateString();
                                                } catch (e) {
                                                    formattedDate = wo.reportdate.substring(0, 10);
                                                }
                                            }

                                            // Get priority badge color
                                            const priorityColor = wo.priority === '1' ? 'danger' :
                                                                wo.priority === '2' ? 'warning' :
                                                                wo.priority === '3' ? 'info' : 'secondary';

                                            return `
                                                <tr>
                                                    <td><strong>${wo.wonum || 'N/A'}</strong></td>
                                                    <td title="${wo.description || 'No description'}">${(wo.description || 'No description').substring(0, 50)}${(wo.description || '').length > 50 ? '...' : ''}</td>
                                                    <td><span class="badge bg-secondary">${wo.status || 'Unknown'}</span></td>
                                                    <td>${formattedDate}</td>
                                                    <td><span class="badge bg-${priorityColor}">${wo.priority || '3'}</span></td>
                                                </tr>
                                            `;
                                        }).join('')}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                ` : ''}

                <!-- High Risk Duplicates -->
                ${duplicationAnalysis.high_risk && duplicationAnalysis.high_risk.length > 0 ? `
                    <div class="row mb-4">
                        <div class="col-12">
                            <h6><i class="fas fa-exclamation-triangle me-2 text-danger"></i>High Risk Duplicates Detected</h6>
                            <div class="alert alert-danger">
                                <p class="mb-2"><strong>⚠️ Warning:</strong> Found ${duplicationAnalysis.high_risk.length} work order(s) with very similar descriptions:</p>
                                ${duplicationAnalysis.high_risk.slice(0, 3).map(dup => {
                                    const wo = dup.workorder;
                                    const similarity = Math.round(dup.similarity_score * 100);
                                    return `
                                        <div class="border rounded p-2 mb-2 bg-light">
                                            <div class="d-flex justify-content-between align-items-start">
                                                <div>
                                                    <strong>WO ${wo.wonum}</strong> - ${similarity}% similar
                                                    <br><small class="text-muted">${wo.description}</small>
                                                    <br><span class="badge bg-secondary">${wo.status}</span>
                                                </div>
                                                <span class="badge bg-danger">${similarity}%</span>
                                            </div>
                                            ${dup.risk_factors && dup.risk_factors.length > 0 ? `
                                                <div class="mt-1">
                                                    <small class="text-danger">Risk factors: ${dup.risk_factors.join(', ')}</small>
                                                </div>
                                            ` : ''}
                                        </div>
                                    `;
                                }).join('')}
                            </div>
                        </div>
                    </div>
                ` : ''}

                <!-- AI Recommendations -->
                ${recommendations.length > 0 ? `
                    <div class="row mb-4">
                        <div class="col-12">
                            <h6><i class="fas fa-lightbulb me-2"></i>AI Recommendations</h6>
                            <div class="alert alert-warning">
                                <ul class="mb-0">
                                    ${recommendations.map(rec => {
                                        // Handle both string and object recommendations
                                        if (typeof rec === 'string') {
                                            return `<li>${rec}</li>`;
                                        } else if (rec && rec.message) {
                                            // Display recommendation with proper formatting
                                            const typeIcon = rec.type === 'WARNING' ? 'fas fa-exclamation-triangle' :
                                                           rec.type === 'INFO' ? 'fas fa-info-circle' : 'fas fa-check-circle';
                                            return `<li><i class="${typeIcon} me-1"></i><strong>${rec.title}:</strong> ${rec.message}</li>`;
                                        } else {
                                            // Fallback for malformed objects
                                            return `<li>Recommendation data format error</li>`;
                                        }
                                    }).join('')}
                                </ul>
                            </div>
                        </div>
                    </div>
                ` : ''}

                <!-- Action Buttons -->
                <div class="row">
                    <div class="col-12">
                        <div class="d-flex gap-2 justify-content-end">
                            <button type="button" class="btn btn-outline-secondary" id="reviewExistingBtn">
                                <i class="fas fa-eye me-1"></i>Review Existing Work Orders
                            </button>
                            <button type="button" class="btn btn-warning" id="proceedWithCreationBtn">
                                <i class="fas fa-wrench me-1"></i>Proceed with Creation
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    setupAIInsightsEventListeners(analysisResult, assetData, formData) {
        // Proceed with creation button
        const proceedBtn = document.getElementById('proceedWithCreationBtn');
        if (proceedBtn) {
            proceedBtn.addEventListener('click', () => {
                this.proceedWithWorkOrderCreation(analysisResult, assetData, formData);
            });
        }

        // Review existing work orders button
        const reviewBtn = document.getElementById('reviewExistingBtn');
        if (reviewBtn) {
            reviewBtn.addEventListener('click', () => {
                console.log('🔍 QR SCANNER: Review existing work orders clicked - closing modals immediately');

                // IMMEDIATELY close the work order creation modal FIRST
                const woModalElement = document.getElementById('createWorkOrderModal');
                if (woModalElement) {
                    // Force close using Bootstrap's data-bs-dismiss approach
                    const woModal = bootstrap.Modal.getInstance(woModalElement) || new bootstrap.Modal(woModalElement);
                    woModal.hide();
                    console.log('✅ QR SCANNER: Work order creation modal closed immediately');

                    // Also trigger the hidden event manually if needed
                    setTimeout(() => {
                        woModalElement.style.display = 'none';
                        woModalElement.classList.remove('show');
                        document.body.classList.remove('modal-open');
                        const backdrop = document.querySelector('.modal-backdrop');
                        if (backdrop) backdrop.remove();
                    }, 100);
                }

                // Close the AI insights modal
                const aiModalElement = document.getElementById('aiInsightsModal');
                if (aiModalElement) {
                    const aiModal = bootstrap.Modal.getInstance(aiModalElement) || new bootstrap.Modal(aiModalElement);
                    aiModal.hide();
                    console.log('✅ QR SCANNER: AI insights modal closed');
                }

                // Check if we're on mobile
                const isMobile = window.innerWidth <= 768;

                // Open enhanced work orders page with asset filter pre-applied
                const url = `/enhanced-workorders?assetnum=${assetData.assetnum}&siteid=${assetData.siteid}&auto_search=true`;

                if (isMobile) {
                    // For mobile: navigate within the same app
                    window.location.href = url;
                } else {
                    // For desktop: open in new tab
                    window.open(url, '_blank');
                }

                // Show a helpful message
                this.showSuccess(`Opening work orders for asset ${assetData.assetnum}. Review existing work orders before creating a new one.`);
            });
        }
    }

    async proceedWithWorkOrderCreation(analysisResult, assetData, formData) {
        try {
            console.log('🔧 QR SCANNER: Proceeding with work order creation...');

            // Close ONLY the AI insights modal, KEEP work order modal open
            const aiModalElement = document.getElementById('aiInsightsModal');
            if (aiModalElement) {
                const aiModal = bootstrap.Modal.getInstance(aiModalElement) || new bootstrap.Modal(aiModalElement);
                aiModal.hide();
                console.log('✅ QR SCANNER: AI insights modal closed');
            }

            // DO NOT CLOSE the work order creation modal - keep it open during API call
            console.log('🔧 QR SCANNER: KEEPING work order modal OPEN during API call...');

            // Show loading state on the submit button
            const submitBtn = document.querySelector('#createWorkOrderForm button[type="submit"]');
            if (submitBtn) {
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Creating Work Order...';
                submitBtn.disabled = true;
                console.log('✅ QR SCANNER: Submit button set to loading state');
            }

            // Create the work order - modal will be closed AFTER success in createWorkOrderWithoutAI
            await this.createWorkOrderWithoutAI(formData);

        } catch (error) {
            console.error('❌ QR SCANNER: Error in work order creation process:', error);
            this.showError('Failed to proceed with work order creation: ' + error.message);

            // Reset button state on error
            const submitBtn = document.querySelector('#createWorkOrderForm button[type="submit"]');
            if (submitBtn) {
                submitBtn.innerHTML = '<i class="fas fa-wrench me-1"></i>Create Work Order';
                submitBtn.disabled = false;
            }
        }
    }

    handleOperationSuccess(operationType, operationNumber) {
        console.log(`🎉 QR SCANNER: ${operationType} ${operationNumber} created successfully`);

        // Update the operations section to show success
        const operationsSection = document.getElementById('assetOperations');
        if (operationsSection) {
            const successAlert = document.createElement('div');
            successAlert.className = 'alert alert-success mt-3';
            successAlert.innerHTML = `
                <div class="d-flex align-items-center">
                    <i class="fas fa-check-circle fa-2x me-3"></i>
                    <div>
                        <h6 class="mb-1">Operation Completed Successfully!</h6>
                        <p class="mb-0">${operationType === 'workorder' ? 'Work Order' : 'Service Request'} <strong>${operationNumber}</strong> has been created.</p>
                    </div>
                </div>
            `;

            // Insert at the top of the operations section
            operationsSection.insertBefore(successAlert, operationsSection.firstChild);

            // Auto-remove after 10 seconds
            setTimeout(() => {
                if (successAlert.parentNode) {
                    successAlert.remove();
                }
            }, 10000);
        }
    }

    // Beautiful Work Order Creation Success Display
    showWorkOrderCreationSuccess(result) {
        console.log('🎉 QR SCANNER: Showing beautiful work order creation success display');
        console.log('🔍 QR SCANNER: Full result data:', result);

        const data = result.data || {};

        // Enhanced work order number extraction - check all possible locations
        let wonum = result.wonum || data.wonum || data.workorder_number || data.WONUM;

        // If still no work order number, check if it's in the response message or other fields
        if (!wonum && result.message) {
            const wonumMatch = result.message.match(/(?:work order|wo)\s*#?\s*(\w+)/i);
            if (wonumMatch) {
                wonum = wonumMatch[1];
            }
        }

        // Final fallback
        if (!wonum) {
            wonum = 'Generated by Maximo';
        }

        const assetnum = result.assetnum || data.assetnum || data.ASSETNUM || 'N/A';
        const siteid = result.siteid || data.siteid || data.SITEID || 'N/A';

        // Extract the actual status from Maximo response - check multiple possible locations
        const status = result.status || data.status || data.workorder_status || data.STATUS || 'WAPPR';
        console.log('🔍 QR SCANNER: Extracted work order details:');
        console.log('🔍 QR SCANNER: - Work Order Number:', wonum);
        console.log('🔍 QR SCANNER: - Asset Number:', assetnum);
        console.log('🔍 QR SCANNER: - Site ID:', siteid);
        console.log('🔍 QR SCANNER: - Status:', status);

        // Determine if we have a real work order number or just a placeholder
        const hasRealWonum = wonum && wonum !== 'Generated by Maximo' && wonum.length > 0;

        const responseCard = `
            <div class="alert alert-success alert-dismissible fade show shadow-lg border-0" role="alert" style="position: fixed; top: 20px; right: 20px; z-index: 9999; max-width: 450px;">
                <div class="d-flex align-items-center mb-3">
                    <div class="flex-shrink-0">
                        <div class="bg-success bg-opacity-10 rounded-circle p-3">
                            <i class="fas fa-wrench text-success" style="font-size: 1.5rem;"></i>
                        </div>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <h5 class="alert-heading mb-1 text-success">
                            <i class="fas fa-check-circle me-2"></i>Work Order Created!
                        </h5>
                        <p class="mb-0 text-muted">Successfully created in Maximo</p>
                    </div>
                </div>

                <div class="row g-2 mb-3">
                    <div class="col-6">
                        <div class="bg-light rounded p-2 text-center">
                            <div class="fw-bold text-success">${hasRealWonum ? wonum : 'Generated'}</div>
                            <small class="text-muted">Work Order #</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="bg-light rounded p-2 text-center">
                            <div class="fw-bold text-primary">${assetnum}</div>
                            <small class="text-muted">Asset</small>
                        </div>
                    </div>
                </div>

                <div class="row g-2 mb-3">
                    <div class="col-6">
                        <div class="bg-light rounded p-2 text-center">
                            <div class="fw-bold text-info">${siteid}</div>
                            <small class="text-muted">Site</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="bg-light rounded p-2 text-center">
                            <div class="fw-bold text-warning">
                                <i class="fas fa-clock me-1"></i>${status}
                            </div>
                            <small class="text-muted">Status</small>
                        </div>
                    </div>
                </div>

                ${hasRealWonum ? `
                    <div class="d-grid gap-2">
                        <button type="button" class="btn btn-outline-success btn-sm" data-action="view-workorder" data-wonum="${wonum}">
                            <i class="fas fa-external-link-alt me-2"></i>View Work Order
                        </button>
                    </div>
                ` : `
                    <div class="alert alert-warning alert-sm mb-0 py-2">
                        <small>
                            <i class="fas fa-info-circle me-1"></i>
                            Work order created successfully. Number will be assigned by Maximo.
                        </small>
                    </div>
                `}

                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>

                <script>
                    // Define the function in global scope for the onclick handler
                    window.openWorkOrderDetailView = function(wonum) {
                        console.log('🔗 QR SCANNER: Opening work order detail view for:', wonum);

                        // Validate work order number
                        if (!wonum || wonum === 'Generated by Maximo' || wonum.trim() === '') {
                            console.warn('⚠️ QR SCANNER: Invalid work order number, cannot open detail view');
                            if (window.assetQRScanner) {
                                window.assetQRScanner.showError('Work order number not available yet. Please check Maximo directly.');
                            }
                            return;
                        }

                        // Check if we're on mobile
                        const isMobile = window.innerWidth <= 768;

                        try {
                            if (isMobile) {
                                // For mobile: navigate within the same app (no new tab)
                                window.location.href = '/workorder/' + encodeURIComponent(wonum);
                            } else {
                                // For desktop: open in new tab
                                const newWindow = window.open('/workorder/' + encodeURIComponent(wonum), '_blank');
                                if (!newWindow) {
                                    console.warn('⚠️ QR SCANNER: Popup blocked, trying same window navigation');
                                    window.location.href = '/workorder/' + encodeURIComponent(wonum);
                                }
                            }
                        } catch (error) {
                            console.error('❌ QR SCANNER: Error opening work order detail view:', error);
                            if (window.assetQRScanner) {
                                window.assetQRScanner.showError('Failed to open work order details: ' + error.message);
                            }
                        }
                    };
                </script>
            </div>
        `;

        // Insert the response card
        document.body.insertAdjacentHTML('beforeend', responseCard);

        // Auto-dismiss after 15 seconds (longer for important success messages)
        setTimeout(() => {
            const alertElement = document.querySelector('.alert:last-of-type');
            if (alertElement) {
                const alert = new bootstrap.Alert(alertElement);
                alert.close();
            }
        }, 15000);
    }
}

// Initialize the scanner when DOM is ready
let assetQRScanner;

function initializeScanner() {
    console.log('🚀 ASSET QR SCANNER: Initializing scanner...');
    try {
        assetQRScanner = new AssetQRScanner();
        window.assetQRScanner = assetQRScanner;
        console.log('✅ ASSET QR SCANNER: Scanner initialized successfully');
    } catch (error) {
        console.error('❌ ASSET QR SCANNER: Failed to initialize scanner:', error);
    }
}

// Try multiple initialization methods
document.addEventListener('DOMContentLoaded', initializeScanner);
window.addEventListener('load', function() {
    if (!window.assetQRScanner) {
        console.log('🔄 ASSET QR SCANNER: Retrying initialization on window load');
        initializeScanner();
    }
});

// Fallback initialization after a delay
setTimeout(function() {
    if (!window.assetQRScanner) {
        console.log('🔄 ASSET QR SCANNER: Fallback initialization');
        initializeScanner();
    }
}, 1000);

// Global functions for HTML onclick handlers
function processQRCode() {
    console.log('🔍 GLOBAL: processQRCode called');

    if (window.assetQRScanner && typeof window.assetQRScanner.processQRCode === 'function') {
        console.log('✅ GLOBAL: Calling scanner processQRCode');
        window.assetQRScanner.processQRCode();
    } else {
        console.error('❌ GLOBAL: Scanner not available, trying direct processing');

        // Fallback: try direct processing
        const qrInput = document.getElementById('qrCodeInput');
        if (qrInput && qrInput.value.trim()) {
            alert('Scanner not ready. Please refresh the page and try again.');
        } else {
            alert('Please enter QR code content');
        }
    }
}

function clearScanner() {
    if (window.assetQRScanner) {
        // Clear input
        document.getElementById('qrCodeInput').value = '';
        
        // Hide sections
        document.getElementById('assetInfoSection').style.display = 'none';
        document.getElementById('assetOperations').style.display = 'none';
        document.getElementById('operationResults').style.display = 'none';
        
        // Reset data
        window.assetQRScanner.currentAssetData = null;
        
        // Focus on input
        document.getElementById('qrCodeInput').focus();
    }
}

function createWorkOrderForAsset() {
    if (window.assetQRScanner && window.assetQRScanner.currentAssetData) {
        const assetData = window.assetQRScanner.currentAssetData;
        console.log('🔧 Creating work order for scanned asset:', assetData.assetnum);

        // Show the work order creation modal
        window.assetQRScanner.showCreateWorkOrderModal(assetData);
    }
}

function createServiceRequestForAsset() {
    if (window.assetQRScanner && window.assetQRScanner.currentAssetData) {
        const assetData = window.assetQRScanner.currentAssetData;
        console.log('🎫 Creating service request for scanned asset:', assetData.assetnum);

        // For now, redirect to asset management page with the asset pre-selected
        const url = `/asset-management?search=${assetData.assetnum}&siteid=${assetData.siteid}`;
        window.open(url, '_blank');

        // Show success message
        window.assetQRScanner.showSuccess(`Opened asset management for ${assetData.assetnum}. Use the "Create Service Request" button there.`);
    }
}

function viewAssetDetails() {
    if (window.assetQRScanner && window.assetQRScanner.currentAssetData) {
        const assetData = window.assetQRScanner.currentAssetData;
        // Open asset management page with this asset
        const url = `/asset-management?search=${assetData.assetnum}&siteid=${assetData.siteid}`;
        window.open(url, '_blank');
    }
}

// Camera functionality
function toggleCameraScanner() {
    if (window.assetQRScanner) {
        window.assetQRScanner.toggleCamera();
    }
}

function switchCamera() {
    if (window.assetQRScanner) {
        window.assetQRScanner.switchCamera();
    }
}

function forceEnableCamera() {
    if (window.assetQRScanner) {
        window.assetQRScanner.forceEnableCamera();
    }
}

function captureQRManually() {
    if (window.assetQRScanner) {
        window.assetQRScanner.captureManually();
    }
}

function handleQRImageUpload(event) {
    const file = event.target.files[0];
    if (!file) {
        return;
    }

    console.log('📁 Processing QR image upload:', file.name);

    // Check if it's an image
    if (!file.type.startsWith('image/')) {
        alert('Please select an image file');
        return;
    }

    // Show loading
    if (window.assetQRScanner) {
        window.assetQRScanner.showLoading('Reading QR code from image...');
    }

    // Create file reader
    const reader = new FileReader();
    reader.onload = function(e) {
        const img = new Image();
        img.onload = function() {
            try {
                // Create canvas to process the image
                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');

                // Set canvas size to image size
                canvas.width = img.width;
                canvas.height = img.height;

                // Draw image to canvas
                ctx.drawImage(img, 0, 0);

                // Get image data
                const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);

                // Try to decode QR code
                if (typeof jsQR !== 'undefined') {
                    const code = jsQR(imageData.data, imageData.width, imageData.height);

                    if (code) {
                        console.log('✅ QR code found in image:', code.data);

                        // Put the QR content in the input field
                        document.getElementById('qrCodeInput').value = code.data;

                        // Process it automatically
                        if (window.assetQRScanner) {
                            window.assetQRScanner.hideLoading();
                            window.assetQRScanner.processQRCode();
                        }
                    } else {
                        console.log('❌ No QR code found in image');
                        if (window.assetQRScanner) {
                            window.assetQRScanner.hideLoading();
                            window.assetQRScanner.showError('No QR code found in the uploaded image. Please try a clearer image.');
                        }
                    }
                } else {
                    console.error('❌ jsQR library not available');
                    if (window.assetQRScanner) {
                        window.assetQRScanner.hideLoading();
                        window.assetQRScanner.showError('QR code reading library not available. Please try manual input.');
                    }
                }

            } catch (error) {
                console.error('❌ Error processing image:', error);
                if (window.assetQRScanner) {
                    window.assetQRScanner.hideLoading();
                    window.assetQRScanner.showError('Error processing image: ' + error.message);
                }
            }
        };

        img.onerror = function() {
            console.error('❌ Error loading image');
            if (window.assetQRScanner) {
                window.assetQRScanner.hideLoading();
                window.assetQRScanner.showError('Error loading image. Please try a different file.');
            }
        };

        img.src = e.target.result;
    };

    reader.onerror = function() {
        console.error('❌ Error reading file');
        if (window.assetQRScanner) {
            window.assetQRScanner.hideLoading();
            window.assetQRScanner.showError('Error reading file. Please try again.');
        }
    };

    reader.readAsDataURL(file);
}

function triggerFileUpload() {
    document.getElementById('qrImageUpload').click();
}

function viewRecord(recordType, recordNumber) {
    if (recordType === 'workorder') {
        console.log('🔗 QR SCANNER: Opening work order detail view for:', recordNumber);

        // Check if we're on mobile
        const isMobile = window.innerWidth <= 768;

        if (isMobile) {
            // For mobile: navigate within the same app (no new tab)
            window.location.href = `/workorder/${recordNumber}`;
        } else {
            // For desktop: open in new tab
            window.open(`/workorder/${recordNumber}`, '_blank');
        }
    } else if (recordType === 'servicerequest') {
        // Open service request details (if available)
        console.log(`Opening service request ${recordNumber}`);
        // This would need to be implemented based on your service request detail page structure
        alert(`Service Request ${recordNumber} created. Details page not yet implemented.`);
    }
}

// ===== RELATED RECORDS FUNCTIONALITY =====

// Add related records methods to the AssetQRScanner class
AssetQRScanner.prototype.loadRelatedRecords = async function(assetData) {
    try {
        console.log('📋 QR SCANNER: Loading related records for asset:', assetData.assetnum, 'in site:', assetData.siteid);

        const requestData = {
            assetnum: assetData.assetnum,
            siteid: assetData.siteid
        };

        console.log('📋 QR SCANNER: Request data:', requestData);

        const response = await fetch('/api/qr-scanner/related-records', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(requestData)
        });

        console.log('📋 QR SCANNER: Response status:', response.status);

        if (!response.ok) {
            const errorText = await response.text();
            console.error('❌ QR SCANNER: HTTP Error:', response.status, errorText);
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const result = await response.json();
        console.log('📋 QR SCANNER: Full API response:', result);

        if (result.success) {
            console.log('✅ QR SCANNER: Related records loaded successfully');
            console.log('📊 QR SCANNER: Records summary:', result.summary);
            console.log('📊 QR SCANNER: Work orders count:', result.records?.work_orders?.length || 0);
            this.displayRelatedRecords(result, assetData);
        } else {
            console.error('❌ QR SCANNER: API returned error:', result.error);
            throw new Error(result.error || 'Failed to load related records');
        }

    } catch (error) {
        console.error('❌ QR SCANNER: Error loading related records:', error);
        this.showRelatedRecordsError(error.message);
    }
};

AssetQRScanner.prototype.showRelatedRecordsLoading = function() {
    const container = document.getElementById('relatedRecordsContainer');
    if (!container) return;

    container.innerHTML = `
        <div class="qr-related-records">
            <div class="records-grid">
                ${this.createLoadingSkeleton('Work Orders')}
                ${this.createLoadingSkeleton('Service Requests')}
                ${this.createLoadingSkeleton('Asset History')}
                ${this.createLoadingSkeleton('PM Schedules')}
            </div>
        </div>
    `;
};

AssetQRScanner.prototype.createLoadingSkeleton = function(title) {
    return `
        <div class="records-card animate-in">
            <div class="records-card-header">
                <h5 class="records-card-title">
                    <span class="skeleton skeleton-text short"></span>
                    <span class="skeleton records-card-count" style="width: 30px; height: 24px;"></span>
                </h5>
            </div>
            <div class="records-card-content">
                <div class="record-item">
                    <div class="skeleton skeleton-text medium"></div>
                    <div class="skeleton skeleton-text long"></div>
                    <div class="skeleton skeleton-text short"></div>
                </div>
                <div class="record-item">
                    <div class="skeleton skeleton-text medium"></div>
                    <div class="skeleton skeleton-text long"></div>
                    <div class="skeleton skeleton-text short"></div>
                </div>
            </div>
        </div>
    `;
};

AssetQRScanner.prototype.displayRelatedRecords = function(recordsData, assetData) {
    console.log('🎨 QR SCANNER: Updating related records with real data');
    console.log('📊 QR SCANNER: Records data:', recordsData);

    const records = recordsData.records || {};
    const analytics = recordsData.analytics || {};
    const summary = recordsData.summary || {};

    // Update Asset Overview Card
    const overviewContent = document.getElementById('assetOverviewContent');
    if (overviewContent) {
        overviewContent.innerHTML = this.createAssetSummaryContent(summary, analytics, assetData);
        console.log('✅ QR SCANNER: Asset overview updated');
    }

    // Update Work Orders Card
    const workOrdersCard = document.getElementById('workOrdersCard');
    if (workOrdersCard) {
        const countElement = workOrdersCard.querySelector('.records-card-count');
        const contentElement = workOrdersCard.querySelector('.records-card-content');

        if (countElement) {
            countElement.textContent = records.work_orders?.length || 0;
            countElement.classList.remove('skeleton');
        }
        if (contentElement) {
            contentElement.innerHTML = this.createWorkOrdersContent(records.work_orders || []);
        }
        console.log('✅ QR SCANNER: Work orders updated:', records.work_orders?.length || 0);
    }

    // Update Service Requests Card
    const serviceRequestsCard = document.getElementById('serviceRequestsCard');
    if (serviceRequestsCard) {
        const countElement = serviceRequestsCard.querySelector('.records-card-count');
        const contentElement = serviceRequestsCard.querySelector('.records-card-content');

        if (countElement) {
            countElement.textContent = records.service_requests?.length || 0;
            countElement.classList.remove('skeleton');
        }
        if (contentElement) {
            contentElement.innerHTML = this.createServiceRequestsContent(records.service_requests || []);
        }
        console.log('✅ QR SCANNER: Service requests updated:', records.service_requests?.length || 0);
    }

    // Update Asset History Card
    const assetHistoryCard = document.getElementById('assetHistoryCard');
    if (assetHistoryCard) {
        const countElement = assetHistoryCard.querySelector('.records-card-count');
        const contentElement = assetHistoryCard.querySelector('.records-card-content');

        if (countElement) {
            countElement.textContent = records.asset_history?.length || 0;
            countElement.classList.remove('skeleton');
        }
        if (contentElement) {
            contentElement.innerHTML = this.createAssetHistoryContent(records.asset_history || []);
        }
        console.log('✅ QR SCANNER: Asset history updated:', records.asset_history?.length || 0);
    }

    // Update PM Schedules Card
    const pmSchedulesCard = document.getElementById('pmSchedulesCard');
    if (pmSchedulesCard) {
        const countElement = pmSchedulesCard.querySelector('.records-card-count');
        const contentElement = pmSchedulesCard.querySelector('.records-card-content');

        if (countElement) {
            countElement.textContent = records.pm_schedules?.length || 0;
            countElement.classList.remove('skeleton');
        }
        if (contentElement) {
            contentElement.innerHTML = this.createPMSchedulesContent(records.pm_schedules || []);
        }
        console.log('✅ QR SCANNER: PM schedules updated:', records.pm_schedules?.length || 0);
    }

    // Add click handlers for record items
    this.addRecordClickHandlers();

    // Re-add event listeners after content update
    this.addCollapseEventListeners();
    this.addActionEventListeners();

    console.log('✅ QR SCANNER: All related records updated successfully');
};

AssetQRScanner.prototype.createAssetSummaryContent = function(summary, analytics, assetData) {
    const healthScore = summary.health_score || 0;
    const healthColor = healthScore >= 80 ? 'success' : healthScore >= 60 ? 'warning' : 'danger';

    return `
        <div class="row">
            <div class="col-md-3 col-6 mb-3">
                <div class="text-center">
                    <div class="h4 mb-1 text-${healthColor}">${healthScore}</div>
                    <small class="text-muted">Health Score</small>
                </div>
            </div>
            <div class="col-md-3 col-6 mb-3">
                <div class="text-center">
                    <div class="h4 mb-1 text-primary">${summary.open_work_orders || 0}</div>
                    <small class="text-muted">Open Work Orders</small>
                </div>
            </div>
            <div class="col-md-3 col-6 mb-3">
                <div class="text-center">
                    <div class="h4 mb-1 text-info">${summary.open_service_requests || 0}</div>
                    <small class="text-muted">Open Service Requests</small>
                </div>
            </div>
            <div class="col-md-3 col-6 mb-3">
                <div class="text-center">
                    <div class="h4 mb-1 text-warning">${summary.overdue_pms || 0}</div>
                    <small class="text-muted">Overdue PMs</small>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-md-6">
                <p class="mb-1"><strong>Last Maintenance:</strong> ${this.formatDate(summary.last_maintenance)}</p>
                <p class="mb-1"><strong>Next PM Due:</strong> ${this.formatDate(summary.next_pm_due)}</p>
            </div>
            <div class="col-md-6">
                <p class="mb-1"><strong>Total Records:</strong> ${summary.total_records || 0}</p>
                <p class="mb-1"><strong>Completed Work Orders:</strong> ${summary.completed_work_orders || 0}</p>
            </div>
        </div>
    `;
};

AssetQRScanner.prototype.createWorkOrdersContent = function(workOrders) {
    if (!workOrders || workOrders.length === 0) {
        return '<div class="record-item text-center text-muted">No work orders found</div>';
    }

    return workOrders.slice(0, 10).map(wo => {
        const statusClass = this.getStatusClass(wo.status);
        const priorityClass = this.getPriorityClass(wo.priority);
        const urgencyIcon = wo.urgency_level === 'HIGH' ? 'fas fa-exclamation-triangle text-danger' :
                           wo.urgency_level === 'MEDIUM' ? 'fas fa-exclamation-circle text-warning' :
                           'fas fa-info-circle text-info';

        return `
            <div class="record-item" data-record-type="workorder" data-record-id="${wo.wonum}">
                <div class="record-header">
                    <div class="record-id">${wo.wonum}</div>
                    <span class="record-status ${statusClass}">${wo.status}</span>
                </div>
                <div class="record-description">${wo.description || 'No description'}</div>
                <div class="record-meta">
                    <div class="record-date">
                        <i class="fas fa-calendar-alt"></i>
                        ${this.formatDate(wo.reportdate)}
                        ${wo.days_open > 0 ? `(${wo.days_open} days open)` : ''}
                    </div>
                    <div class="d-flex align-items-center gap-2">
                        <i class="${urgencyIcon}" title="${wo.urgency_level} urgency"></i>
                        <span class="record-priority ${priorityClass}">${wo.priority || '3'}</span>
                    </div>
                </div>
            </div>
        `;
    }).join('');
};

AssetQRScanner.prototype.createServiceRequestsContent = function(serviceRequests) {
    if (!serviceRequests || serviceRequests.length === 0) {
        return '<div class="record-item text-center text-muted">No service requests found</div>';
    }

    return serviceRequests.slice(0, 10).map(sr => {
        const statusClass = this.getStatusClass(sr.status);

        return `
            <div class="record-item" data-record-type="servicerequest" data-record-id="${sr.ticketid}">
                <div class="record-header">
                    <div class="record-id">${sr.ticketid}</div>
                    <span class="record-status ${statusClass}">${sr.status}</span>
                </div>
                <div class="record-description">${sr.description || 'No description'}</div>
                <div class="record-meta">
                    <div class="record-date">
                        <i class="fas fa-calendar-alt"></i>
                        ${this.formatDate(sr.reportdate)}
                        ${sr.days_open > 0 ? `(${sr.days_open} days open)` : ''}
                    </div>
                    <div class="text-muted">
                        <i class="fas fa-user"></i>
                        ${sr.reportedby || 'Unknown'}
                    </div>
                </div>
            </div>
        `;
    }).join('');
};

AssetQRScanner.prototype.createAssetHistoryContent = function(history) {
    if (!history || history.length === 0) {
        return '<div class="record-item text-center text-muted">No maintenance history found</div>';
    }

    return history.slice(0, 10).map(record => {
        const cost = record.total_cost > 0 ? `$${record.total_cost.toFixed(2)}` : 'N/A';

        return `
            <div class="record-item" data-record-type="history" data-record-id="${record.wonum}">
                <div class="record-header">
                    <div class="record-id">${record.wonum}</div>
                    <span class="record-status status-closed">COMPLETED</span>
                </div>
                <div class="record-description">${record.description || 'No description'}</div>
                <div class="record-meta">
                    <div class="record-date">
                        <i class="fas fa-check-circle"></i>
                        ${this.formatDate(record.actfinish)}
                        ${record.duration_days > 0 ? `(${record.duration_days} days)` : ''}
                    </div>
                    <div class="text-success">
                        <i class="fas fa-dollar-sign"></i>
                        ${cost}
                    </div>
                </div>
            </div>
        `;
    }).join('');
};

AssetQRScanner.prototype.createPMSchedulesContent = function(pmSchedules) {
    if (!pmSchedules || pmSchedules.length === 0) {
        return '<div class="record-item text-center text-muted">No PM schedules found</div>';
    }

    return pmSchedules.map(pm => {
        const statusClass = pm.is_overdue ? 'status-overdue' : 'status-open';
        const daysText = pm.is_overdue ? `${Math.abs(pm.days_until_due)} days overdue` :
                        pm.days_until_due > 0 ? `${pm.days_until_due} days until due` : 'Due today';

        return `
            <div class="record-item" data-record-type="pm" data-record-id="${pm.pmnum}">
                <div class="record-header">
                    <div class="record-id">${pm.pmnum}</div>
                    <span class="record-status ${statusClass}">${pm.status}</span>
                </div>
                <div class="record-description">${pm.description || 'No description'}</div>
                <div class="record-meta">
                    <div class="record-date">
                        <i class="fas fa-calendar-check"></i>
                        ${this.formatDate(pm.nextdate)}
                    </div>
                    <div class="${pm.is_overdue ? 'text-danger' : 'text-info'}">
                        <i class="fas fa-clock"></i>
                        ${daysText}
                    </div>
                </div>
            </div>
        `;
    }).join('');
};

// Utility methods for related records
AssetQRScanner.prototype.formatDate = function(dateString) {
    if (!dateString || dateString === 'N/A') return 'N/A';

    try {
        const date = new Date(dateString);
        return date.toLocaleDateString();
    } catch (e) {
        return dateString.substring(0, 10);
    }
};

AssetQRScanner.prototype.getStatusClass = function(status) {
    const statusMap = {
        'WAPPR': 'status-wappr',
        'INPRG': 'status-inprg',
        'COMP': 'status-closed',
        'CLOSE': 'status-closed',
        'CLOSED': 'status-closed',
        'OPEN': 'status-open',
        'NEW': 'status-open'
    };

    return statusMap[status] || 'status-open';
};

AssetQRScanner.prototype.getPriorityClass = function(priority) {
    const priorityMap = {
        '1': 'priority-1',
        '2': 'priority-2',
        '3': 'priority-3',
        '4': 'priority-3',
        '5': 'priority-3'
    };

    return priorityMap[priority] || 'priority-3';
};

AssetQRScanner.prototype.initializePullToRefresh = function(assetData) {
    const container = document.getElementById('relatedRecordsContent');
    const indicator = document.getElementById('pullIndicator');

    if (!container || !indicator) return;

    let startY = 0;
    let currentY = 0;
    let isRefreshing = false;

    container.addEventListener('touchstart', (e) => {
        if (container.scrollTop === 0) {
            startY = e.touches[0].clientY;
        }
    });

    container.addEventListener('touchmove', (e) => {
        if (isRefreshing || container.scrollTop > 0) return;

        currentY = e.touches[0].clientY;
        const diff = currentY - startY;

        if (diff > 0 && diff < 100) {
            indicator.style.top = `${-60 + diff}px`;
            indicator.style.opacity = diff / 100;
        } else if (diff >= 100) {
            indicator.classList.add('active');
            indicator.querySelector('i').style.animation = 'spin 1s linear infinite';
        }
    });

    container.addEventListener('touchend', (e) => {
        const diff = currentY - startY;

        if (diff >= 100 && !isRefreshing) {
            isRefreshing = true;
            this.refreshRelatedRecords(assetData);
        }

        // Reset indicator
        indicator.style.top = '-60px';
        indicator.style.opacity = '0';
        indicator.classList.remove('active');
        indicator.querySelector('i').style.animation = '';

        startY = 0;
        currentY = 0;
    });
};

AssetQRScanner.prototype.refreshRelatedRecords = function(assetData) {
    console.log('🔄 QR SCANNER: Refreshing related records...');

    setTimeout(() => {
        this.loadRelatedRecords(assetData);
        isRefreshing = false;
    }, 1000);
};

AssetQRScanner.prototype.addRecordClickHandlers = function() {
    const recordItems = document.querySelectorAll('.record-item[data-record-id]');

    recordItems.forEach(item => {
        item.addEventListener('click', () => {
            const recordType = item.dataset.recordType;
            const recordId = item.dataset.recordId;

            this.openRecordDetails(recordType, recordId);
        });
    });
};

AssetQRScanner.prototype.openRecordDetails = function(recordType, recordId) {
    console.log(`🔗 QR SCANNER: Opening ${recordType} details for ${recordId}`);

    const isMobile = window.innerWidth <= 768;
    let url = '';

    switch (recordType) {
        case 'workorder':
            url = `/workorder/${recordId}`;
            break;
        case 'servicerequest':
            url = `/service-request/${recordId}`;
            break;
        case 'history':
            url = `/workorder/${recordId}`;
            break;
        case 'pm':
            url = `/pm-schedule/${recordId}`;
            break;
        default:
            console.warn('Unknown record type:', recordType);
            return;
    }

    if (isMobile) {
        window.location.href = url;
    } else {
        window.open(url, '_blank');
    }
};

AssetQRScanner.prototype.showRelatedRecordsError = function(message) {
    const container = document.getElementById('relatedRecordsContainer');
    if (!container) return;

    container.innerHTML = `
        <div class="qr-related-records">
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <strong>Error loading related records:</strong> ${message}
                <div class="mt-2">
                    <button class="btn btn-sm btn-outline-danger" data-action="retry-records">
                        <i class="fas fa-retry me-1"></i>Retry
                    </button>
                </div>
            </div>
        </div>
    `;
};

// Event listener methods for CSP compliance
AssetQRScanner.prototype.addCollapseEventListeners = function() {
    const headers = document.querySelectorAll('[data-toggle="collapse"]');
    headers.forEach(header => {
        header.addEventListener('click', function() {
            const content = this.parentElement.querySelector('.records-card-content');
            if (content) {
                content.classList.toggle('collapsed');
                this.classList.toggle('collapsed');
            }
        });
    });
};

AssetQRScanner.prototype.addActionEventListeners = function() {
    // View record buttons
    const viewButtons = document.querySelectorAll('[data-action="view-record"]');
    viewButtons.forEach(button => {
        button.addEventListener('click', function() {
            const type = this.dataset.type;
            const number = this.dataset.number;
            window.assetQRScanner.viewRecord(type, number);
        });
    });

    // Clear scanner buttons
    const clearButtons = document.querySelectorAll('[data-action="clear-scanner"]');
    clearButtons.forEach(button => {
        button.addEventListener('click', function() {
            window.assetQRScanner.clearScanner();
        });
    });

    // View work order buttons
    const woButtons = document.querySelectorAll('[data-action="view-workorder"]');
    woButtons.forEach(button => {
        button.addEventListener('click', function() {
            const wonum = this.dataset.wonum;
            if (window.openWorkOrderDetailView) {
                window.openWorkOrderDetailView(wonum);
            }
        });
    });

    // Retry buttons
    const retryButtons = document.querySelectorAll('[data-action="retry-records"]');
    retryButtons.forEach(button => {
        button.addEventListener('click', function() {
            if (window.assetQRScanner && window.assetQRScanner.currentAssetData) {
                window.assetQRScanner.loadRelatedRecords(window.assetQRScanner.currentAssetData);
            }
        });
    });
};

// Related records are now integrated directly into displayAssetInfo method

// Initialize the scanner when the page loads
document.addEventListener('DOMContentLoaded', function() {
    console.log('🔍 ASSET QR SCANNER: DOM loaded, creating scanner instance');
    window.assetQRScanner = new AssetQRScanner();
});
