// COPY AND PASTE THIS INTO BROWSER CONSOLE ON QR SCANNER PAGE
// This will show exactly what's wrong with the grid layout

console.log('🔍 INSPECTING QR SCANNER LAYOUT');
console.log('================================');

// Find all potential grid containers
const selectors = [
    '.records-grid',
    '#relatedRecordsGrid', 
    '.qr-related-records .records-grid',
    '.qr-related-records',
    '#assetInfoCard .records-grid'
];

let foundGrid = false;

selectors.forEach(selector => {
    const element = document.querySelector(selector);
    if (element) {
        console.log(`\n📋 Found element: ${selector}`);
        console.log('Element:', element);
        
        const style = window.getComputedStyle(element);
        console.log('Display:', style.display);
        console.log('Grid template columns:', style.gridTemplateColumns);
        console.log('Gap:', style.gap);
        console.log('Width:', style.width);
        console.log('Position:', style.position);
        console.log('Float:', style.float);
        
        if (selector.includes('grid')) {
            foundGrid = true;
            
            // Check if it's actually a grid
            if (style.display === 'grid') {
                console.log('✅ This element IS a grid');
                
                // Check children
                const children = element.children;
                console.log(`📦 Grid has ${children.length} children`);
                
                Array.from(children).forEach((child, index) => {
                    const childStyle = window.getComputedStyle(child);
                    console.log(`   Child ${index + 1}:`, {
                        className: child.className,
                        display: childStyle.display,
                        width: childStyle.width,
                        marginBottom: childStyle.marginBottom,
                        gridColumn: childStyle.gridColumn
                    });
                });
                
            } else {
                console.log('❌ This element is NOT a grid! Display is:', style.display);
                
                // Try to force grid
                console.log('🔧 Attempting to force grid...');
                element.style.display = 'grid';
                element.style.gridTemplateColumns = 'repeat(auto-fit, minmax(300px, 1fr))';
                element.style.gap = '20px';
                element.style.width = '100%';
                
                // Check if it worked
                const newStyle = window.getComputedStyle(element);
                if (newStyle.display === 'grid') {
                    console.log('✅ Grid forced successfully!');
                } else {
                    console.log('❌ Grid forcing failed!');
                }
            }
        }
    }
});

if (!foundGrid) {
    console.log('❌ NO GRID CONTAINER FOUND!');
    
    // Look for any element with "grid" in class or id
    const allElements = document.querySelectorAll('*');
    const gridLike = Array.from(allElements).filter(el => 
        el.className.includes('grid') || el.id.includes('grid')
    );
    
    console.log('🔍 Elements with "grid" in name:', gridLike);
}

// Check CSS files
console.log('\n📄 CSS FILES:');
Array.from(document.styleSheets).forEach(sheet => {
    try {
        if (sheet.href) {
            console.log('CSS:', sheet.href);
            if (sheet.href.includes('qr_related_records')) {
                console.log('✅ QR related records CSS found');
            }
        }
    } catch (e) {
        console.log('Cannot access stylesheet');
    }
});

// Check for cards
console.log('\n📋 CHECKING CARDS:');
const cards = document.querySelectorAll('.records-card');
console.log(`Found ${cards.length} cards`);

if (cards.length > 0) {
    console.log('Card details:');
    cards.forEach((card, index) => {
        const style = window.getComputedStyle(card);
        console.log(`Card ${index + 1}:`, {
            className: card.className,
            display: style.display,
            width: style.width,
            marginBottom: style.marginBottom,
            position: style.position,
            float: style.float
        });
    });
}

// MANUAL FIX FUNCTION
window.FORCE_GRID_NOW = function() {
    console.log('🚨 FORCING GRID LAYOUT NOW!');
    
    // Find any potential grid container
    let container = document.querySelector('.records-grid') || 
                   document.getElementById('relatedRecordsGrid') ||
                   document.querySelector('.qr-related-records');
    
    if (!container) {
        // Create a grid container if none exists
        const cards = document.querySelectorAll('.records-card:not(.full-width)');
        if (cards.length > 0) {
            const parent = cards[0].parentElement;
            const gridDiv = document.createElement('div');
            gridDiv.className = 'forced-grid';
            gridDiv.style.display = 'grid';
            gridDiv.style.gridTemplateColumns = 'repeat(auto-fit, minmax(300px, 1fr))';
            gridDiv.style.gap = '20px';
            gridDiv.style.width = '100%';
            
            // Move cards into grid
            cards.forEach(card => {
                card.style.marginBottom = '0';
                gridDiv.appendChild(card);
            });
            
            parent.appendChild(gridDiv);
            console.log('✅ Created new grid container and moved cards');
            return;
        }
    }
    
    if (container) {
        // Force grid styles
        container.style.display = 'grid';
        container.style.gridTemplateColumns = 'repeat(auto-fit, minmax(300px, 1fr))';
        container.style.gap = '20px';
        container.style.width = '100%';
        container.style.margin = '0';
        container.style.padding = '0';
        
        // Fix cards
        const cards = container.querySelectorAll('.records-card:not(.full-width)');
        cards.forEach(card => {
            card.style.marginBottom = '0';
            card.style.width = '100%';
            card.style.display = 'flex';
            card.style.flexDirection = 'column';
        });
        
        console.log('✅ Grid forced on existing container');
    } else {
        console.log('❌ No container found to force grid on');
    }
};

console.log('\n💡 Run FORCE_GRID_NOW() to manually fix the layout');
console.log('================================');
