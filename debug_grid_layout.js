// Debug script to check grid layout
// Add this to browser console when viewing the QR scanner page

console.log('🔍 DEBUGGING GRID LAYOUT');
console.log('========================');

// Find the grid container
const grid = document.querySelector('.records-grid');
if (grid) {
    console.log('✅ Grid container found:', grid);
    
    // Check computed styles
    const computedStyle = window.getComputedStyle(grid);
    console.log('📊 Grid display:', computedStyle.display);
    console.log('📊 Grid template columns:', computedStyle.gridTemplateColumns);
    console.log('📊 Grid gap:', computedStyle.gap);
    console.log('📊 Grid width:', computedStyle.width);
    
    // Check if it's actually a grid
    if (computedStyle.display === 'grid') {
        console.log('✅ Grid is active!');
    } else {
        console.log('❌ Grid is NOT active! Display is:', computedStyle.display);
        
        // Force grid layout
        console.log('🔧 Forcing grid layout...');
        grid.style.display = 'grid';
        grid.style.gridTemplateColumns = 'repeat(auto-fit, minmax(300px, 1fr))';
        grid.style.gap = '1.5rem';
        grid.style.width = '100%';
        
        // Check again
        const newStyle = window.getComputedStyle(grid);
        console.log('🔄 After forcing - Display:', newStyle.display);
        console.log('🔄 After forcing - Columns:', newStyle.gridTemplateColumns);
    }
    
    // Check cards
    const cards = grid.querySelectorAll('.records-card');
    console.log('📋 Found', cards.length, 'cards in grid');
    
    cards.forEach((card, index) => {
        const cardStyle = window.getComputedStyle(card);
        console.log(`📋 Card ${index + 1}:`, {
            display: cardStyle.display,
            width: cardStyle.width,
            marginBottom: cardStyle.marginBottom,
            gridColumn: cardStyle.gridColumn
        });
    });
    
} else {
    console.log('❌ Grid container NOT found!');
    
    // Look for alternative selectors
    const alternatives = [
        '#relatedRecordsGrid',
        '.qr-related-records .records-grid',
        '[id*="grid"]',
        '[class*="grid"]'
    ];
    
    alternatives.forEach(selector => {
        const element = document.querySelector(selector);
        if (element) {
            console.log('🔍 Found alternative:', selector, element);
        }
    });
}

// Check CSS files
console.log('📄 Checking CSS files...');
const stylesheets = Array.from(document.styleSheets);
stylesheets.forEach((sheet, index) => {
    try {
        if (sheet.href && sheet.href.includes('qr_related_records')) {
            console.log('✅ Found QR related records CSS:', sheet.href);
        }
    } catch (e) {
        console.log('⚠️ Cannot access stylesheet:', e.message);
    }
});

// Check for conflicting CSS
console.log('🔍 Checking for CSS conflicts...');
const allElements = document.querySelectorAll('*');
let gridElements = 0;
allElements.forEach(el => {
    const style = window.getComputedStyle(el);
    if (style.display === 'grid') {
        gridElements++;
    }
});
console.log('📊 Total elements with display: grid:', gridElements);

// Manual grid fix function
window.fixGridLayout = function() {
    console.log('🔧 MANUAL GRID FIX');
    const grid = document.querySelector('.records-grid') || document.getElementById('relatedRecordsGrid');
    if (grid) {
        grid.style.display = 'grid';
        grid.style.gridTemplateColumns = 'repeat(auto-fit, minmax(300px, 1fr))';
        grid.style.gap = '1.5rem';
        grid.style.width = '100%';
        grid.style.margin = '1rem 0';
        
        const cards = grid.querySelectorAll('.records-card');
        cards.forEach(card => {
            card.style.marginBottom = '0';
            card.style.width = '100%';
            card.style.display = 'flex';
            card.style.flexDirection = 'column';
        });
        
        console.log('✅ Grid layout manually fixed!');
        return true;
    } else {
        console.log('❌ Grid container not found for manual fix');
        return false;
    }
};

console.log('💡 Run fixGridLayout() to manually fix the grid');
console.log('========================');
