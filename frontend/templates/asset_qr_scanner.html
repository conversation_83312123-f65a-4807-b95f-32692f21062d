{% extends "base.html" %}

{% block title %}Asset QR Code Scanner{% endblock %}

{% block head %}
<link rel="stylesheet" href="{{ url_for('static', filename='css/asset_management.css') }}">
<link rel="stylesheet" href="{{ url_for('static', filename='css/qr_related_records.css') }}">
<style>
    .scanner-container {
        max-width: 800px;
        margin: 0 auto;
    }
    
    .qr-scanner-section {
        background: white;
        border-radius: 10px;
        padding: 20px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        margin-bottom: 20px;
    }
    
    .scanner-input-group {
        margin-bottom: 15px;
    }
    
    .scanner-input {
        font-family: monospace;
        font-size: 14px;
    }
    
    .asset-info-card {
        background: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 20px;
    }
    
    .asset-operations {
        display: none;
    }
    
    .operation-card {
        border: 1px solid #dee2e6;
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 15px;
        background: white;
    }
    
    .operation-card.active {
        border-color: #007bff;
        background: #f8f9ff;
    }

    /* Loading overlay */
    .loading-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.5);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 9999;
    }

    .loading-content {
        background: white;
        padding: 30px;
        border-radius: 8px;
        text-align: center;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    }

    .loading-text {
        font-weight: 500;
        color: #495057;
    }

    .spinner-border {
        width: 3rem;
        height: 3rem;
    }

    .btn {
        transition: all 0.3s ease;
    }

    .btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    }

    /* Mobile Responsive */
    @media (max-width: 768px) {
        .qr-scanner-section {
            padding: 15px;
            margin-bottom: 15px;
        }

        .asset-info-card {
            padding: 15px;
        }

        .btn {
            width: 100%;
            margin-bottom: 10px;
        }

        .row .col-md-6 {
            margin-bottom: 15px;
        }
    }

    @media (max-width: 576px) {
        .container-fluid {
            padding: 10px;
        }

        h5 {
            font-size: 1.1rem;
        }

        .btn-sm {
            font-size: 0.8rem;
            padding: 0.4rem 0.8rem;
        }

        .operation-card {
            padding: 10px;
        }
    }
</style>
{% endblock %}

{% block content %}
<!-- Loading Overlay -->
<div id="loadingOverlay" class="loading-overlay" style="display: none;">
    <div class="loading-content">
        <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">Loading...</span>
        </div>
        <div class="loading-text mt-3">Processing...</div>
    </div>
</div>

<div class="container-fluid">
    <!-- Header Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="page-header">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h1 class="page-title">
                            <i class="fas fa-qrcode me-2"></i>Asset QR Code Scanner
                        </h1>
                        <p class="page-subtitle text-muted">
                            Scan asset QR codes to create work orders and service requests
                        </p>
                    </div>
                    <div class="header-actions">
                        <button class="btn btn-outline-primary" onclick="clearScanner()" title="Clear Scanner">
                            <i class="fas fa-broom me-1"></i>
                            <span class="d-none d-md-inline">Clear</span>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="scanner-container">
        <!-- QR Code Scanner Section -->
        <div class="qr-scanner-section">
            <h5 class="mb-3">
                <i class="fas fa-camera me-2"></i>Scan Asset QR Code
            </h5>
            
            <!-- Manual QR Code Input -->
            <div class="scanner-input-group">
                <label for="qrCodeInput" class="form-label">QR Code Data (Manual Entry)</label>
                <div class="input-group">
                    <input type="text"
                           class="form-control scanner-input"
                           id="qrCodeInput"
                           placeholder="Paste asset QR code data here or scan with camera..."
                           autocomplete="off">
                    <button class="btn btn-primary" type="button" onclick="processQRCode()">
                        <i class="fas fa-search me-1"></i>Process
                    </button>
                </div>
                <div class="form-text">
                    Paste the asset QR code content or use the camera scanner below
                </div>
            </div>

            <!-- QR Code Upload Section -->
            <div class="scanner-input-group">
                <label for="qrImageUpload" class="form-label">Upload QR Code Image</label>
                <div class="input-group">
                    <input type="file"
                           class="form-control"
                           id="qrImageUpload"
                           accept="image/*"
                           onchange="handleQRImageUpload(event)">
                    <button class="btn btn-success" type="button" onclick="triggerFileUpload()">
                        <i class="fas fa-upload me-1"></i>Browse
                    </button>
                </div>
                <div class="form-text">
                    Upload an image containing an asset QR code to scan
                </div>
            </div>

            <!-- Camera Scanner -->
            <div class="camera-scanner-section">
                <div class="text-center mb-3">
                    <button class="btn btn-outline-secondary" onclick="toggleCameraScanner()" id="cameraToggleBtn" disabled>
                        <i class="fas fa-camera me-1"></i>Loading Camera...
                    </button>
                    <button class="btn btn-outline-secondary ms-2" onclick="switchCamera()" id="cameraSwitchBtn" style="display: none;">
                        <i class="fas fa-sync-alt me-1"></i>Switch Camera
                    </button>
                    <button class="btn btn-outline-warning ms-2" onclick="forceEnableCamera()" id="forceEnableBtn" style="display: none;">
                        <i class="fas fa-exclamation-triangle me-1"></i>Force Enable
                    </button>
                </div>

                <!-- Camera Video Element -->
                <div id="cameraContainer" class="text-center" style="display: none;">
                    <div class="position-relative d-inline-block">
                        <video id="qrVideo" autoplay playsinline style="width: 100%; max-width: 400px; border: 2px solid #007bff; border-radius: 8px; background: #000;"></video>
                        <canvas id="qrCanvas" style="display: none;"></canvas>
                        <div id="scanRegion" style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); width: 200px; height: 200px; border: 2px solid #00ff00; border-radius: 8px; pointer-events: none;"></div>
                    </div>
                    <div class="mt-2">
                        <small class="text-muted">Position asset QR code within the green scanning area</small>
                    </div>
                    <div class="mt-2">
                        <button class="btn btn-warning btn-sm" onclick="captureQRManually()" id="captureBtn">
                            <i class="fas fa-camera-retro me-1"></i>Capture QR Now
                        </button>
                    </div>
                    <div class="mt-2">
                        <div id="cameraInfo" class="small text-info"></div>
                        <div id="scanStatus" class="small text-success mt-1"></div>
                        <div id="debugInfo" class="small text-muted mt-1" style="font-family: monospace;"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Asset Information Display -->
        <div id="assetInfoSection" class="qr-scanner-section" style="display: none;">
            <h5 class="mb-3">
                <i class="fas fa-cogs me-2"></i>Asset Information
            </h5>
            <div id="assetInfoCard" class="asset-info-card">
                <!-- Asset details will be populated here -->
            </div>
        </div>

        <!-- Related Records are now integrated directly into the asset info display -->

        <!-- Asset Operations Section -->
        <div id="assetOperations" class="asset-operations">
            <div class="qr-scanner-section">
                <h5 class="mb-3">
                    <i class="fas fa-tasks me-2"></i>Asset Operations
                </h5>

                <!-- Create Work Order Operation -->
                <div class="operation-card" id="createWorkOrderCard">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h6 class="mb-0">
                            <i class="fas fa-wrench me-2"></i>Create Work Order
                        </h6>
                        <button class="btn btn-sm btn-warning" onclick="createWorkOrderForAsset()">
                            <i class="fas fa-plus me-1"></i>Create
                        </button>
                    </div>
                    <p class="text-muted mb-0">Create a new work order for this asset with pre-populated asset information.</p>
                </div>

                <!-- Create Service Request Operation -->
                <div class="operation-card" id="createServiceRequestCard">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h6 class="mb-0">
                            <i class="fas fa-headset me-2"></i>Create Service Request
                        </h6>
                        <button class="btn btn-sm btn-info" onclick="createServiceRequestForAsset()">
                            <i class="fas fa-plus me-1"></i>Create
                        </button>
                    </div>
                    <p class="text-muted mb-0">Create a new service request for this asset with pre-populated asset information.</p>
                </div>

                <!-- View Asset Details Operation -->
                <div class="operation-card" id="viewAssetDetailsCard">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h6 class="mb-0">
                            <i class="fas fa-eye me-2"></i>View Asset Details
                        </h6>
                        <button class="btn btn-sm btn-primary" onclick="viewAssetDetails()">
                            <i class="fas fa-eye me-1"></i>View
                        </button>
                    </div>
                    <p class="text-muted mb-0">View comprehensive asset details and related records.</p>
                </div>
            </div>
        </div>

        <!-- Results Section -->
        <div id="operationResults" class="qr-scanner-section" style="display: none;">
            <h5 class="mb-3">
                <i class="fas fa-check-circle me-2"></i>Operation Results
            </h5>
            <div id="resultsContent">
                <!-- Results will be displayed here -->
            </div>
        </div>
    </div>
</div>

<!-- Create Work Order Modal -->
<div class="modal fade" id="createWorkOrderModal" tabindex="-1" aria-labelledby="createWorkOrderModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="createWorkOrderModalLabel">
                    <i class="fas fa-wrench me-2"></i>Create Work Order
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" id="createWorkOrderModalBody">
                <!-- Dynamic content will be inserted here -->
            </div>
        </div>
    </div>
</div>

<!-- Create Service Request Modal -->
<div class="modal fade" id="createServiceRequestModal" tabindex="-1" aria-labelledby="createServiceRequestModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="createServiceRequestModalLabel">
                    <i class="fas fa-headset me-2"></i>Create Service Request
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" id="createServiceRequestModalBody">
                <!-- Dynamic content will be inserted here -->
            </div>
        </div>
    </div>
</div>

<!-- AI Insights Modal -->
<div class="modal fade" id="aiInsightsModal" tabindex="-1" aria-labelledby="aiInsightsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="aiInsightsModalLabel">
                    <i class="fas fa-brain me-2"></i>AI Work Order Analysis
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" id="aiInsightsModalBody">
                <!-- Dynamic content will be inserted here -->
            </div>
        </div>
    </div>
</div>

<!-- QR Scanner Library - Load before our scanner script -->
<script src="/static/js/jsqr.min.js"></script>
<script>
console.log('🔍 JSQR LOADED: typeof jsQR =', typeof jsQR);
if (typeof jsQR !== 'undefined') {
    console.log('✅ SUCCESS: jsQR is available');
    window.dispatchEvent(new Event('jsQRLoaded'));
} else {
    console.error('❌ ERROR: jsQR not available');
}
</script>
<script src="{{ url_for('static', filename='js/asset_qr_scanner.js') }}"></script>
{% endblock %}
