/* QR Scanner Related Records - Mobile-First Magical UI */

/* ===== MOBILE-FIRST RESPONSIVE DESIGN ===== */

/* Base styles for mobile devices */
.qr-related-records {
    padding: 1rem;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    min-height: 100vh;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* ===== GRID LAYOUT SYSTEM ===== */

/* SIMPLIFIED GRID LAYOUT */
.records-grid {
    display: grid !important;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr)) !important;
    gap: 1.5rem !important;
    margin-top: 1rem !important;
    width: 100% !important;
    box-sizing: border-box !important;
}

/* Full width cards span all columns */
.records-card.full-width {
    grid-column: 1 / -1 !important;
}

/* Force grid display */
.qr-related-records .records-grid {
    display: grid !important;
}

.qr-related-records .records-card {
    display: flex !important;
    flex-direction: column !important;
    margin-bottom: 0 !important;
}

/* ===== MAGICAL CARD ANIMATIONS ===== */

.records-card {
    background: white;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    margin-bottom: 1rem;
    overflow: hidden;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    transform: translateY(0);
    border: 1px solid rgba(255, 255, 255, 0.2);
    min-height: 200px;
    display: flex;
    flex-direction: column;
    width: 100%;
    box-sizing: border-box;
}

.records-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.records-card.animate-in {
    animation: slideInUp 0.6s cubic-bezier(0.4, 0, 0.2, 1) forwards;
    opacity: 0;
    transform: translateY(30px);
}

@keyframes slideInUp {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* ===== COLOR-CODED CARD TYPES ===== */

.records-card.work-orders {
    border-left: 4px solid #007bff;
    background: linear-gradient(135deg, #ffffff 0%, #f8f9ff 100%);
}

.records-card.service-requests {
    border-left: 4px solid #28a745;
    background: linear-gradient(135deg, #ffffff 0%, #f8fff8 100%);
}

.records-card.asset-history {
    border-left: 4px solid #ffc107;
    background: linear-gradient(135deg, #ffffff 0%, #fffef8 100%);
}

.records-card.pm-schedules {
    border-left: 4px solid #dc3545;
    background: linear-gradient(135deg, #ffffff 0%, #fff8f8 100%);
}

.records-card.analytics {
    border-left: 4px solid #6f42c1;
    background: linear-gradient(135deg, #ffffff 0%, #faf8ff 100%);
}

/* ===== CARD HEADERS ===== */

.records-card-header {
    padding: 1rem 1.25rem;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(10px);
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
}

.records-card-header:hover {
    background: rgba(255, 255, 255, 0.95);
}

.records-card-title {
    font-size: 1.1rem;
    font-weight: 600;
    margin: 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
    color: #2c3e50;
}

.records-card-icon {
    width: 24px;
    height: 24px;
    margin-right: 0.75rem;
    opacity: 0.8;
    transition: all 0.3s ease;
}

.records-card-header:hover .records-card-icon {
    opacity: 1;
    transform: scale(1.1);
}

.records-card-count {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 500;
    min-width: 24px;
    text-align: center;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

.collapse-indicator {
    transition: transform 0.3s ease;
    color: #6c757d;
    font-size: 1.2rem;
}

.collapsed .collapse-indicator {
    transform: rotate(-90deg);
}

/* ===== CARD CONTENT ===== */

.records-card-content {
    padding: 1rem 1.25rem 1.25rem;
    max-height: 500px;
    overflow-y: auto;
    transition: all 0.3s ease;
    flex: 1;
    display: flex;
    flex-direction: column;
    background: rgba(255, 255, 255, 0.95);
}

.records-card-content.collapsed {
    max-height: 0;
    overflow: hidden;
}

/* ===== RECORD ITEMS ===== */

.record-item {
    padding: 1rem 1.25rem;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    position: relative;
    cursor: pointer;
}

.record-item:last-child {
    border-bottom: none;
}

.record-item:hover {
    background: rgba(0, 123, 255, 0.02);
    transform: translateX(4px);
}

.record-item:active {
    transform: translateX(2px) scale(0.98);
}

.record-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 0.5rem;
}

.record-id {
    font-weight: 600;
    color: #2c3e50;
    font-size: 0.95rem;
}

.record-status {
    padding: 0.25rem 0.75rem;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Status color coding */
.status-open { background: #e3f2fd; color: #1976d2; }
.status-inprg { background: #fff3e0; color: #f57c00; }
.status-closed { background: #e8f5e8; color: #388e3c; }
.status-wappr { background: #fce4ec; color: #c2185b; }
.status-overdue { background: #ffebee; color: #d32f2f; }

.record-description {
    color: #5a6c7d;
    font-size: 0.9rem;
    line-height: 1.4;
    margin-bottom: 0.5rem;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.record-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.8rem;
    color: #8e9aaf;
}

.record-date {
    display: flex;
    align-items: center;
}

.record-date i {
    margin-right: 0.25rem;
    opacity: 0.7;
}

.record-priority {
    padding: 0.2rem 0.5rem;
    border-radius: 8px;
    font-weight: 500;
}

.priority-1 { background: #ffebee; color: #c62828; }
.priority-2 { background: #fff3e0; color: #ef6c00; }
.priority-3 { background: #e8f5e8; color: #2e7d32; }

/* ===== LOADING SKELETONS ===== */

.skeleton {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
    border-radius: 4px;
}

@keyframes loading {
    0% { background-position: 200% 0; }
    100% { background-position: -200% 0; }
}

.skeleton-card {
    height: 120px;
    margin-bottom: 1rem;
    border-radius: 16px;
}

.skeleton-text {
    height: 1rem;
    margin-bottom: 0.5rem;
}

.skeleton-text.short { width: 60%; }
.skeleton-text.medium { width: 80%; }
.skeleton-text.long { width: 100%; }

/* ===== PULL TO REFRESH ===== */

.pull-to-refresh {
    position: relative;
    overflow: hidden;
}

.pull-indicator {
    position: absolute;
    top: -60px;
    left: 50%;
    transform: translateX(-50%);
    width: 40px;
    height: 40px;
    background: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    z-index: 10;
}

.pull-indicator.active {
    top: 20px;
}

.pull-indicator i {
    color: #007bff;
    font-size: 1.2rem;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* ===== RESPONSIVE BREAKPOINTS ===== */

/* SIMPLIFIED RESPONSIVE DESIGN */
@media (min-width: 576px) {
    .qr-related-records {
        padding: 1.5rem;
    }

    .records-grid {
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)) !important;
        gap: 1.5rem !important;
    }
}

@media (min-width: 768px) {
    .qr-related-records {
        padding: 2rem;
    }

    .records-grid {
        grid-template-columns: repeat(auto-fit, minmax(320px, 1fr)) !important;
        gap: 2rem !important;
    }
}

@media (min-width: 992px) {
    .qr-related-records {
        padding: 2.5rem;
    }

    .records-grid {
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)) !important;
        gap: 2rem !important;
    }
}

/* FORCE GRID LAYOUT - NUCLEAR OPTION */
div.records-grid,
.qr-related-records .records-grid,
#assetInfoCard .records-grid {
    display: grid !important;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr)) !important;
    gap: 1.5rem !important;
    width: 100% !important;
    margin-top: 1rem !important;
    box-sizing: border-box !important;
}

div.records-card,
.qr-related-records .records-card,
#assetInfoCard .records-card {
    width: 100% !important;
    box-sizing: border-box !important;
    margin-bottom: 0 !important;
    display: flex !important;
    flex-direction: column !important;
}

/* Override Bootstrap and other frameworks */
.records-grid[class*="col-"],
.records-grid[class*="row"] {
    display: grid !important;
}

/* Make sure cards don't have float or other display properties */
.records-card {
    float: none !important;
    position: relative !important;
}

/* ===== ACCESSIBILITY ===== */

.records-card:focus-within {
    outline: 2px solid #007bff;
    outline-offset: 2px;
}

.record-item:focus {
    outline: 2px solid #007bff;
    outline-offset: -2px;
    background: rgba(0, 123, 255, 0.05);
}

/* Reduced motion for accessibility */
@media (prefers-reduced-motion: reduce) {
    .records-card,
    .record-item,
    .collapse-indicator,
    .records-card-icon {
        transition: none;
    }
    
    .records-card.animate-in {
        animation: none;
        opacity: 1;
        transform: none;
    }
}

/* ===== DARK MODE SUPPORT ===== */

@media (prefers-color-scheme: dark) {
    .qr-related-records {
        background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
        color: #e0e0e0;
    }
    
    .records-card {
        background: #2d2d2d;
        border-color: rgba(255, 255, 255, 0.1);
        color: #e0e0e0;
    }
    
    .records-card-header {
        background: rgba(45, 45, 45, 0.8);
        border-bottom-color: rgba(255, 255, 255, 0.1);
    }
    
    .record-item:hover {
        background: rgba(255, 255, 255, 0.05);
    }
}

/* ===== FINAL GRID OVERRIDE - HIGHEST PRIORITY ===== */

/* This CSS has the highest priority and will definitely work */
.qr-related-records .records-grid {
    display: grid !important;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)) !important;
    gap: 1.5rem !important;
    width: 100% !important;
    margin: 1rem 0 !important;
    padding: 0 !important;
}

.qr-related-records .records-card:not(.full-width) {
    margin-bottom: 0 !important;
    width: 100% !important;
    display: flex !important;
    flex-direction: column !important;
}

.qr-related-records .records-card.full-width {
    grid-column: 1 / -1 !important;
    margin-bottom: 1rem !important;
}

/* Force grid on mobile too */
@media (max-width: 575px) {
    .qr-related-records .records-grid {
        grid-template-columns: 1fr !important;
        gap: 1rem !important;
    }
}

/* Force grid on tablets */
@media (min-width: 576px) and (max-width: 991px) {
    .qr-related-records .records-grid {
        grid-template-columns: repeat(2, 1fr) !important;
        gap: 1.5rem !important;
    }
}

/* Force grid on desktop */
@media (min-width: 992px) {
    .qr-related-records .records-grid {
        grid-template-columns: repeat(3, 1fr) !important;
        gap: 2rem !important;
    }
}
