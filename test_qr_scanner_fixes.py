#!/usr/bin/env python3
"""
Test script to validate QR scanner work order creation AI analysis fixes.

This script tests:
1. AI recommendations display format (no more [object Object])
2. Improved similarity detection for duplicate work orders
3. Enhanced work order number extraction
4. Form workflow improvements

Author: Augment Agent
Date: 2025-01-28
"""

import requests
import json
import time

def test_ai_analysis_improvements():
    """Test the improved AI analysis functionality."""
    
    base_url = "http://localhost:5000"
    
    # Test data with identical descriptions to check duplicate detection
    test_data = {
        "asset_data": {
            "assetnum": "PUMP001",
            "siteid": "BEDFORD",
            "description": "Centrifugal pump for water circulation"
        },
        "user_description": "repair pump motor bearing replacement",
        "form_data": {
            "assetnum": "PUMP001",
            "siteid": "BEDFORD",
            "orgid": "EAGLENA",
            "description": "repair pump motor bearing replacement",
            "worktype": "CM",
            "priority": "2",
            "longdescription": "Motor bearing needs replacement due to excessive noise and vibration",
            "createdby": "sd"
        }
    }
    
    print("🧪 Testing AI Analysis Improvements...")
    print("=" * 60)
    
    try:
        # Test AI analysis endpoint
        print("📡 Sending AI analysis request...")
        response = requests.post(
            f"{base_url}/api/ai-analysis/analyze-workorder",
            json=test_data,
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            
            if result.get('success'):
                print("✅ AI Analysis Request Successful")
                
                # Test 1: Check recommendations format
                recommendations = result.get('recommendations', [])
                print(f"\n🔍 Testing Recommendations Format:")
                print(f"   • Found {len(recommendations)} recommendations")
                
                for i, rec in enumerate(recommendations):
                    if isinstance(rec, dict):
                        print(f"   • Rec {i+1}: [{rec.get('type', 'UNKNOWN')}] {rec.get('title', 'No title')}")
                        print(f"     Message: {rec.get('message', 'No message')}")
                    else:
                        print(f"   • Rec {i+1}: {rec} (String format)")
                
                # Test 2: Check similarity detection
                duplication_analysis = result.get('duplication_analysis', {})
                kpis = result.get('kpis', {})
                
                print(f"\n🔍 Testing Similarity Detection:")
                print(f"   • Risk Level: {kpis.get('risk_level', 'UNKNOWN')}")
                print(f"   • Highest Similarity: {round((kpis.get('highest_similarity_score', 0) * 100), 1)}%")
                print(f"   • Potential Duplicates: {kpis.get('potential_duplicates', 0)}")
                
                high_risk = duplication_analysis.get('high_risk', [])
                medium_risk = duplication_analysis.get('medium_risk', [])
                
                if high_risk:
                    print(f"   • HIGH RISK DUPLICATES ({len(high_risk)}):")
                    for dup in high_risk[:2]:  # Show first 2
                        wo = dup['workorder']
                        similarity = round(dup['similarity_score'] * 100, 1)
                        print(f"     - WO {wo['wonum']}: {similarity}% similar")
                        print(f"       Description: '{wo['description']}'")
                        print(f"       Risk Factors: {', '.join(dup.get('risk_factors', []))}")
                
                if medium_risk:
                    print(f"   • MEDIUM RISK DUPLICATES ({len(medium_risk)}):")
                    for dup in medium_risk[:2]:  # Show first 2
                        wo = dup['workorder']
                        similarity = round(dup['similarity_score'] * 100, 1)
                        print(f"     - WO {wo['wonum']}: {similarity}% similar")
                        print(f"       Description: '{wo['description']}'")
                
                # Test 3: Check analysis performance
                duration = result.get('analysis_duration_ms', 0)
                print(f"\n⚡ Performance:")
                print(f"   • Analysis Duration: {duration}ms")
                print(f"   • Total Open Work Orders: {kpis.get('total_open_workorders', 0)}")
                
                print(f"\n✅ All AI Analysis Tests Completed Successfully!")
                
            else:
                print(f"❌ AI Analysis Failed: {result.get('error')}")
                return False
                
        else:
            print(f"❌ Request Failed: {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Test Error: {str(e)}")
        return False
    
    return True

def test_work_order_creation_workflow():
    """Test the work order creation workflow improvements."""
    
    print("\n🧪 Testing Work Order Creation Workflow...")
    print("=" * 60)
    
    # This would typically be tested through the frontend, but we can test the backend
    base_url = "http://localhost:5000"
    
    test_wo_data = {
        "assetnum": "PUMP001",
        "siteid": "BEDFORD",
        "orgid": "EAGLENA",
        "description": "Test work order for validation",
        "worktype": "CM",
        "priority": "3",
        "longdescription": "Testing the improved workflow",
        "createdby": "sd"
    }
    
    try:
        print("📡 Testing work order creation endpoint...")
        response = requests.post(
            f"{base_url}/api/asset/create-workorder",
            json=test_wo_data,
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            
            if result.get('success'):
                print("✅ Work Order Creation Successful")
                
                # Test work order number extraction
                wonum = result.get('wonum') or result.get('data', {}).get('wonum')
                print(f"   • Work Order Number: {wonum}")
                
                if wonum and wonum != 'Generated by Maximo':
                    print(f"   • ✅ Valid work order number extracted")
                else:
                    print(f"   • ⚠️ Work order number may need manual assignment")
                
                return True
            else:
                print(f"❌ Work Order Creation Failed: {result.get('error')}")
                return False
        else:
            print(f"❌ Request Failed: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Test Error: {str(e)}")
        return False

if __name__ == "__main__":
    print("🚀 Starting QR Scanner Fixes Validation Tests")
    print("=" * 60)
    
    # Run tests
    ai_test_passed = test_ai_analysis_improvements()
    wo_test_passed = test_work_order_creation_workflow()
    
    print("\n" + "=" * 60)
    print("📊 TEST RESULTS SUMMARY")
    print("=" * 60)
    print(f"AI Analysis Improvements: {'✅ PASSED' if ai_test_passed else '❌ FAILED'}")
    print(f"Work Order Creation Workflow: {'✅ PASSED' if wo_test_passed else '❌ FAILED'}")
    
    if ai_test_passed and wo_test_passed:
        print("\n🎉 ALL TESTS PASSED! QR Scanner fixes are working correctly.")
    else:
        print("\n⚠️ Some tests failed. Please review the issues above.")
