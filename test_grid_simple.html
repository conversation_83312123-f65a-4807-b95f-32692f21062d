<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Grid Test</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background: #f5f5f5;
        }
        
        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            width: 100%;
            background: rgba(255, 0, 0, 0.1);
            border: 2px solid red;
            padding: 20px;
        }
        
        .test-card {
            background: white;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            min-height: 150px;
        }
        
        .test-card h3 {
            margin: 0 0 10px 0;
            color: #333;
        }
        
        .vertical-test {
            background: rgba(0, 255, 0, 0.1);
            border: 2px solid green;
            padding: 20px;
            margin-top: 40px;
        }
        
        .vertical-test .test-card {
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <h1>Grid Layout Test</h1>
    
    <h2>This should be GRID (horizontal):</h2>
    <div class="test-grid">
        <div class="test-card">
            <h3>Card 1</h3>
            <p>This should be in a grid layout</p>
        </div>
        <div class="test-card">
            <h3>Card 2</h3>
            <p>This should be next to Card 1</p>
        </div>
        <div class="test-card">
            <h3>Card 3</h3>
            <p>This should be next to Card 2</p>
        </div>
        <div class="test-card">
            <h3>Card 4</h3>
            <p>This should wrap to next row</p>
        </div>
    </div>
    
    <h2>This should be VERTICAL (for comparison):</h2>
    <div class="vertical-test">
        <div class="test-card">
            <h3>Card A</h3>
            <p>This should stack vertically</p>
        </div>
        <div class="test-card">
            <h3>Card B</h3>
            <p>This should be below Card A</p>
        </div>
        <div class="test-card">
            <h3>Card C</h3>
            <p>This should be below Card B</p>
        </div>
    </div>
    
    <script>
        console.log('Testing grid layout...');
        
        const grid = document.querySelector('.test-grid');
        const style = window.getComputedStyle(grid);
        
        console.log('Grid display:', style.display);
        console.log('Grid template columns:', style.gridTemplateColumns);
        console.log('Grid gap:', style.gap);
        
        if (style.display === 'grid') {
            console.log('✅ Grid is working!');
        } else {
            console.log('❌ Grid is NOT working!');
        }
    </script>
</body>
</html>
