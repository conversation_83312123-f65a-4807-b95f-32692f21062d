<!DOCTYPE html>
<html lang="en" data-bs-theme="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}<PERSON>o OAuth Login{% endblock %}</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Google Fonts -->
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}?v=mobile2024">
    {% block extra_css %}{% endblock %}
</head>
<body>
    {% if 'username' in session %}
    <!-- Top Header for all devices -->
    <header class="app-header">
        <div class="container-fluid d-flex justify-content-between align-items-center">
            <div class="app-title">
                <a href="{{ url_for('welcome') }}">
                    <i class="fas fa-key me-2"></i>Maximo OAuth
                </a>
            </div>
            <div class="d-flex align-items-center">
                <div class="d-none d-md-flex me-3">
                    <a href="{{ url_for('welcome') }}" class="nav-link me-3">
                        <i class="fas fa-home me-1"></i>Home
                    </a>
                    <a href="{{ url_for('asset_management') }}" class="nav-link me-3">
                        <i class="fas fa-cogs me-1"></i>Assets
                    </a>
                    <a href="{{ url_for('inventory_management') }}" class="nav-link me-3">
                        <i class="fas fa-boxes me-1"></i>Inventory
                    </a>
                    <a href="{{ url_for('enhanced_workorders') }}" class="nav-link me-3">
                        <i class="fas fa-clipboard-list me-1"></i>Work Orders
                    </a>
                    <!-- More dropdown for additional items -->
                    <div class="dropdown">
                        <a class="nav-link me-3 dropdown-toggle" href="#" role="button" id="moreDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-ellipsis-h me-1"></i>More
                        </a>
                        <ul class="dropdown-menu" aria-labelledby="moreDropdown">
                            <li><a class="dropdown-item" href="{{ url_for('profile') }}">
                                <i class="fas fa-user me-2"></i>Profile
                            </a></li>
                            <li><a class="dropdown-item" href="{{ url_for('enhanced_profile') }}">
                                <i class="fas fa-rocket me-2"></i>Enhanced Profile
                            </a></li>
                            <li><a class="dropdown-item" href="{{ url_for('sync') }}">
                                <i class="fas fa-sync-alt me-2"></i>Sync
                            </a></li>
                            <li><a class="dropdown-item" href="{{ url_for('qr_inventory_scanner') }}">
                                <i class="fas fa-qrcode me-2"></i>QR Scanner - Inventory
                            </a></li>
                            <li><a class="dropdown-item" href="{{ url_for('asset_qr_scanner') }}">
                                <i class="fas fa-qrcode me-2"></i>QR Scanner - Assets
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="{{ url_for('logout') }}">
                                <i class="fas fa-sign-out-alt me-2"></i>Logout
                            </a></li>
                        </ul>
                    </div>
                </div>
                <!-- Theme Toggle -->
                <div class="form-check form-switch me-3">
                    <input class="form-check-input" type="checkbox" id="themeSwitch" title="Toggle Dark/Light Mode">
                    <label class="form-check-label text-white d-none d-md-inline" for="themeSwitch">
                        <i class="fas fa-moon me-1"></i>
                    </label>
                </div>
                <span class="user-info d-none d-md-inline-block">
                    <i class="fas fa-user-circle me-1"></i>{{ session['username'] }}
                </span>
            </div>
        </div>
    </header>

    <!-- Standardized Bottom Navigation for Mobile -->
    <nav class="mobile-nav d-md-none">
        <a href="{{ url_for('welcome') }}" class="nav-link {{ 'active' if request.endpoint == 'welcome' else '' }}">
            <i class="fas fa-home"></i>
            <span>Home</span>
        </a>
        <a href="{{ url_for('asset_management') }}" class="nav-link {{ 'active' if request.endpoint == 'asset_management' else '' }}">
            <i class="fas fa-cogs"></i>
            <span>Assets</span>
        </a>
        <a href="{{ url_for('inventory_management') }}" class="nav-link {{ 'active' if request.endpoint == 'inventory_management' else '' }}">
            <i class="fas fa-boxes"></i>
            <span>Inventory</span>
        </a>
        <a href="{{ url_for('enhanced_workorders') }}" class="nav-link {{ 'active' if request.endpoint == 'enhanced_workorders' else '' }}">
            <i class="fas fa-clipboard-list"></i>
            <span>Work Orders</span>
        </a>
        <div class="nav-link" style="cursor: pointer;" onclick="toggleMobileMore()">
            <i class="fas fa-ellipsis-h"></i>
            <span>More</span>
        </div>
    </nav>

    <!-- Mobile More Menu (Hidden by default) -->
    <div class="mobile-more-menu d-md-none" id="mobileMoreMenu" style="display: none;">
        <div class="more-menu-content">
            <div class="more-menu-header">
                <h6><i class="fas fa-ellipsis-h me-2"></i>More Options</h6>
                <button class="btn-close" onclick="toggleMobileMore()"></button>
            </div>
            <div class="more-menu-items">
                <a href="{{ url_for('welcome') }}" class="more-menu-item">
                    <i class="fas fa-home"></i>
                    <span>Home</span>
                </a>
                <a href="{{ url_for('profile') }}" class="more-menu-item">
                    <i class="fas fa-user"></i>
                    <span>Profile</span>
                </a>
                <a href="{{ url_for('enhanced_profile') }}" class="more-menu-item">
                    <i class="fas fa-rocket"></i>
                    <span>Enhanced Profile</span>
                </a>
                <a href="{{ url_for('sync') }}" class="more-menu-item">
                    <i class="fas fa-sync-alt"></i>
                    <span>Sync</span>
                </a>
                <a href="{{ url_for('qr_inventory_scanner') }}" class="more-menu-item">
                    <i class="fas fa-qrcode"></i>
                    <span>QR Scanner - Inventory</span>
                </a>
                <a href="{{ url_for('asset_qr_scanner') }}" class="more-menu-item">
                    <i class="fas fa-qrcode"></i>
                    <span>QR Scanner - Assets</span>
                </a>
                <div class="more-menu-item" style="cursor: pointer;" onclick="document.getElementById('themeSwitch').click()">
                    <i class="fas fa-moon"></i>
                    <span>Theme</span>
                </div>
                <a href="{{ url_for('logout') }}" class="more-menu-item">
                    <i class="fas fa-sign-out-alt"></i>
                    <span>Logout</span>
                </a>
            </div>
        </div>
    </div>
    {% endif %}

    <main class="app-content">
        <div class="container">
            {% with messages = get_flashed_messages(with_categories=true) %}
                {% if messages %}
                    {% for category, message in messages %}
                        <div class="alert alert-{{ category if category != 'error' else 'danger' }} alert-dismissible fade show">
                            {{ message }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    {% endfor %}
                {% endif %}
            {% endwith %}

            {% block content %}{% endblock %}
        </div>
    </main>

    <footer class="app-footer">
        <div class="container text-center">
            <p class="mb-0">Developed by Praba Krishna @2023</p>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="{{ url_for('static', filename='js/main.js') }}"></script>

    <!-- Mobile More Menu JavaScript -->
    <script>
    function toggleMobileMore() {
        const moreMenu = document.getElementById('mobileMoreMenu');
        if (moreMenu.style.display === 'none' || moreMenu.style.display === '') {
            moreMenu.style.display = 'block';
            document.body.style.overflow = 'hidden'; // Prevent background scrolling
        } else {
            moreMenu.style.display = 'none';
            document.body.style.overflow = ''; // Restore scrolling
        }
    }

    // Close more menu when clicking outside
    document.addEventListener('click', function(event) {
        const moreMenu = document.getElementById('mobileMoreMenu');
        const moreButton = event.target.closest('.nav-link');

        if (moreMenu && moreMenu.style.display === 'block' &&
            !moreMenu.contains(event.target) &&
            (!moreButton || !moreButton.textContent.includes('More'))) {
            toggleMobileMore();
        }
    });
    </script>

    {% block extra_js %}{% endblock %}
</body>
</html>
